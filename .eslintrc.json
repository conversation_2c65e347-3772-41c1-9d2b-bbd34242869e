{"extends": ["next/core-web-vitals", "@typescript-eslint/recommended", "@typescript-eslint/recommended-requiring-type-checking"], "plugins": ["@typescript-eslint"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json"}, "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "error", "@typescript-eslint/prefer-nullish-coalescing": "error", "@typescript-eslint/prefer-optional-chain": "error", "@typescript-eslint/no-non-null-assertion": "error", "@typescript-eslint/strict-boolean-expressions": "error", "@typescript-eslint/prefer-readonly": "error", "@typescript-eslint/no-floating-promises": "error", "@typescript-eslint/await-thenable": "error", "@typescript-eslint/no-misused-promises": "error", "import/no-unresolved": "off", "import/named": "off", "no-console": "error", "react-hooks/exhaustive-deps": "error", "prefer-const": "error", "no-var": "error", "object-shorthand": "error", "prefer-template": "error"}}