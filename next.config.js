/**
 * Professional Next.js Configuration
 * Advanced performance optimization, bundle analysis, and production-ready settings
 */

const { withSentryConfig } = require('@sentry/nextjs')

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable React strict mode for better development experience
  reactStrictMode: true,
  
  // Enable SWC minification for better performance
  swcMinify: true,
  
  // Experimental features for performance
  experimental: {
    // Enable app directory (if using App Router)
    appDir: false, // Set to true if using App Router
    
    // Optimize server components
    serverComponentsExternalPackages: ['@prisma/client', 'bcryptjs'],
    
    // Enable edge runtime for API routes where possible
    runtime: 'nodejs',
    
    // Optimize CSS
    optimizeCss: true,
    
    // Enable modern JavaScript features
    esmExternals: true,
    
    // Optimize images
    images: {
      allowFutureImage: true,
    },
  },

  // Image optimization configuration
  images: {
    // Enable image optimization
    unoptimized: false,
    
    // Supported formats
    formats: ['image/webp', 'image/avif'],
    
    // Device sizes for responsive images
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    
    // Image sizes for different breakpoints
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    
    // Domains allowed for external images
    domains: [
      'localhost',
      'images.unsplash.com',
      'via.placeholder.com',
    ],
    
    // Remote patterns for more flexible image sources
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
    ],
    
    // Minimize layout shift
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days
  },

  // Webpack configuration for advanced optimization
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Bundle analyzer (only in development or when ANALYZE=true)
    if (process.env.ANALYZE === 'true') {
      const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: 'static',
          openAnalyzer: false,
          reportFilename: isServer ? '../analyze/server.html' : './analyze/client.html',
        })
      )
    }

    // Optimize bundle splitting
    if (!isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          // Vendor chunk for stable dependencies
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: 10,
            reuseExistingChunk: true,
          },
          // Common chunk for shared code
          common: {
            name: 'common',
            minChunks: 2,
            priority: 5,
            reuseExistingChunk: true,
          },
          // UI library chunk
          ui: {
            test: /[\\/]node_modules[\\/](@radix-ui|@headlessui|framer-motion)[\\/]/,
            name: 'ui',
            priority: 15,
            reuseExistingChunk: true,
          },
          // React Query chunk
          reactQuery: {
            test: /[\\/]node_modules[\\/]@tanstack[\\/]/,
            name: 'react-query',
            priority: 15,
            reuseExistingChunk: true,
          },
        },
      }
    }

    // Tree shaking optimization
    config.optimization.usedExports = true
    config.optimization.sideEffects = false

    // Resolve aliases for cleaner imports
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': require('path').resolve(__dirname, 'src'),
      '~': require('path').resolve(__dirname, 'public'),
    }

    // Optimize for production
    if (!dev) {
      // Remove console.log in production
      config.optimization.minimizer[0].options.minimizer.options.compress.drop_console = true
      
      // Enable gzip compression
      const CompressionPlugin = require('compression-webpack-plugin')
      config.plugins.push(
        new CompressionPlugin({
          algorithm: 'gzip',
          test: /\.(js|css|html|svg)$/,
          threshold: 8192,
          minRatio: 0.8,
        })
      )
    }

    return config
  },

  // Environment variables
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // Headers for security and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          // Security headers
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          // Performance headers
          {
            key: 'X-Robots-Tag',
            value: 'index, follow',
          },
        ],
      },
      // Cache static assets
      {
        source: '/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      // Cache API responses
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=300, s-maxage=300', // 5 minutes
          },
        ],
      },
    ]
  },

  // Redirects for better SEO
  async redirects() {
    return [
      // Add any necessary redirects here
    ]
  },

  // Rewrites for API proxying or clean URLs
  async rewrites() {
    return [
      // Add any necessary rewrites here
    ]
  },

  // Compression
  compress: true,

  // Power by header
  poweredByHeader: false,

  // Generate ETags for better caching
  generateEtags: true,

  // HTTP keep alive
  httpAgentOptions: {
    keepAlive: true,
  },

  // Output configuration
  output: 'standalone', // For Docker deployment

  // TypeScript configuration
  typescript: {
    // Type checking is handled by separate process
    ignoreBuildErrors: false,
  },

  // ESLint configuration
  eslint: {
    // ESLint is handled by separate process
    ignoreDuringBuilds: false,
  },

  // Trailing slash configuration
  trailingSlash: false,

  // Page extensions
  pageExtensions: ['ts', 'tsx', 'js', 'jsx'],

  // Asset prefix for CDN
  assetPrefix: process.env.NODE_ENV === 'production' ? process.env.CDN_URL || '' : '',

  // Base path for deployment
  basePath: process.env.BASE_PATH || '',

  // Internationalization (if needed)
  // i18n: {
  //   locales: ['en', 'zh'],
  //   defaultLocale: 'zh',
  // },

  // Logging
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
}

// Sentry configuration (if using Sentry)
const sentryWebpackPluginOptions = {
  // Additional config options for the Sentry Webpack plugin
  silent: true,
  org: process.env.SENTRY_ORG,
  project: process.env.SENTRY_PROJECT,
  authToken: process.env.SENTRY_AUTH_TOKEN,
}

// Export configuration with or without Sentry
module.exports = process.env.NEXT_PUBLIC_SENTRY_DSN
  ? withSentryConfig(nextConfig, sentryWebpackPluginOptions)
  : nextConfig
