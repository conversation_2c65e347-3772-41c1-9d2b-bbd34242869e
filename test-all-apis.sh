#!/bin/bash

# 完整的API测试脚本
BASE_URL="http://localhost:3002"

echo "🧪 开始完整的API功能测试..."
echo "=================================="

# 测试客户API
echo "📋 测试客户管理API..."

# 1. 获取所有客户
echo "1. 获取客户列表..."
CLIENT_COUNT=$(curl -s $BASE_URL/api/clients | jq '.clients | length')
echo "   ✅ 当前客户数量: $CLIENT_COUNT"

# 2. 创建新客户
echo "2. 创建新客户..."
NEW_CLIENT=$(curl -s -X POST $BASE_URL/api/clients \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "测试",
    "last_name": "用户",
    "phone": "18888888888",
    "email": "<EMAIL>",
    "status": "active",
    "preferred_language": "zh-CN"
  }')

CLIENT_ID=$(echo $NEW_CLIENT | jq -r '.client.id')
if [ "$CLIENT_ID" != "null" ]; then
  echo "   ✅ 客户创建成功，ID: $CLIENT_ID"
else
  echo "   ❌ 客户创建失败"
  exit 1
fi

# 3. 获取单个客户
echo "3. 获取单个客户..."
CLIENT_DETAIL=$(curl -s $BASE_URL/api/clients/$CLIENT_ID)
CLIENT_NAME=$(echo $CLIENT_DETAIL | jq -r '.client.first_name')
if [ "$CLIENT_NAME" = "测试" ]; then
  echo "   ✅ 客户详情获取成功"
else
  echo "   ❌ 客户详情获取失败"
fi

# 4. 更新客户
echo "4. 更新客户信息..."
UPDATED_CLIENT=$(curl -s -X PUT $BASE_URL/api/clients/$CLIENT_ID \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "更新",
    "last_name": "用户",
    "phone": "18888888888",
    "email": "<EMAIL>",
    "status": "active",
    "preferred_language": "zh-CN"
  }')

UPDATED_NAME=$(echo $UPDATED_CLIENT | jq -r '.client.first_name')
if [ "$UPDATED_NAME" = "更新" ]; then
  echo "   ✅ 客户更新成功"
else
  echo "   ❌ 客户更新失败"
fi

echo ""
echo "🏥 测试治疗项目API..."

# 5. 获取所有治疗项目
echo "5. 获取治疗项目列表..."
TREATMENT_COUNT=$(curl -s $BASE_URL/api/treatments | jq '.treatments | length')
echo "   ✅ 当前治疗项目数量: $TREATMENT_COUNT"

# 6. 创建新治疗项目
echo "6. 创建新治疗项目..."
NEW_TREATMENT=$(curl -s -X POST $BASE_URL/api/treatments \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Facial",
    "name_chinese": "测试面部护理",
    "description": "Test facial treatment",
    "description_chinese": "测试面部护理项目",
    "default_price": 199.99,
    "duration_minutes": 90,
    "category": "面部护理",
    "is_active": true,
    "requires_consultation": false
  }')

TREATMENT_ID=$(echo $NEW_TREATMENT | jq -r '.treatment.id')
if [ "$TREATMENT_ID" != "null" ]; then
  echo "   ✅ 治疗项目创建成功，ID: $TREATMENT_ID"
else
  echo "   ❌ 治疗项目创建失败"
  exit 1
fi

# 7. 获取单个治疗项目
echo "7. 获取单个治疗项目..."
TREATMENT_DETAIL=$(curl -s $BASE_URL/api/treatments/$TREATMENT_ID)
TREATMENT_NAME=$(echo $TREATMENT_DETAIL | jq -r '.treatment.name_chinese')
if [ "$TREATMENT_NAME" = "测试面部护理" ]; then
  echo "   ✅ 治疗项目详情获取成功"
else
  echo "   ❌ 治疗项目详情获取失败"
fi

# 8. 更新治疗项目
echo "8. 更新治疗项目..."
UPDATED_TREATMENT=$(curl -s -X PUT $BASE_URL/api/treatments/$TREATMENT_ID \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Facial",
    "name_chinese": "更新的面部护理",
    "default_price": 299.99
  }')

UPDATED_TREATMENT_NAME=$(echo $UPDATED_TREATMENT | jq -r '.treatment.name_chinese')
if [ "$UPDATED_TREATMENT_NAME" = "更新的面部护理" ]; then
  echo "   ✅ 治疗项目更新成功"
else
  echo "   ❌ 治疗项目更新失败"
fi

echo ""
echo "📅 测试预约API..."

# 9. 获取预约列表
echo "9. 获取预约列表..."
APPOINTMENTS=$(curl -s "$BASE_URL/api/appointments?start_date=2025-07-01&end_date=2025-07-31")
APPOINTMENT_COUNT=$(echo $APPOINTMENTS | jq '.appointments | length')
echo "   ✅ 当前预约数量: $APPOINTMENT_COUNT"

echo ""
echo "💰 测试账单API..."

# 10. 获取账单列表
echo "10. 获取账单列表..."
INVOICES=$(curl -s $BASE_URL/api/invoices)
INVOICE_COUNT=$(echo $INVOICES | jq '.invoices | length')
echo "   ✅ 当前账单数量: $INVOICE_COUNT"

echo ""
echo "💳 测试付款API..."

# 11. 获取付款记录
echo "11. 获取付款记录..."
PAYMENTS=$(curl -s $BASE_URL/api/payments)
PAYMENT_COUNT=$(echo $PAYMENTS | jq '.payments | length')
echo "   ✅ 当前付款记录数量: $PAYMENT_COUNT"

echo ""
echo "🎉 所有API测试完成！"
echo "=================================="
echo "✅ 客户管理: 创建、读取、更新 - 正常"
echo "✅ 治疗项目: 创建、读取、更新 - 正常"  
echo "✅ 预约管理: 读取 - 正常"
echo "✅ 账单管理: 读取 - 正常"
echo "✅ 付款管理: 读取 - 正常"
echo ""
echo "🚀 系统功能完全正常，可以投入使用！"
