#!/bin/bash

# 测试脚本：固定定金和咨询费系统
# 执行日期：2025-07-13
# 目的：验证新的业务逻辑实现是否正确

echo "🧪 开始测试固定定金和咨询费系统..."
echo "=================================="

BASE_URL="http://localhost:3001"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试结果统计
TESTS_PASSED=0
TESTS_FAILED=0

# 测试函数
test_api() {
    local name="$1"
    local method="$2"
    local url="$3"
    local data="$4"
    local expected_status="$5"
    
    echo -n "测试: $name ... "
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" "$BASE_URL$url")
    else
        response=$(curl -s -w "%{http_code}" -X "$method" -H "Content-Type: application/json" -d "$data" "$BASE_URL$url")
    fi
    
    status_code="${response: -3}"
    body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ 通过${NC}"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        echo -e "${RED}❌ 失败 (状态码: $status_code)${NC}"
        echo "响应: $body"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# 1. 测试治疗项目API - 验证新字段
echo -e "\n${YELLOW}1. 测试治疗项目API（新字段支持）${NC}"
echo "----------------------------------------"

# 创建测试治疗项目
test_api "创建带固定定金的治疗项目" "POST" "/api/treatments" '{
    "name": "Test Facial Treatment",
    "name_chinese": "测试面部护理",
    "description": "Test treatment for fixed deposit",
    "description_chinese": "固定定金测试治疗",
    "default_price": 1000,
    "fixed_deposit_amount": 300,
    "consultation_fee": 100,
    "duration_minutes": 60,
    "category": "面部护理",
    "requires_consultation": true
}' "201"

# 获取治疗项目列表
test_api "获取治疗项目列表" "GET" "/api/treatments" "" "200"

# 2. 测试客户API
echo -e "\n${YELLOW}2. 测试客户管理API${NC}"
echo "----------------------------------------"

# 创建测试客户
test_api "创建测试客户" "POST" "/api/clients" '{
    "first_name": "测试",
    "last_name": "客户",
    "phone": "13800138000",
    "email": "<EMAIL>"
}' "201"

# 3. 测试预约API - 验证咨询费逻辑
echo -e "\n${YELLOW}3. 测试预约系统（咨询费逻辑）${NC}"
echo "----------------------------------------"

# 获取客户和治疗项目ID（需要从实际响应中提取）
echo "注意：以下测试需要实际的客户和治疗项目ID"

# 4. 测试账单API - 验证固定定金
echo -e "\n${YELLOW}4. 测试账单系统（固定定金）${NC}"
echo "----------------------------------------"

test_api "获取账单列表" "GET" "/api/invoices" "" "200"

# 5. 测试付款API
echo -e "\n${YELLOW}5. 测试付款系统${NC}"
echo "----------------------------------------"

test_api "获取付款记录" "GET" "/api/payments" "" "200"

# 6. 数据库完整性检查
echo -e "\n${YELLOW}6. 数据库完整性检查${NC}"
echo "----------------------------------------"

echo "检查数据库表结构..."

# 这里需要实际的数据库连接来验证表结构
echo "⚠️  需要手动验证以下数据库字段："
echo "   - treatments.fixed_deposit_amount"
echo "   - treatments.consultation_fee"
echo "   - invoices.consultation_fee_waived"
echo "   - invoices.original_consultation_fee"

# 7. 业务逻辑测试场景
echo -e "\n${YELLOW}7. 业务逻辑测试场景${NC}"
echo "----------------------------------------"

echo "📋 需要手动测试的业务场景："
echo ""
echo "场景1: 咨询预约 → 治疗预约 → 咨询费减免"
echo "  1. 创建咨询预约"
echo "  2. 验证咨询费账单生成"
echo "  3. 创建治疗预约"
echo "  4. 验证咨询费自动减免"
echo ""
echo "场景2: 同一天多项治疗 → 一个定金"
echo "  1. 同一天创建多个治疗预约"
echo "  2. 验证只收取最高定金金额"
echo ""
echo "场景3: 不同天治疗 → 分别收取定金"
echo "  1. 不同天创建治疗预约"
echo "  2. 验证每天单独收取定金"

# 8. 前端组件测试
echo -e "\n${YELLOW}8. 前端组件测试${NC}"
echo "----------------------------------------"

echo "🖥️  需要手动测试的前端功能："
echo ""
echo "TreatmentModal:"
echo "  ✓ 固定定金金额字段显示和编辑"
echo "  ✓ 咨询费字段显示和编辑"
echo "  ✓ 字段验证和保存"
echo ""
echo "InvoiceModal:"
echo "  ✓ 咨询费减免选项"
echo "  ✓ 原始咨询费字段"
echo "  ✓ 定金计算说明文本"
echo ""
echo "AppointmentModal:"
echo "  ✓ 费用信息显示"
echo "  ✓ 预约类型选择"
echo "  ✓ 定金和咨询费提示"

# 测试结果汇总
echo -e "\n${YELLOW}测试结果汇总${NC}"
echo "=================================="
echo -e "✅ 通过: ${GREEN}$TESTS_PASSED${NC}"
echo -e "❌ 失败: ${RED}$TESTS_FAILED${NC}"
echo -e "📊 总计: $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n🎉 ${GREEN}所有API测试通过！${NC}"
    echo "请继续进行手动业务逻辑测试"
else
    echo -e "\n⚠️  ${RED}发现 $TESTS_FAILED 个失败的测试${NC}"
    echo "请检查相关API实现"
fi

echo -e "\n📝 下一步："
echo "1. 开发服务器已启动: http://localhost:3001"
echo "2. 访问 http://localhost:3001/dashboard"
echo "3. 执行手动业务逻辑测试"
echo "4. 验证用户界面功能"
echo "5. 检查数据完整性"

echo -e "\n✨ 测试完成！"
