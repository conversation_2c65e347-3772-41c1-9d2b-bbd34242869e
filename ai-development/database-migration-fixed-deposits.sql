-- 数据库迁移脚本：固定定金和咨询费系统
-- 执行日期：2025-07-13
-- 目的：从百分比定金系统迁移到固定金额定金系统，并添加咨询费支持

-- ============================================================================
-- 第一步：为 treatments 表添加新字段
-- ============================================================================

-- 添加固定定金金额字段
ALTER TABLE treatments 
ADD COLUMN IF NOT EXISTS fixed_deposit_amount DECIMAL(10,2) DEFAULT 0.00;

-- 添加咨询费字段
ALTER TABLE treatments 
ADD COLUMN IF NOT EXISTS consultation_fee DECIMAL(10,2) DEFAULT 0.00;

-- 为新字段添加注释
COMMENT ON COLUMN treatments.fixed_deposit_amount IS '固定定金金额（替代百分比计算）';
COMMENT ON COLUMN treatments.consultation_fee IS '咨询费用';

-- ============================================================================
-- 第二步：为 invoices 表添加新字段
-- ============================================================================

-- 添加咨询费减免标记字段
ALTER TABLE invoices 
ADD COLUMN IF NOT EXISTS consultation_fee_waived BOOLEAN DEFAULT false;

-- 添加原始咨询费金额字段（用于记录减免前的金额）
ALTER TABLE invoices 
ADD COLUMN IF NOT EXISTS original_consultation_fee DECIMAL(10,2) DEFAULT 0.00;

-- 为新字段添加注释
COMMENT ON COLUMN invoices.consultation_fee_waived IS '咨询费是否已减免';
COMMENT ON COLUMN invoices.original_consultation_fee IS '原始咨询费金额（减免前）';

-- ============================================================================
-- 第三步：数据迁移 - 将现有百分比定金转换为固定金额
-- ============================================================================

-- 为现有治疗项目设置默认固定定金金额（基于默认价格的50%）
UPDATE treatments 
SET fixed_deposit_amount = ROUND(default_price * 0.50, 2)
WHERE fixed_deposit_amount = 0.00;

-- 为需要咨询的治疗项目设置默认咨询费（基于默认价格的20%）
UPDATE treatments 
SET consultation_fee = ROUND(default_price * 0.20, 2)
WHERE requires_consultation = true AND consultation_fee = 0.00;

-- ============================================================================
-- 第四步：更新索引以提高查询性能
-- ============================================================================

-- 为新字段创建索引
CREATE INDEX IF NOT EXISTS idx_treatments_fixed_deposit_amount ON treatments(fixed_deposit_amount);
CREATE INDEX IF NOT EXISTS idx_treatments_consultation_fee ON treatments(consultation_fee);
CREATE INDEX IF NOT EXISTS idx_invoices_consultation_fee_waived ON invoices(consultation_fee_waived);

-- ============================================================================
-- 第五步：验证数据完整性
-- ============================================================================

-- 检查是否所有治疗项目都有固定定金金额
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM treatments WHERE fixed_deposit_amount IS NULL OR fixed_deposit_amount < 0) THEN
        RAISE EXCEPTION '存在无效的固定定金金额';
    END IF;
    
    IF EXISTS (SELECT 1 FROM treatments WHERE consultation_fee IS NULL OR consultation_fee < 0) THEN
        RAISE EXCEPTION '存在无效的咨询费金额';
    END IF;
    
    RAISE NOTICE '数据迁移验证通过';
END $$;

-- ============================================================================
-- 第六步：创建辅助函数
-- ============================================================================

-- 创建函数：获取治疗项目的固定定金金额
CREATE OR REPLACE FUNCTION get_treatment_deposit_amount(treatment_id UUID)
RETURNS DECIMAL(10,2) AS $$
DECLARE
    deposit_amount DECIMAL(10,2);
BEGIN
    SELECT fixed_deposit_amount INTO deposit_amount
    FROM treatments
    WHERE id = treatment_id AND is_active = true;
    
    RETURN COALESCE(deposit_amount, 0.00);
END;
$$ LANGUAGE plpgsql;

-- 创建函数：获取治疗项目的咨询费
CREATE OR REPLACE FUNCTION get_treatment_consultation_fee(treatment_id UUID)
RETURNS DECIMAL(10,2) AS $$
DECLARE
    consult_fee DECIMAL(10,2);
BEGIN
    SELECT consultation_fee INTO consult_fee
    FROM treatments
    WHERE id = treatment_id AND is_active = true AND requires_consultation = true;
    
    RETURN COALESCE(consult_fee, 0.00);
END;
$$ LANGUAGE plpgsql;

-- 创建函数：计算账单的总定金金额（考虑一天一个定金规则）
CREATE OR REPLACE FUNCTION calculate_daily_deposit_amount(client_id_param UUID, treatment_date_param DATE)
RETURNS DECIMAL(10,2) AS $$
DECLARE
    total_deposit DECIMAL(10,2) := 0.00;
    max_deposit DECIMAL(10,2) := 0.00;
    current_deposit DECIMAL(10,2);
BEGIN
    -- 获取该客户在指定日期的所有预约的定金金额
    FOR current_deposit IN
        SELECT t.fixed_deposit_amount
        FROM appointments a
        JOIN treatments t ON a.treatment_id = t.id
        WHERE a.client_id = client_id_param 
        AND a.appointment_date = treatment_date_param
        AND a.status NOT IN ('cancelled', 'no_show')
    LOOP
        -- 找出最大的定金金额（一天只收一个定金，取最高的）
        IF current_deposit > max_deposit THEN
            max_deposit := current_deposit;
        END IF;
    END LOOP;
    
    RETURN max_deposit;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- 第七步：更新现有账单的定金计算方式
-- ============================================================================

-- 注意：这个步骤需要谨慎执行，建议在生产环境中先备份数据
-- 暂时注释掉，需要手动执行

/*
-- 更新现有账单，使用新的固定定金计算方式
UPDATE invoices 
SET deposit_amount = (
    SELECT calculate_daily_deposit_amount(invoices.client_id, invoices.treatment_date)
)
WHERE status IN ('draft', 'deposit_pending');
*/

-- ============================================================================
-- 完成迁移
-- ============================================================================

-- 记录迁移完成
INSERT INTO contact_logs (client_id, contact_type, contact_direction, subject, notes, staff_member)
SELECT 
    (SELECT id FROM clients LIMIT 1),
    'other',
    'outbound',
    '系统迁移完成',
    '固定定金和咨询费系统迁移于 ' || NOW() || ' 完成',
    'System'
WHERE EXISTS (SELECT 1 FROM clients LIMIT 1);

RAISE NOTICE '数据库迁移完成：固定定金和咨询费系统已启用';
