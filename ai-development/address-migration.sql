-- 地址字段改进迁移脚本
-- 将单一address字段改为标准的多字段地址结构

-- 1. 添加新的地址字段
ALTER TABLE clients 
ADD COLUMN address_line_1 VARCHAR(255),
ADD COLUMN address_line_2 VARCHAR(255),
ADD COLUMN city VARCHAR(100),
ADD COLUMN state_province VARCHAR(100),
ADD COLUMN postal_code VARCHAR(20),
ADD COLUMN country VARCHAR(100) DEFAULT '中国',
ADD COLUMN latitude DECIMAL(10,8),
ADD COLUMN longitude DECIMAL(11,8);

-- 2. 数据迁移：将现有的address数据迁移到address_line_1
-- 注意：这是一个简单的迁移，实际使用时可能需要更复杂的解析逻辑
UPDATE clients 
SET address_line_1 = address 
WHERE address IS NOT NULL AND address != '';

-- 3. 创建索引以提高查询性能
CREATE INDEX idx_clients_city ON clients(city);
CREATE INDEX idx_clients_state_province ON clients(state_province);
CREATE INDEX idx_clients_postal_code ON clients(postal_code);
CREATE INDEX idx_clients_location ON clients(latitude, longitude);

-- 4. 添加注释
COMMENT ON COLUMN clients.address_line_1 IS '地址第一行（街道地址）';
COMMENT ON COLUMN clients.address_line_2 IS '地址第二行（公寓号、楼层等）';
COMMENT ON COLUMN clients.city IS '城市';
COMMENT ON COLUMN clients.state_province IS '省/州';
COMMENT ON COLUMN clients.postal_code IS '邮政编码';
COMMENT ON COLUMN clients.country IS '国家';
COMMENT ON COLUMN clients.latitude IS '纬度（用于地图显示）';
COMMENT ON COLUMN clients.longitude IS '经度（用于地图显示）';

-- 5. 可选：在确认新字段工作正常后，删除旧的address字段
-- ALTER TABLE clients DROP COLUMN address;

-- 6. 更新RLS策略（如果使用）
-- 确保新字段也受到相同的安全策略保护
-- 这里假设已有的RLS策略会自动应用到新字段

-- 7. 创建地址格式化函数
CREATE OR REPLACE FUNCTION format_client_address(
    address_line_1 VARCHAR(255),
    address_line_2 VARCHAR(255),
    city VARCHAR(100),
    state_province VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100)
) RETURNS TEXT AS $$
BEGIN
    RETURN TRIM(
        COALESCE(address_line_1, '') ||
        CASE WHEN address_line_2 IS NOT NULL AND address_line_2 != '' 
             THEN ', ' || address_line_2 
             ELSE '' END ||
        CASE WHEN city IS NOT NULL AND city != '' 
             THEN ', ' || city 
             ELSE '' END ||
        CASE WHEN state_province IS NOT NULL AND state_province != '' 
             THEN ', ' || state_province 
             ELSE '' END ||
        CASE WHEN postal_code IS NOT NULL AND postal_code != '' 
             THEN ' ' || postal_code 
             ELSE '' END ||
        CASE WHEN country IS NOT NULL AND country != '' AND country != '中国'
             THEN ', ' || country 
             ELSE '' END
    );
END;
$$ LANGUAGE plpgsql;

-- 8. 创建地址搜索函数
CREATE OR REPLACE FUNCTION search_clients_by_address(search_term TEXT)
RETURNS TABLE(
    id UUID,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    formatted_address TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id,
        c.first_name,
        c.last_name,
        c.phone,
        format_client_address(
            c.address_line_1,
            c.address_line_2,
            c.city,
            c.state_province,
            c.postal_code,
            c.country
        ) as formatted_address
    FROM clients c
    WHERE 
        c.address_line_1 ILIKE '%' || search_term || '%' OR
        c.address_line_2 ILIKE '%' || search_term || '%' OR
        c.city ILIKE '%' || search_term || '%' OR
        c.state_province ILIKE '%' || search_term || '%' OR
        c.postal_code ILIKE '%' || search_term || '%'
    ORDER BY c.last_name, c.first_name;
END;
$$ LANGUAGE plpgsql;
