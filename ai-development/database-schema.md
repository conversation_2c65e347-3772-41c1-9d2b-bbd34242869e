# 数据库架构设计 - 医美诊所 CRM 系统

## 核心业务逻辑

- **单次定金规则**: 同一天多项治疗 = 一个定金
- **多次定金规则**: 不同天的治疗 = 每天单独定金
- **语言**: 所有界面文本必须为中文简体
- **主要用户**: 前台工作人员和医生

## 数据库表结构

### 1. clients (客户表)

存储客户/患者基本信息

```sql
- id: UUID (主键)
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
- first_name: VARCHAR(100) (名)
- last_name: VARCHAR(100) (姓)
- phone: VARCHAR(20) (电话，必填)
- email: VARCHAR(255) (邮箱，可选)
- date_of_birth: DATE (出生日期)
- address_line_1: VARCHAR(255) (地址第一行，如街道地址)
- address_line_2: VARCHAR(255) (地址第二行，如公寓号、楼层等，可选)
- city: VARCHAR(100) (城市)
- state_province: VARCHAR(100) (省/州)
- postal_code: VARCHAR(20) (邮政编码)
- country: VARCHAR(100) DEFAULT '中国' (国家)
- latitude: DECIMAL(10,8) (纬度，用于地图显示)
- longitude: DECIMAL(11,8) (经度，用于地图显示)
- emergency_contact_name: VARCHAR(200) (紧急联系人姓名)
- emergency_contact_phone: VARCHAR(20) (紧急联系人电话)
- notes: TEXT (备注)
- status: ENUM('active', 'inactive', 'archived') (状态)
- preferred_language: VARCHAR(10) DEFAULT 'zh-CN' (首选语言)
- referral_source: VARCHAR(100) (推荐来源)
```

### 2. treatments (治疗项目表)

存储可用的治疗服务和定价

```sql
- id: UUID (主键)
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
- name: VARCHAR(200) (英文名称)
- name_chinese: VARCHAR(200) (中文名称)
- description: TEXT (英文描述)
- description_chinese: TEXT (中文描述)
- default_price: DECIMAL(10,2) (默认价格)
- duration_minutes: INTEGER (治疗时长，分钟)
- category: VARCHAR(100) (治疗类别)
- is_active: BOOLEAN DEFAULT true (是否启用)
- requires_consultation: BOOLEAN DEFAULT false (是否需要咨询)
```

### 3. appointments (预约表)

存储预约信息

```sql
- id: UUID (主键)
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
- client_id: UUID (客户ID，外键)
- treatment_id: UUID (治疗项目ID，外键)
- appointment_date: DATE (预约日期)
- start_time: TIME (开始时间)
- end_time: TIME (结束时间)
- status: ENUM('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show')
- appointment_type: ENUM('consultation', 'treatment', 'follow_up')
- notes: TEXT (客户备注)
- staff_notes: TEXT (工作人员备注)
- custom_price: DECIMAL(10,2) (自定义价格，覆盖默认价格)
```

### 4. invoices (账单表)

存储账单信息，支持定金计算业务逻辑

```sql
- id: UUID (主键)
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
- client_id: UUID (客户ID，外键)
- invoice_number: VARCHAR(50) (账单编号，唯一)
- invoice_date: DATE (账单日期)
- treatment_date: DATE (治疗日期，用于定金分组)
- total_amount: DECIMAL(10,2) (总金额)
- deposit_amount: DECIMAL(10,2) (定金金额)
- deposit_percentage: DECIMAL(5,2) DEFAULT 50.00 (定金百分比)
- status: ENUM('draft', 'deposit_pending', 'deposit_paid', 'paid_in_full', 'overdue', 'cancelled')
- due_date: DATE (到期日期)
- notes: TEXT (备注)
```

### 5. invoice_items (账单项目表)

存储账单中的具体治疗项目

```sql
- id: UUID (主键)
- created_at: TIMESTAMP
- invoice_id: UUID (账单ID，外键)
- appointment_id: UUID (预约ID，外键)
- treatment_name: VARCHAR(200) (治疗名称快照)
- treatment_name_chinese: VARCHAR(200) (中文治疗名称快照)
- quantity: INTEGER DEFAULT 1 (数量)
- unit_price: DECIMAL(10,2) (单价)
- total_price: DECIMAL(10,2) (总价)
```

### 6. payments (付款表)

存储付款记录

```sql
- id: UUID (主键)
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
- invoice_id: UUID (账单ID，外键)
- client_id: UUID (客户ID，外键)
- amount: DECIMAL(10,2) (付款金额)
- payment_date: DATE (付款日期)
- payment_method: ENUM('cash', 'credit_card', 'debit_card', 'bank_transfer', 'check', 'other')
- payment_type: ENUM('deposit', 'full_payment', 'partial_payment')
- reference_number: VARCHAR(100) (参考号码)
- notes: TEXT (备注)
```

### 7. contact_logs (联系记录表)

存储客户互动历史

```sql
- id: UUID (主键)
- created_at: TIMESTAMP
- client_id: UUID (客户ID，外键)
- contact_type: ENUM('phone_call', 'email', 'in_person', 'sms', 'other')
- contact_direction: ENUM('inbound', 'outbound')
- subject: VARCHAR(200) (主题)
- notes: TEXT (详细记录)
- staff_member: VARCHAR(100) (工作人员)
- follow_up_required: BOOLEAN DEFAULT false (需要跟进)
- follow_up_date: DATE (跟进日期)
```

## 关键关系和约束

### 外键关系

- appointments.client_id → clients.id
- appointments.treatment_id → treatments.id
- invoices.client_id → clients.id
- invoice_items.invoice_id → invoices.id
- invoice_items.appointment_id → appointments.id
- payments.invoice_id → invoices.id
- payments.client_id → clients.id
- contact_logs.client_id → clients.id

### 索引策略

- clients: phone (唯一), email, status
- appointments: client_id, appointment_date, status
- invoices: client_id, invoice_number (唯一), treatment_date, status
- payments: invoice_id, client_id, payment_date
- contact_logs: client_id, created_at

### 业务逻辑实现

#### 定金计算规则

1. **同日多项治疗**:

   - 按 treatment_date 分组
   - 每个日期创建一个 invoice
   - 包含该日期的所有 appointments

2. **不同日期治疗**:
   - 每个 treatment_date 单独创建 invoice
   - 分别计算定金

#### 状态流转

- **预约状态**: scheduled → confirmed → in_progress → completed
- **账单状态**: draft → deposit_pending → deposit_paid → paid_in_full
- **付款类型**: deposit → partial_payment → full_payment

## RLS (行级安全) 策略

- 所有表基于用户认证状态限制访问
- 客户数据仅限授权工作人员访问
- 支持多租户架构（如需要）

## 实时功能

- 预约状态变更实时同步
- 新预约创建实时通知
- 付款状态更新实时反映
