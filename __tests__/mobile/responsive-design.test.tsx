/**
 * Mobile Responsive Design Tests
 * Comprehensive testing of mobile responsiveness, touch interactions, and PWA features
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { act } from 'react-dom/test-utils'
import '@testing-library/jest-dom'
import { MobileLayout } from '../../src/components/layout/MobileLayout'
import { 
  SwipeableCard, 
  PullToRefresh, 
  BottomSheet, 
  TouchButtonGroup,
  ExpandableCard,
  TouchCheckbox 
} from '../../src/components/mobile/TouchOptimizedComponents'

// Mock framer-motion for testing
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  useAnimation: () => ({
    start: jest.fn(),
  }),
  PanInfo: {},
}))

// Mock Next.js router
jest.mock('next/navigation', () => ({
  usePathname: () => '/clients',
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
  }),
}))

describe('Mobile Responsive Design', () => {
  // Mock window dimensions
  const mockWindowDimensions = (width: number, height: number) => {
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: width,
    })
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: height,
    })
    
    // Trigger resize event
    act(() => {
      window.dispatchEvent(new Event('resize'))
    })
  }

  beforeEach(() => {
    // Reset to mobile dimensions
    mockWindowDimensions(375, 667) // iPhone SE dimensions
  })

  describe('MobileLayout Component', () => {
    it('should render mobile layout correctly', () => {
      render(
        <MobileLayout title="测试页面">
          <div>测试内容</div>
        </MobileLayout>
      )

      expect(screen.getByText('测试页面')).toBeInTheDocument()
      expect(screen.getByText('测试内容')).toBeInTheDocument()
      
      // Should show mobile header
      expect(screen.getByRole('button', { name: /menu/i })).toBeInTheDocument()
    })

    it('should show mobile navigation on small screens', () => {
      render(
        <MobileLayout>
          <div>内容</div>
        </MobileLayout>
      )

      // Mobile navigation should be visible
      const mobileNav = screen.getByRole('navigation')
      expect(mobileNav).toBeInTheDocument()
      
      // Should contain navigation items
      expect(screen.getByText('首页')).toBeInTheDocument()
      expect(screen.getByText('客户')).toBeInTheDocument()
      expect(screen.getByText('预约')).toBeInTheDocument()
    })

    it('should open mobile menu when menu button is clicked', async () => {
      render(
        <MobileLayout>
          <div>内容</div>
        </MobileLayout>
      )

      const menuButton = screen.getByRole('button', { name: /menu/i })
      fireEvent.click(menuButton)

      await waitFor(() => {
        expect(screen.getByText('管理员')).toBeInTheDocument()
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      })
    })

    it('should adapt to desktop layout on large screens', () => {
      // Change to desktop dimensions
      mockWindowDimensions(1280, 720)

      render(
        <MobileLayout>
          <div>内容</div>
        </MobileLayout>
      )

      // Desktop sidebar should be visible
      expect(screen.getByText('医美诊所')).toBeInTheDocument()
      
      // Mobile menu button should not be visible on desktop
      expect(screen.queryByRole('button', { name: /menu/i })).not.toBeInTheDocument()
    })

    it('should handle back button functionality', () => {
      const mockOnBack = jest.fn()
      
      render(
        <MobileLayout showBackButton onBackClick={mockOnBack}>
          <div>内容</div>
        </MobileLayout>
      )

      const backButton = screen.getByRole('button')
      fireEvent.click(backButton)

      expect(mockOnBack).toHaveBeenCalled()
    })
  })

  describe('Touch-Optimized Components', () => {
    describe('SwipeableCard', () => {
      it('should render swipeable card with actions', () => {
        const mockSwipeLeft = jest.fn()
        const mockSwipeRight = jest.fn()

        render(
          <SwipeableCard
            onSwipeLeft={mockSwipeLeft}
            onSwipeRight={mockSwipeRight}
            leftAction={{
              icon: <span>✓</span>,
              color: 'bg-green-500',
              label: '完成',
            }}
            rightAction={{
              icon: <span>✗</span>,
              color: 'bg-red-500',
              label: '删除',
            }}
          >
            <div>卡片内容</div>
          </SwipeableCard>
        )

        expect(screen.getByText('卡片内容')).toBeInTheDocument()
        expect(screen.getByText('完成')).toBeInTheDocument()
        expect(screen.getByText('删除')).toBeInTheDocument()
      })
    })

    describe('PullToRefresh', () => {
      it('should render pull to refresh component', () => {
        const mockRefresh = jest.fn().mockResolvedValue(undefined)

        render(
          <PullToRefresh onRefresh={mockRefresh}>
            <div>可刷新内容</div>
          </PullToRefresh>
        )

        expect(screen.getByText('可刷新内容')).toBeInTheDocument()
      })

      it('should handle touch events for pull to refresh', async () => {
        const mockRefresh = jest.fn().mockResolvedValue(undefined)

        render(
          <PullToRefresh onRefresh={mockRefresh}>
            <div>可刷新内容</div>
          </PullToRefresh>
        )

        const container = screen.getByText('可刷新内容').parentElement

        // Simulate touch start
        fireEvent.touchStart(container!, {
          touches: [{ clientY: 100 }],
        })

        // Simulate touch move (pull down)
        fireEvent.touchMove(container!, {
          touches: [{ clientY: 200 }],
        })

        // Simulate touch end
        fireEvent.touchEnd(container!)

        // Should trigger refresh if pulled enough
        await waitFor(() => {
          expect(mockRefresh).toHaveBeenCalled()
        })
      })
    })

    describe('BottomSheet', () => {
      it('should render bottom sheet when open', () => {
        const mockOnClose = jest.fn()

        render(
          <BottomSheet isOpen={true} onClose={mockOnClose} title="测试底部表单">
            <div>底部表单内容</div>
          </BottomSheet>
        )

        expect(screen.getByText('测试底部表单')).toBeInTheDocument()
        expect(screen.getByText('底部表单内容')).toBeInTheDocument()
      })

      it('should not render when closed', () => {
        const mockOnClose = jest.fn()

        render(
          <BottomSheet isOpen={false} onClose={mockOnClose}>
            <div>底部表单内容</div>
          </BottomSheet>
        )

        expect(screen.queryByText('底部表单内容')).not.toBeInTheDocument()
      })

      it('should call onClose when backdrop is clicked', () => {
        const mockOnClose = jest.fn()

        render(
          <BottomSheet isOpen={true} onClose={mockOnClose}>
            <div>内容</div>
          </BottomSheet>
        )

        // Click backdrop
        const backdrop = document.querySelector('.bg-black.bg-opacity-50')
        fireEvent.click(backdrop!)

        expect(mockOnClose).toHaveBeenCalled()
      })
    })

    describe('TouchButtonGroup', () => {
      const mockButtons = [
        { label: '选项1', value: 'option1' },
        { label: '选项2', value: 'option2' },
        { label: '选项3', value: 'option3', disabled: true },
      ]

      it('should render button group with correct selection', () => {
        const mockOnChange = jest.fn()

        render(
          <TouchButtonGroup
            buttons={mockButtons}
            value="option1"
            onChange={mockOnChange}
          />
        )

        expect(screen.getByText('选项1')).toBeInTheDocument()
        expect(screen.getByText('选项2')).toBeInTheDocument()
        expect(screen.getByText('选项3')).toBeInTheDocument()
      })

      it('should handle button selection', () => {
        const mockOnChange = jest.fn()

        render(
          <TouchButtonGroup
            buttons={mockButtons}
            value="option1"
            onChange={mockOnChange}
          />
        )

        fireEvent.click(screen.getByText('选项2'))
        expect(mockOnChange).toHaveBeenCalledWith('option2')
      })

      it('should disable buttons when specified', () => {
        const mockOnChange = jest.fn()

        render(
          <TouchButtonGroup
            buttons={mockButtons}
            value="option1"
            onChange={mockOnChange}
          />
        )

        const disabledButton = screen.getByText('选项3').closest('button')
        expect(disabledButton).toBeDisabled()
      })
    })

    describe('ExpandableCard', () => {
      it('should render expandable card', () => {
        render(
          <ExpandableCard title="可展开卡片">
            <div>卡片内容</div>
          </ExpandableCard>
        )

        expect(screen.getByText('可展开卡片')).toBeInTheDocument()
      })

      it('should expand and collapse when clicked', () => {
        render(
          <ExpandableCard title="可展开卡片">
            <div>卡片内容</div>
          </ExpandableCard>
        )

        const titleButton = screen.getByText('可展开卡片')
        
        // Initially collapsed, content should not be visible
        expect(screen.queryByText('卡片内容')).not.toBeInTheDocument()

        // Click to expand
        fireEvent.click(titleButton)
        expect(screen.getByText('卡片内容')).toBeInTheDocument()

        // Click to collapse
        fireEvent.click(titleButton)
        // Content should be hidden again (may still be in DOM but not visible)
      })

      it('should start expanded when defaultExpanded is true', () => {
        render(
          <ExpandableCard title="可展开卡片" defaultExpanded={true}>
            <div>卡片内容</div>
          </ExpandableCard>
        )

        expect(screen.getByText('卡片内容')).toBeInTheDocument()
      })
    })

    describe('TouchCheckbox', () => {
      it('should render checkbox with label', () => {
        const mockOnChange = jest.fn()

        render(
          <TouchCheckbox
            checked={false}
            onChange={mockOnChange}
            label="测试复选框"
            description="这是一个测试复选框"
          />
        )

        expect(screen.getByText('测试复选框')).toBeInTheDocument()
        expect(screen.getByText('这是一个测试复选框')).toBeInTheDocument()
      })

      it('should handle checkbox state changes', () => {
        const mockOnChange = jest.fn()

        render(
          <TouchCheckbox
            checked={false}
            onChange={mockOnChange}
            label="测试复选框"
          />
        )

        const checkbox = screen.getByText('测试复选框').closest('button')
        fireEvent.click(checkbox!)

        expect(mockOnChange).toHaveBeenCalledWith(true)
      })

      it('should be disabled when specified', () => {
        const mockOnChange = jest.fn()

        render(
          <TouchCheckbox
            checked={false}
            onChange={mockOnChange}
            label="禁用复选框"
            disabled={true}
          />
        )

        const checkbox = screen.getByText('禁用复选框').closest('button')
        expect(checkbox).toBeDisabled()

        fireEvent.click(checkbox!)
        expect(mockOnChange).not.toHaveBeenCalled()
      })
    })
  })

  describe('Responsive Breakpoints', () => {
    it('should adapt layout for mobile screens (< 768px)', () => {
      mockWindowDimensions(375, 667)

      render(
        <div className="grid-responsive md:grid-cols-2 lg:grid-cols-4">
          <div>项目1</div>
          <div>项目2</div>
          <div>项目3</div>
          <div>项目4</div>
        </div>
      )

      const container = screen.getByText('项目1').parentElement
      expect(container).toHaveClass('grid-responsive')
    })

    it('should adapt layout for tablet screens (768px - 1024px)', () => {
      mockWindowDimensions(768, 1024)

      render(
        <div className="grid-responsive md:grid-cols-2 lg:grid-cols-4">
          <div>项目1</div>
          <div>项目2</div>
        </div>
      )

      // Should apply tablet styles
      const container = screen.getByText('项目1').parentElement
      expect(container).toHaveClass('grid-responsive')
    })

    it('should adapt layout for desktop screens (> 1024px)', () => {
      mockWindowDimensions(1280, 720)

      render(
        <div className="grid-responsive md:grid-cols-2 lg:grid-cols-4">
          <div>项目1</div>
          <div>项目2</div>
          <div>项目3</div>
          <div>项目4</div>
        </div>
      )

      // Should apply desktop styles
      const container = screen.getByText('项目1').parentElement
      expect(container).toHaveClass('grid-responsive')
    })
  })

  describe('Touch Target Accessibility', () => {
    it('should have minimum touch target size for buttons', () => {
      render(
        <button className="btn-touch">
          触摸按钮
        </button>
      )

      const button = screen.getByText('触摸按钮')
      const styles = window.getComputedStyle(button)
      
      // Should have minimum 44px touch target (as defined in CSS variables)
      expect(button).toHaveClass('btn-touch')
    })

    it('should have appropriate spacing for touch interactions', () => {
      render(
        <div className="space-y-2">
          <button className="btn-touch">按钮1</button>
          <button className="btn-touch">按钮2</button>
        </div>
      )

      const buttons = screen.getAllByRole('button')
      expect(buttons).toHaveLength(2)
      
      // Buttons should have touch-friendly class
      buttons.forEach(button => {
        expect(button).toHaveClass('btn-touch')
      })
    })
  })

  describe('Performance Considerations', () => {
    it('should handle rapid touch interactions without lag', async () => {
      const mockOnChange = jest.fn()

      render(
        <TouchButtonGroup
          buttons={[
            { label: '快速1', value: 'fast1' },
            { label: '快速2', value: 'fast2' },
          ]}
          value="fast1"
          onChange={mockOnChange}
        />
      )

      // Simulate rapid clicks
      const button2 = screen.getByText('快速2')
      
      for (let i = 0; i < 5; i++) {
        fireEvent.click(button2)
        await new Promise(resolve => setTimeout(resolve, 10))
      }

      // Should handle all clicks
      expect(mockOnChange).toHaveBeenCalledTimes(5)
    })
  })
})
