import { businessLogic } from '@/lib/supabase/queries'
import { supabase } from '@/lib/supabase/client'

// Mock Supabase client
jest.mock('@/lib/supabase/client')
const mockSupabase = supabase as jest.Mocked<typeof supabase>

describe('Deposit Calculation Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Same Day Multiple Appointments - One Deposit Rule', () => {
    it('should calculate single deposit for multiple same-day appointments', async () => {
      const sameDay = '2025-07-15'
      const appointments = [
        { 
          id: 'apt-1', 
          treatment_id: 'treatment-1', 
          appointment_date: sameDay,
          custom_price: 200 
        },
        { 
          id: 'apt-2', 
          treatment_id: 'treatment-2', 
          appointment_date: sameDay,
          custom_price: 300 
        },
        { 
          id: 'apt-3', 
          treatment_id: 'treatment-3', 
          appointment_date: sameDay,
          custom_price: 150 
        },
      ]

      // Mock treatment responses with different deposit amounts
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn()
          .mockResolvedValueOnce({ data: { fixed_deposit_amount: 80 }, error: null })
          .mockResolvedValueOnce({ data: { fixed_deposit_amount: 120 }, error: null })
          .mockResolvedValueOnce({ data: { fixed_deposit_amount: 60 }, error: null }),
      } as any)

      const depositAmount = await businessLogic.calculateFixedDeposit(appointments as any)

      expect(depositAmount).toBe(120) // Should return the highest deposit amount
      expect(mockSupabase.from).toHaveBeenCalledTimes(3)
      expect(mockSupabase.from).toHaveBeenCalledWith('treatments')
    })

    it('should generate single invoice for same-day appointments', async () => {
      const sameDay = '2025-07-15'
      const appointments = [
        { 
          id: 'apt-1', 
          client_id: 'client-1',
          treatment_id: 'treatment-1', 
          appointment_date: sameDay,
          custom_price: 200 
        },
        { 
          id: 'apt-2', 
          client_id: 'client-1',
          treatment_id: 'treatment-2', 
          appointment_date: sameDay,
          custom_price: 300 
        },
      ]

      // Mock the calculateFixedDeposit function
      jest.spyOn(businessLogic, 'calculateFixedDeposit').mockResolvedValue(150)
      jest.spyOn(businessLogic, 'calculateConsultationFees').mockResolvedValue(0)
      jest.spyOn(businessLogic, 'shouldWaiveConsultationFee').mockResolvedValue(false)
      jest.spyOn(businessLogic, 'generateInvoiceNumber').mockReturnValue('INV-20250715-0001')

      const invoices = await businessLogic.generateInvoiceFromAppointments(appointments as any)

      expect(invoices).toHaveLength(1) // Only one invoice for same day
      expect(invoices[0].total_amount).toBe(500) // 200 + 300
      expect(invoices[0].deposit_amount).toBe(150) // Fixed deposit amount
      expect(invoices[0].treatment_date).toBe(sameDay)
    })
  })

  describe('Different Days Multiple Appointments - Separate Deposits', () => {
    it('should calculate separate deposits for different-day appointments', async () => {
      const appointments = [
        { 
          id: 'apt-1', 
          client_id: 'client-1',
          treatment_id: 'treatment-1', 
          appointment_date: '2025-07-15',
          custom_price: 200 
        },
        { 
          id: 'apt-2', 
          client_id: 'client-1',
          treatment_id: 'treatment-2', 
          appointment_date: '2025-07-16',
          custom_price: 300 
        },
        { 
          id: 'apt-3', 
          client_id: 'client-1',
          treatment_id: 'treatment-3', 
          appointment_date: '2025-07-17',
          custom_price: 150 
        },
      ]

      // Mock functions
      jest.spyOn(businessLogic, 'calculateFixedDeposit')
        .mockResolvedValueOnce(80)  // Day 1
        .mockResolvedValueOnce(120) // Day 2
        .mockResolvedValueOnce(60)  // Day 3
      jest.spyOn(businessLogic, 'calculateConsultationFees').mockResolvedValue(0)
      jest.spyOn(businessLogic, 'shouldWaiveConsultationFee').mockResolvedValue(false)
      jest.spyOn(businessLogic, 'generateInvoiceNumber')
        .mockReturnValueOnce('INV-20250715-0001')
        .mockReturnValueOnce('INV-20250716-0002')
        .mockReturnValueOnce('INV-20250717-0003')

      const invoices = await businessLogic.generateInvoiceFromAppointments(appointments as any)

      expect(invoices).toHaveLength(3) // Three separate invoices
      expect(invoices[0].deposit_amount).toBe(80)
      expect(invoices[1].deposit_amount).toBe(120)
      expect(invoices[2].deposit_amount).toBe(60)
      expect(invoices[0].treatment_date).toBe('2025-07-15')
      expect(invoices[1].treatment_date).toBe('2025-07-16')
      expect(invoices[2].treatment_date).toBe('2025-07-17')
    })
  })

  describe('Consultation Fee Logic', () => {
    it('should calculate consultation fees correctly', async () => {
      const appointments = [
        { 
          id: 'apt-1', 
          treatment_id: 'treatment-1', 
          appointment_type: 'consultation' 
        },
        { 
          id: 'apt-2', 
          treatment_id: 'treatment-2', 
          appointment_type: 'treatment' 
        },
      ]

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn()
          .mockResolvedValueOnce({ 
            data: { consultation_fee: 75, requires_consultation: true }, 
            error: null 
          })
          .mockResolvedValueOnce({ 
            data: { consultation_fee: 50, requires_consultation: false }, 
            error: null 
          }),
      } as any)

      const consultationFee = await businessLogic.calculateConsultationFees(appointments as any)

      expect(consultationFee).toBe(75) // Only the first appointment should count
    })

    it('should waive consultation fee when client books treatment', async () => {
      const clientId = 'client-1'
      const treatmentDate = '2025-07-15'

      // Mock recent consultation invoice
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        gt: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: [{
            id: 'invoice-1',
            consultation_fee_waived: false,
            original_consultation_fee: 75,
            notes: '咨询费账单'
          }],
          error: null
        }),
      } as any)

      // Mock invoice update
      const mockInvoiceQueries = {
        update: jest.fn().mockResolvedValue({ id: 'invoice-1' })
      }
      jest.doMock('@/lib/supabase/queries', () => ({
        ...jest.requireActual('@/lib/supabase/queries'),
        invoiceQueries: mockInvoiceQueries
      }))

      const result = await businessLogic.processConsultationFeeWaiver(clientId, treatmentDate)

      expect(result).toBeTruthy()
      expect(mockInvoiceQueries.update).toHaveBeenCalledWith('invoice-1', {
        consultation_fee_waived: true,
        notes: '咨询费账单 [咨询费已于2025-07-15减免]'
      })
    })
  })

  describe('Invoice Status Updates', () => {
    it('should update invoice status based on payment amount', async () => {
      const invoiceId = 'invoice-1'
      const mockInvoice = {
        id: invoiceId,
        total_amount: 500,
        deposit_amount: 150,
        status: 'deposit_pending'
      }

      // Mock invoice and payment queries
      const mockInvoiceQueries = {
        getById: jest.fn().mockResolvedValue(mockInvoice),
        update: jest.fn().mockResolvedValue(mockInvoice)
      }
      const mockPaymentQueries = {
        getByInvoiceId: jest.fn()
      }

      jest.doMock('@/lib/supabase/queries', () => ({
        ...jest.requireActual('@/lib/supabase/queries'),
        invoiceQueries: mockInvoiceQueries,
        paymentQueries: mockPaymentQueries
      }))

      // Test deposit paid scenario
      mockPaymentQueries.getByInvoiceId.mockResolvedValue([
        { amount: 150 } // Deposit amount paid
      ])

      let newStatus = await businessLogic.updateInvoiceStatus(invoiceId)
      expect(newStatus).toBe('deposit_paid')

      // Test full payment scenario
      mockPaymentQueries.getByInvoiceId.mockResolvedValue([
        { amount: 150 }, // Deposit
        { amount: 350 }  // Remaining balance
      ])

      newStatus = await businessLogic.updateInvoiceStatus(invoiceId)
      expect(newStatus).toBe('paid_in_full')

      // Test partial payment scenario
      mockPaymentQueries.getByInvoiceId.mockResolvedValue([
        { amount: 100 } // Less than deposit
      ])

      newStatus = await businessLogic.updateInvoiceStatus(invoiceId)
      expect(newStatus).toBe('deposit_pending')
    })
  })

  describe('Edge Cases', () => {
    it('should handle zero deposit amounts', async () => {
      const appointments = [
        { id: 'apt-1', treatment_id: 'treatment-1', appointment_date: '2025-07-15' }
      ]

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ 
          data: { fixed_deposit_amount: 0 }, 
          error: null 
        }),
      } as any)

      const depositAmount = await businessLogic.calculateFixedDeposit(appointments as any)
      expect(depositAmount).toBe(0)
    })

    it('should handle missing treatment data', async () => {
      const appointments = [
        { id: 'apt-1', treatment_id: 'non-existent', appointment_date: '2025-07-15' }
      ]

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ 
          data: null, 
          error: { code: 'PGRST116' } 
        }),
      } as any)

      const depositAmount = await businessLogic.calculateFixedDeposit(appointments as any)
      expect(depositAmount).toBe(0)
    })

    it('should handle empty appointments array', async () => {
      const depositAmount = await businessLogic.calculateFixedDeposit([])
      expect(depositAmount).toBe(0)
    })

    it('should handle database connection errors gracefully', async () => {
      const appointments = [
        { id: 'apt-1', treatment_id: 'treatment-1', appointment_date: '2025-07-15' }
      ]

      mockSupabase.from.mockImplementation(() => {
        throw new Error('Database connection failed')
      })

      await expect(businessLogic.calculateFixedDeposit(appointments as any))
        .rejects.toThrow('Database connection failed')
    })
  })

  describe('Backward Compatibility', () => {
    it('should still support percentage-based deposits', () => {
      expect(businessLogic.calculateDeposit(200, 50)).toBe(100)
      expect(businessLogic.calculateDeposit(300, 30)).toBe(90)
      expect(businessLogic.calculateDeposit(150, 25)).toBe(37.5)
    })

    it('should use default 50% if no percentage provided', () => {
      expect(businessLogic.calculateDeposit(200)).toBe(100)
      expect(businessLogic.calculateDeposit(100)).toBe(50)
    })
  })
})
