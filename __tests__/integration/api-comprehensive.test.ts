/**
 * Comprehensive Integration Tests for Enhanced API System
 * Tests all API endpoints with professional validation, error handling, and security
 */

import { NextRequest } from 'next/server'
import { createMockRequest, testDataFactory, testSetup, customMatchers } from '../../src/lib/test-utils'
import { GET as clientsGET, POST as clientsPOST } from '../../src/app/api/clients/route'

// Extend Jest matchers
expect.extend(customMatchers)

// Mock external dependencies
jest.mock('@/lib/supabase/client')
jest.mock('@/lib/logger')

describe('Enhanced API System - Comprehensive Integration Tests', () => {
  beforeAll(testSetup.beforeAll)
  afterAll(testSetup.afterAll)
  beforeEach(testSetup.beforeEach)
  afterEach(testSetup.afterEach)

  describe('API Response Structure Validation', () => {
    it('should return standardized success response structure', async () => {
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost:3000/api/clients',
      })

      const response = await clientsGET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toBeValidApiResponse()
      expect(data).toHaveProperty('success', true)
      expect(data).toHaveProperty('data')
      expect(data).toHaveProperty('timestamp')
      expect(new Date(data.timestamp)).toBeInstanceOf(Date)
    })

    it('should return standardized error response structure', async () => {
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: { invalid: 'data' }, // Invalid data to trigger validation error
      })

      const response = await clientsPOST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data).toHaveProperty('error')
      expect(data.error).toHaveProperty('code')
      expect(data.error).toHaveProperty('message')
      expect(data.error).toHaveProperty('timestamp')
      expect(data.error).toHaveProperty('details')
    })

    it('should include request ID in all responses', async () => {
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost:3000/api/clients',
      })

      const response = await clientsGET(request)

      expect(response.headers.get('X-Request-ID')).toBeTruthy()
      expect(response.headers.get('X-Request-ID')).toMatch(/^[a-f0-9-]{36}$/) // UUID format
    })
  })

  describe('Security Headers Validation', () => {
    it('should include all required security headers', async () => {
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost:3000/api/clients',
      })

      const response = await clientsGET(request)

      expect(response.headers.get('X-Content-Type-Options')).toBe('nosniff')
      expect(response.headers.get('X-Frame-Options')).toBe('DENY')
      expect(response.headers.get('X-XSS-Protection')).toBe('1; mode=block')
    })

    it('should handle CORS properly', async () => {
      const request = createMockRequest({
        method: 'OPTIONS',
        url: 'http://localhost:3000/api/clients',
        headers: {
          'Origin': 'http://localhost:3000',
          'Access-Control-Request-Method': 'POST',
        },
      })

      // Note: OPTIONS handling would be implemented in the API handler
      // This test structure shows how it should work
      expect(request.method).toBe('OPTIONS')
    })
  })

  describe('Input Validation and Sanitization', () => {
    it('should validate required fields', async () => {
      const invalidData = {
        first_name: '', // Empty required field
        last_name: 'Doe',
        phone: '1234567890',
      }

      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: invalidData,
      })

      const response = await clientsPOST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error.code).toBe('VALIDATION_ERROR')
      expect(data.error.message).toContain('验证失败')
    })

    it('should validate field formats', async () => {
      const invalidData = {
        first_name: 'John',
        last_name: 'Doe',
        phone: 'invalid-phone',
        email: 'invalid-email',
      }

      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: invalidData,
      })

      const response = await clientsPOST(request)

      expect(response.status).toBe(400)
    })

    it('should validate field lengths', async () => {
      const invalidData = {
        first_name: 'a'.repeat(51), // Too long
        last_name: 'Doe',
        phone: '1234567890',
      }

      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: invalidData,
      })

      const response = await clientsPOST(request)

      expect(response.status).toBe(400)
    })

    it('should sanitize input data', async () => {
      const dataWithHtml = {
        first_name: 'John<script>alert("xss")</script>',
        last_name: 'Doe',
        phone: '1234567890',
        notes: '<img src="x" onerror="alert(1)">',
      }

      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: dataWithHtml,
      })

      const response = await clientsPOST(request)

      // Should either succeed with sanitized data or fail validation
      expect([200, 201, 400]).toContain(response.status)
    })
  })

  describe('Error Handling Scenarios', () => {
    it('should handle malformed JSON', async () => {
      const request = new NextRequest('http://localhost:3000/api/clients', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: 'invalid json{',
      })

      const response = await clientsPOST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error.code).toBe('INVALID_JSON')
    })

    it('should handle missing Content-Type header', async () => {
      const request = new NextRequest('http://localhost:3000/api/clients', {
        method: 'POST',
        body: JSON.stringify({ first_name: 'John', last_name: 'Doe', phone: '1234567890' }),
      })

      const response = await clientsPOST(request)

      // Should handle gracefully
      expect([200, 201, 400]).toContain(response.status)
    })

    it('should handle very large payloads', async () => {
      const largeData = {
        first_name: 'John',
        last_name: 'Doe',
        phone: '1234567890',
        notes: 'a'.repeat(10000), // Very large notes field
      }

      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: largeData,
      })

      const response = await clientsPOST(request)

      expect(response.status).toBe(400) // Should reject due to field length validation
    })

    it('should handle database errors gracefully', async () => {
      // This would require mocking the database to throw specific errors
      const validData = testDataFactory.client({
        id: undefined,
        created_at: undefined,
        updated_at: undefined,
      })

      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: validData,
      })

      const response = await clientsPOST(request)

      // Should either succeed or fail with proper error structure
      expect([200, 201, 409, 500]).toContain(response.status)

      if (response.status >= 400) {
        const data = await response.json()
        expect(data).toHaveProperty('error')
        expect(data.error).toHaveProperty('code')
        expect(data.error).toHaveProperty('message')
      }
    })
  })

  describe('Performance and Scalability', () => {
    it('should respond within acceptable time limits', async () => {
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost:3000/api/clients',
      })

      const start = performance.now()
      const response = await clientsGET(request)
      const end = performance.now()

      expect(response.status).toBe(200)
      expect(end - start).toBeLessThan(1000) // Should respond within 1 second
    })

    it('should handle concurrent requests', async () => {
      const requests = Array.from({ length: 10 }, () =>
        createMockRequest({
          method: 'GET',
          url: 'http://localhost:3000/api/clients',
        })
      )

      const start = performance.now()
      const responses = await Promise.all(requests.map(req => clientsGET(req)))
      const end = performance.now()

      responses.forEach(response => {
        expect(response.status).toBe(200)
      })

      expect(end - start).toBeLessThan(2000) // All requests should complete within 2 seconds
    })

    it('should handle pagination efficiently', async () => {
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost:3000/api/clients',
        searchParams: { page: '1', limit: '50' },
      })

      const response = await clientsGET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveValidPagination()
      expect(data.data.pagination.limit).toBe(50)
    })
  })

  describe('Data Consistency and Integrity', () => {
    it('should maintain data consistency across operations', async () => {
      const clientData = testDataFactory.client({
        id: undefined,
        created_at: undefined,
        updated_at: undefined,
      })

      // Create client
      const createRequest = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: clientData,
      })

      const createResponse = await clientsPOST(createRequest)

      if (createResponse.status === 201) {
        const createData = await createResponse.json()
        expect(createData.data.client.first_name).toBe(clientData.first_name)
        expect(createData.data.client.phone).toBe(clientData.phone)
      }
    })

    it('should prevent duplicate phone numbers', async () => {
      const clientData = testDataFactory.client({
        id: undefined,
        created_at: undefined,
        updated_at: undefined,
        phone: '1234567890', // Fixed phone number
      })

      // First creation should succeed
      const request1 = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: clientData,
      })

      const response1 = await clientsPOST(request1)

      // Second creation with same phone should fail
      const request2 = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: { ...clientData, first_name: 'Different Name' },
      })

      const response2 = await clientsPOST(request2)

      // Should either succeed (if first failed) or fail with conflict
      if (response1.status === 201) {
        expect(response2.status).toBe(409) // Conflict
      }
    })
  })

  describe('Monitoring and Observability', () => {
    it('should log request details for monitoring', async () => {
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost:3000/api/clients',
        headers: {
          'User-Agent': 'Test-Agent/1.0',
          'X-Forwarded-For': '***********',
        },
      })

      const response = await clientsGET(request)

      expect(response.status).toBe(200)
      // Logging would be verified through mock assertions in a real test
    })

    it('should track performance metrics', async () => {
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost:3000/api/clients',
      })

      const start = performance.now()
      const response = await clientsGET(request)
      const end = performance.now()

      expect(response.status).toBe(200)
      expect(end - start).toBeGreaterThan(0)
      // Metrics would be verified through monitoring system mocks
    })
  })

  describe('Edge Cases and Boundary Conditions', () => {
    it('should handle empty request body', async () => {
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: {},
      })

      const response = await clientsPOST(request)

      expect(response.status).toBe(400) // Should fail validation
    })

    it('should handle null values in request', async () => {
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: {
          first_name: null,
          last_name: 'Doe',
          phone: '1234567890',
        },
      })

      const response = await clientsPOST(request)

      expect(response.status).toBe(400) // Should fail validation
    })

    it('should handle Unicode and special characters', async () => {
      const unicodeData = {
        first_name: '张三',
        last_name: '李四',
        phone: '1234567890',
        notes: '测试备注 🎉 emoji',
      }

      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: unicodeData,
      })

      const response = await clientsPOST(request)

      // Should handle Unicode properly
      expect([200, 201]).toContain(response.status)
    })
  })
})
