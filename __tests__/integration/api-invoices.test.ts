import { createMocks } from 'node-mocks-http'
import { GET, POST } from '@/app/api/invoices/route'
import { businessLogic } from '@/lib/supabase/queries'

// Mock Supabase and business logic
jest.mock('@/lib/supabase/client')
jest.mock('@/lib/supabase/queries')

describe('/api/invoices API Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/invoices', () => {
    it('should return all invoices with correct structure', async () => {
      const mockInvoices = [
        {
          id: '1',
          client_id: 'client-1',
          invoice_number: 'INV-20250715-0001',
          total_amount: 500,
          deposit_amount: 150,
          status: 'deposit_pending',
        },
      ]

      const mockSupabase = require('@/lib/supabase/client').supabase
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({ data: mockInvoices, error: null }),
      })

      const { req } = createMocks({ method: 'GET' })
      const response = await GET(req)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('invoices')
      expect(data.invoices).toEqual(mockInvoices)
    })

    it('should filter invoices by client_id', async () => {
      const { req } = createMocks({
        method: 'GET',
        url: '/api/invoices?client_id=client-1',
      })

      const mockSupabase = require('@/lib/supabase/client').supabase
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({ data: [], error: null }),
      })

      const response = await GET(req)
      expect(response.status).toBe(200)
      expect(mockSupabase.from().eq).toHaveBeenCalledWith('client_id', 'client-1')
    })

    it('should filter invoices by status', async () => {
      const { req } = createMocks({
        method: 'GET',
        url: '/api/invoices?status=deposit_pending',
      })

      const mockSupabase = require('@/lib/supabase/client').supabase
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({ data: [], error: null }),
      })

      const response = await GET(req)
      expect(response.status).toBe(200)
      expect(mockSupabase.from().eq).toHaveBeenCalledWith('status', 'deposit_pending')
    })
  })

  describe('POST /api/invoices', () => {
    const validInvoiceData = {
      client_id: 'client-1',
      treatment_date: '2025-07-15',
      total_amount: 500,
      appointments: [
        { id: 'apt-1', treatment_id: 'treatment-1', appointment_date: '2025-07-15' },
        { id: 'apt-2', treatment_id: 'treatment-2', appointment_date: '2025-07-15' },
      ],
    }

    beforeEach(() => {
      // Mock business logic functions
      const mockBusinessLogic = businessLogic as jest.Mocked<typeof businessLogic>
      mockBusinessLogic.generateInvoiceNumber.mockReturnValue('INV-20250715-0001')
      mockBusinessLogic.calculateFixedDeposit.mockResolvedValue(150)

      // Mock Supabase
      const mockSupabase = require('@/lib/supabase/client').supabase
      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { id: 'invoice-1', ...validInvoiceData },
          error: null,
        }),
      })
    })

    it('should create invoice with fixed deposit calculation', async () => {
      const { req } = createMocks({
        method: 'POST',
        body: validInvoiceData,
      })

      const response = await POST(req)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data).toHaveProperty('invoice')
      expect(businessLogic.calculateFixedDeposit).toHaveBeenCalledWith(
        validInvoiceData.appointments
      )
    })

    it('should generate invoice number if not provided', async () => {
      const { req } = createMocks({
        method: 'POST',
        body: validInvoiceData,
      })

      await POST(req)

      expect(businessLogic.generateInvoiceNumber).toHaveBeenCalled()
    })

    it('should use provided invoice number', async () => {
      const dataWithInvoiceNumber = {
        ...validInvoiceData,
        invoice_number: 'CUSTOM-INV-001',
      }

      const { req } = createMocks({
        method: 'POST',
        body: dataWithInvoiceNumber,
      })

      await POST(req)

      expect(businessLogic.generateInvoiceNumber).not.toHaveBeenCalled()
    })

    it('should fallback to percentage-based deposit if no appointments', async () => {
      const mockBusinessLogic = businessLogic as jest.Mocked<typeof businessLogic>
      mockBusinessLogic.calculateDeposit.mockReturnValue(250) // 50% of 500

      const dataWithoutAppointments = {
        client_id: 'client-1',
        treatment_date: '2025-07-15',
        total_amount: 500,
        deposit_percentage: 50,
      }

      const { req } = createMocks({
        method: 'POST',
        body: dataWithoutAppointments,
      })

      await POST(req)

      expect(businessLogic.calculateDeposit).toHaveBeenCalledWith(500, 50)
    })

    it('should validate required fields', async () => {
      const invalidData = {
        client_id: 'client-1',
        // Missing required fields
      }

      const { req } = createMocks({
        method: 'POST',
        body: invalidData,
      })

      const response = await POST(req)
      expect(response.status).toBe(400)
    })

    it('should validate treatment_date format', async () => {
      const invalidData = {
        ...validInvoiceData,
        treatment_date: 'invalid-date',
      }

      const { req } = createMocks({
        method: 'POST',
        body: invalidData,
      })

      const response = await POST(req)
      expect(response.status).toBe(400)
    })

    it('should validate total_amount is positive', async () => {
      const invalidData = {
        ...validInvoiceData,
        total_amount: -100,
      }

      const { req } = createMocks({
        method: 'POST',
        body: invalidData,
      })

      const response = await POST(req)
      expect(response.status).toBe(400)
    })

    it('should handle database errors', async () => {
      const mockSupabase = require('@/lib/supabase/client').supabase
      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockRejectedValue(new Error('Database error')),
      })

      const { req } = createMocks({
        method: 'POST',
        body: validInvoiceData,
      })

      const response = await POST(req)
      expect(response.status).toBe(500)
    })
  })

  describe('Business Logic Integration', () => {
    it('should calculate deposit for same-day appointments correctly', async () => {
      const appointments = [
        { id: 'apt-1', treatment_id: 'treatment-1', appointment_date: '2025-07-15' },
        { id: 'apt-2', treatment_id: 'treatment-2', appointment_date: '2025-07-15' },
      ]

      // Mock treatment responses with different deposit amounts
      const mockSupabase = require('@/lib/supabase/client').supabase
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn()
          .mockResolvedValueOnce({ data: { fixed_deposit_amount: 100 }, error: null })
          .mockResolvedValueOnce({ data: { fixed_deposit_amount: 150 }, error: null }),
      })

      // Reset the mock to use actual implementation
      jest.unmock('@/lib/supabase/queries')
      const { businessLogic: actualBusinessLogic } = require('@/lib/supabase/queries')

      const result = await actualBusinessLogic.calculateFixedDeposit(appointments)
      expect(result).toBe(150) // Should return the highest amount
    })

    it('should generate unique invoice numbers', () => {
      jest.unmock('@/lib/supabase/queries')
      const { businessLogic: actualBusinessLogic } = require('@/lib/supabase/queries')

      const invoice1 = actualBusinessLogic.generateInvoiceNumber()
      const invoice2 = actualBusinessLogic.generateInvoiceNumber()

      expect(invoice1).toMatch(/^INV-\d{8}-\d{4}$/)
      expect(invoice2).toMatch(/^INV-\d{8}-\d{4}$/)
      expect(invoice1).not.toBe(invoice2)
    })
  })

  describe('Error Handling', () => {
    it('should handle malformed JSON', async () => {
      const { req } = createMocks({
        method: 'POST',
        headers: { 'content-type': 'application/json' },
        body: 'invalid-json',
      })

      const response = await POST(req)
      expect(response.status).toBe(400)
    })

    it('should handle missing content-type header', async () => {
      const { req } = createMocks({
        method: 'POST',
        body: validInvoiceData,
      })

      const response = await POST(req)
      expect(response.status).toBe(400)
    })

    it('should handle database connection failures', async () => {
      const mockSupabase = require('@/lib/supabase/client').supabase
      mockSupabase.from.mockImplementation(() => {
        throw new Error('Connection failed')
      })

      const { req } = createMocks({ method: 'GET' })
      const response = await GET(req)

      expect(response.status).toBe(500)
    })
  })
})
