import { createMocks } from 'node-mocks-http'
import { GET, POST, PUT, DELETE } from '@/app/api/clients/route'
import { GET as getById, PUT as updateById, DELETE as deleteById } from '@/app/api/clients/[id]/route'

// Mock Supabase
jest.mock('@/lib/supabase/client')

describe('/api/clients API Integration Tests', () => {
  describe('GET /api/clients', () => {
    it('should return all clients with correct structure', async () => {
      const { req } = createMocks({ method: 'GET' })
      
      const response = await GET(req)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('clients')
      expect(data).toHaveProperty('total')
      expect(Array.isArray(data.clients)).toBe(true)
    })

    it('should handle search parameters', async () => {
      const { req } = createMocks({ 
        method: 'GET',
        url: '/api/clients?search=test&status=active'
      })
      
      const response = await GET(req)
      expect(response.status).toBe(200)
    })

    it('should handle pagination parameters', async () => {
      const { req } = createMocks({ 
        method: 'GET',
        url: '/api/clients?page=2&limit=10'
      })
      
      const response = await GET(req)
      expect(response.status).toBe(200)
    })
  })

  describe('POST /api/clients', () => {
    const validClientData = {
      first_name: '测试',
      last_name: '用户',
      phone: '18888888888',
      email: '<EMAIL>',
      status: 'active',
      preferred_language: 'zh-CN'
    }

    it('should create a new client with valid data', async () => {
      const { req } = createMocks({
        method: 'POST',
        body: validClientData
      })

      const response = await POST(req)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data).toHaveProperty('client')
      expect(data.client).toHaveProperty('id')
    })

    it('should validate required fields', async () => {
      const { req } = createMocks({
        method: 'POST',
        body: { first_name: '测试' } // Missing required fields
      })

      const response = await POST(req)
      expect(response.status).toBe(400)
    })

    it('should validate phone number format', async () => {
      const { req } = createMocks({
        method: 'POST',
        body: { ...validClientData, phone: 'invalid-phone' }
      })

      const response = await POST(req)
      expect(response.status).toBe(400)
    })

    it('should validate email format', async () => {
      const { req } = createMocks({
        method: 'POST',
        body: { ...validClientData, email: 'invalid-email' }
      })

      const response = await POST(req)
      expect(response.status).toBe(400)
    })

    it('should handle duplicate phone numbers', async () => {
      // This would require mocking Supabase to return a unique constraint error
      const { req } = createMocks({
        method: 'POST',
        body: validClientData
      })

      // Mock Supabase to simulate unique constraint violation
      const mockSupabase = require('@/lib/supabase/client').supabase
      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockRejectedValue({
          code: '23505', // PostgreSQL unique violation
          message: 'duplicate key value violates unique constraint'
        })
      })

      const response = await POST(req)
      expect(response.status).toBe(409)
    })
  })

  describe('GET /api/clients/[id]', () => {
    it('should return client by ID', async () => {
      const { req } = createMocks({ method: 'GET' })
      
      const response = await getById(req, { params: { id: 'test-id' } })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('client')
    })

    it('should return 404 for non-existent client', async () => {
      const mockSupabase = require('@/lib/supabase/client').supabase
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: null, error: { code: 'PGRST116' } })
      })

      const { req } = createMocks({ method: 'GET' })
      const response = await getById(req, { params: { id: 'non-existent' } })

      expect(response.status).toBe(404)
    })
  })

  describe('PUT /api/clients/[id]', () => {
    const updateData = {
      first_name: '更新',
      last_name: '用户',
      email: '<EMAIL>'
    }

    it('should update client with valid data', async () => {
      const { req } = createMocks({
        method: 'PUT',
        body: updateData
      })

      const response = await updateById(req, { params: { id: 'test-id' } })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('client')
    })

    it('should validate update data', async () => {
      const { req } = createMocks({
        method: 'PUT',
        body: { email: 'invalid-email' }
      })

      const response = await updateById(req, { params: { id: 'test-id' } })
      expect(response.status).toBe(400)
    })

    it('should return 404 for non-existent client', async () => {
      const mockSupabase = require('@/lib/supabase/client').supabase
      mockSupabase.from.mockReturnValue({
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: null, error: { code: 'PGRST116' } })
      })

      const { req } = createMocks({
        method: 'PUT',
        body: updateData
      })

      const response = await updateById(req, { params: { id: 'non-existent' } })
      expect(response.status).toBe(404)
    })
  })

  describe('DELETE /api/clients/[id]', () => {
    it('should delete client successfully', async () => {
      const { req } = createMocks({ method: 'DELETE' })
      
      const response = await deleteById(req, { params: { id: 'test-id' } })

      expect(response.status).toBe(200)
    })

    it('should return 404 for non-existent client', async () => {
      const mockSupabase = require('@/lib/supabase/client').supabase
      mockSupabase.from.mockReturnValue({
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockRejectedValue({ code: 'PGRST116' })
      })

      const { req } = createMocks({ method: 'DELETE' })
      const response = await deleteById(req, { params: { id: 'non-existent' } })

      expect(response.status).toBe(404)
    })
  })

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      const mockSupabase = require('@/lib/supabase/client').supabase
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        order: jest.fn().mockRejectedValue(new Error('Database connection failed'))
      })

      const { req } = createMocks({ method: 'GET' })
      const response = await GET(req)

      expect(response.status).toBe(500)
    })

    it('should handle malformed JSON in POST requests', async () => {
      const { req } = createMocks({
        method: 'POST',
        headers: { 'content-type': 'application/json' },
        body: 'invalid-json'
      })

      const response = await POST(req)
      expect(response.status).toBe(400)
    })
  })
})
