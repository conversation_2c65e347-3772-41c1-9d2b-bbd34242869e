/**
 * Enhanced API Integration Tests for /api/clients
 * Tests the new professional API handler with comprehensive validation and error handling
 */

import { createMockRequest, testDataFactory, apiTestHelpers, testSetup, customMatchers } from '../../src/lib/test-utils'
import { GET, POST } from '../../src/app/api/clients/route'

// Extend Jest matchers
expect.extend(customMatchers)

// Mock Supabase
jest.mock('@/lib/supabase/client')

describe('/api/clients - Enhanced Professional API Tests', () => {
  beforeAll(testSetup.beforeAll)
  afterAll(testSetup.afterAll)
  beforeEach(testSetup.beforeEach)
  afterEach(testSetup.afterEach)

  describe('GET /api/clients', () => {
    it('should return paginated clients with valid response structure', async () => {
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost:3000/api/clients',
        searchParams: { page: '1', limit: '10' }
      })

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toBeValidApiResponse()
      expect(data).toHaveValidPagination()
      expect(data.data).toHaveProperty('clients')
      expect(Array.isArray(data.data.clients)).toBe(true)
      expect(response.headers.get('X-Request-ID')).toBeTruthy()
    })

    it('should search clients by query with proper validation', async () => {
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost:3000/api/clients',
        searchParams: { q: 'test', page: '1', limit: '5' }
      })

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toBeValidApiResponse()
      expect(data.data).toHaveProperty('clients')
      expect(data.data.pagination.limit).toBe(5)
    })

    it('should handle invalid pagination parameters', async () => {
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost:3000/api/clients',
        searchParams: { page: '0', limit: '200' }
      })

      const response = await GET(request)
      
      expect(response.status).toBe(400)
      const data = await response.json()
      expect(data.error.code).toBe('VALIDATION_ERROR')
    })

    it('should include security headers', async () => {
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost:3000/api/clients'
      })

      const response = await GET(request)

      expect(response.headers.get('X-Content-Type-Options')).toBe('nosniff')
      expect(response.headers.get('X-Frame-Options')).toBe('DENY')
      expect(response.headers.get('X-XSS-Protection')).toBe('1; mode=block')
    })
  })

  describe('POST /api/clients', () => {
    it('should create a new client with comprehensive validation', async () => {
      const clientData = testDataFactory.client({
        id: undefined, // Remove ID for creation
        created_at: undefined,
        updated_at: undefined,
      })

      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: clientData
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data).toBeValidApiResponse()
      expect(data.message).toBe('客户创建成功')
      expect(data.data).toHaveProperty('client')
      expect(data.data.client.first_name).toBe(clientData.first_name)
      expect(data.data.client.last_name).toBe(clientData.last_name)
      expect(data.data.client.phone).toBe(clientData.phone)
    })

    it('should validate required fields', async () => {
      const invalidData = {
        first_name: '', // Empty required field
        last_name: 'User',
        phone: '1234567890'
      }

      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: invalidData
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error.code).toBe('VALIDATION_ERROR')
      expect(data.error.message).toContain('验证失败')
    })

    it('should validate phone number format', async () => {
      const invalidData = {
        first_name: 'Test',
        last_name: 'User',
        phone: 'invalid-phone' // Invalid phone format
      }

      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: invalidData
      })

      const response = await POST(request)

      expect(response.status).toBe(400)
      const data = await response.json()
      expect(data.error.code).toBe('VALIDATION_ERROR')
    })

    it('should validate email format when provided', async () => {
      const invalidData = {
        first_name: 'Test',
        last_name: 'User',
        phone: '1234567890',
        email: 'invalid-email' // Invalid email format
      }

      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: invalidData
      })

      const response = await POST(request)

      expect(response.status).toBe(400)
      const data = await response.json()
      expect(data.error.code).toBe('VALIDATION_ERROR')
    })

    it('should handle JSON parsing errors', async () => {
      const request = new Request('http://localhost:3000/api/clients', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: 'invalid json'
      }) as any

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error.code).toBe('INVALID_JSON')
    })

    it('should validate address fields when provided', async () => {
      const clientData = {
        first_name: 'Test',
        last_name: 'User',
        phone: '1234567890',
        address_line_1: 'a'.repeat(201), // Too long
      }

      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: clientData
      })

      const response = await POST(request)

      expect(response.status).toBe(400)
      const data = await response.json()
      expect(data.error.code).toBe('VALIDATION_ERROR')
    })

    it('should validate notes length', async () => {
      const clientData = {
        first_name: 'Test',
        last_name: 'User',
        phone: '1234567890',
        notes: 'a'.repeat(1001), // Too long
      }

      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: clientData
      })

      const response = await POST(request)

      expect(response.status).toBe(400)
      const data = await response.json()
      expect(data.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('Error Handling', () => {
    it('should return 405 for unsupported methods', async () => {
      const request = createMockRequest({
        method: 'DELETE',
        url: 'http://localhost:3000/api/clients'
      })

      // Since our handler doesn't support DELETE, it should return 405
      const response = await GET(request) // This will fail since DELETE is not supported

      expect(response.status).toBe(405)
    })

    it('should include request ID in error responses', async () => {
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: { invalid: 'data' }
      })

      const response = await POST(request)

      expect(response.headers.get('X-Request-ID')).toBeTruthy()
    })

    it('should handle database errors gracefully', async () => {
      // Mock a database error
      const clientData = testDataFactory.client()

      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: clientData
      })

      // This would require mocking the database to throw an error
      const response = await POST(request)
      
      // Should either succeed or fail with proper error structure
      expect([200, 201, 409, 500]).toContain(response.status)
      
      if (response.status >= 400) {
        const data = await response.json()
        expect(data).toHaveProperty('error')
        expect(data.error).toHaveProperty('code')
        expect(data.error).toHaveProperty('message')
        expect(data.error).toHaveProperty('timestamp')
      }
    })
  })

  describe('Performance Tests', () => {
    it('should respond within acceptable time limits', async () => {
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost:3000/api/clients'
      })

      const start = performance.now()
      const response = await GET(request)
      const end = performance.now()

      expect(response.status).toBe(200)
      expect(end - start).toBeLessThan(1000) // Should respond within 1 second
    })
  })
})
