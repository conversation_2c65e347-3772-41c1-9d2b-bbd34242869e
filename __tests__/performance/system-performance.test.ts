/**
 * System Performance Tests
 * Comprehensive performance testing for caching, database, and API optimization
 */

import { cache, cacheUtils } from '../../src/lib/cache'
import { db, queryOptimizer } from '../../src/lib/database/performance'
import { performanceTestHelpers } from '../../src/lib/test-utils'
import { apiClient } from '../../src/lib/react-query'

describe('System Performance Tests', () => {
  // Performance thresholds
  const PERFORMANCE_THRESHOLDS = {
    CACHE_OPERATION: 10, // ms
    DATABASE_QUERY: 100, // ms
    API_RESPONSE: 200, // ms
    CONCURRENT_OPERATIONS: 500, // ms for 10 concurrent operations
    MEMORY_LEAK_THRESHOLD: 50 * 1024 * 1024, // 50MB
  }

  describe('Cache Performance', () => {
    beforeEach(async () => {
      await cache.clear()
    })

    it('should perform cache operations within acceptable time', async () => {
      const testData = { id: 1, name: 'Test Data', data: 'x'.repeat(1000) }
      
      // Test cache set performance
      const { duration: setDuration } = await performanceTestHelpers.measureTime(async () => {
        await cache.set('test-key', testData, { ttl: 300 })
      })
      
      expect(setDuration).toBeLessThan(PERFORMANCE_THRESHOLDS.CACHE_OPERATION)
      
      // Test cache get performance
      const { duration: getDuration } = await performanceTestHelpers.measureTime(async () => {
        const result = await cache.get('test-key')
        expect(result).toEqual(testData)
      })
      
      expect(getDuration).toBeLessThan(PERFORMANCE_THRESHOLDS.CACHE_OPERATION)
    })

    it('should handle high-frequency cache operations efficiently', async () => {
      const operations = 1000
      const testData = { id: 1, name: 'Test' }
      
      const { duration } = await performanceTestHelpers.measureTime(async () => {
        const promises = Array.from({ length: operations }, (_, i) => 
          cache.set(`key-${i}`, { ...testData, id: i })
        )
        await Promise.all(promises)
      })
      
      const averageTimePerOperation = duration / operations
      expect(averageTimePerOperation).toBeLessThan(PERFORMANCE_THRESHOLDS.CACHE_OPERATION)
      
      console.log(`Cache performance: ${operations} operations in ${duration.toFixed(2)}ms (${averageTimePerOperation.toFixed(3)}ms avg)`)
    })

    it('should maintain performance with large cache sizes', async () => {
      // Fill cache with 10,000 entries
      const entries = 10000
      const largeData = 'x'.repeat(1000) // 1KB per entry
      
      for (let i = 0; i < entries; i++) {
        await cache.set(`large-key-${i}`, { id: i, data: largeData })
      }
      
      // Test performance with full cache
      const { duration } = await performanceTestHelpers.measureTime(async () => {
        await cache.get('large-key-5000') // Middle entry
      })
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.CACHE_OPERATION * 2) // Allow 2x threshold for large cache
    })

    it('should efficiently handle cache invalidation by tags', async () => {
      // Set up cache entries with tags
      const entriesPerTag = 100
      const tags = ['tag1', 'tag2', 'tag3']
      
      for (const tag of tags) {
        for (let i = 0; i < entriesPerTag; i++) {
          await cache.set(`${tag}-key-${i}`, { tag, id: i }, { tags: [tag] })
        }
      }
      
      // Test tag-based invalidation performance
      const { duration } = await performanceTestHelpers.measureTime(async () => {
        const invalidated = await cache.invalidateByTags(['tag1'])
        expect(invalidated).toBe(entriesPerTag)
      })
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.CACHE_OPERATION * 10) // Allow more time for bulk operations
    })

    it('should not leak memory during sustained cache operations', async () => {
      const initialMemory = process.memoryUsage()
      const iterations = 1000
      
      for (let i = 0; i < iterations; i++) {
        await cache.set(`memory-test-${i}`, { 
          id: i, 
          data: 'x'.repeat(1000),
          timestamp: Date.now() 
        })
        
        // Occasionally get and delete to simulate real usage
        if (i % 10 === 0) {
          await cache.get(`memory-test-${i - 5}`)
          await cache.delete(`memory-test-${i - 5}`)
        }
        
        // Force garbage collection every 100 iterations
        if (i % 100 === 0 && global.gc) {
          global.gc()
        }
      }
      
      const finalMemory = process.memoryUsage()
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed
      
      expect(memoryIncrease).toBeLessThan(PERFORMANCE_THRESHOLDS.MEMORY_LEAK_THRESHOLD)
      
      console.log(`Memory usage: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB increase after ${iterations} operations`)
    })
  })

  describe('Database Performance', () => {
    it('should execute optimized queries within acceptable time', async () => {
      const { duration } = await performanceTestHelpers.measureTime(async () => {
        const result = await db.query(
          'clients',
          (client) => client.select('*').limit(10),
          {
            cache: { enabled: true, ttl: 300, tags: ['clients'], namespace: 'test' },
            monitor: true,
          }
        )
        expect(result.data).toBeDefined()
      })
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.DATABASE_QUERY)
    })

    it('should handle paginated queries efficiently', async () => {
      const { duration } = await performanceTestHelpers.measureTime(async () => {
        const result = await db.paginate(
          'clients',
          (client) => client.select('*'),
          { page: 1, limit: 20 },
          {
            cache: { enabled: true, ttl: 300, tags: ['clients'], namespace: 'test' },
          }
        )
        expect(result.data).toBeDefined()
        expect(result.page).toBe(1)
        expect(result.limit).toBe(20)
      })
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.DATABASE_QUERY)
    })

    it('should execute batch queries efficiently', async () => {
      const queries = [
        {
          table: 'clients',
          queryBuilder: (client: any) => client.select('id, first_name, last_name').limit(5),
        },
        {
          table: 'treatments',
          queryBuilder: (treatment: any) => treatment.select('id, name_chinese').limit(5),
        },
        {
          table: 'appointments',
          queryBuilder: (appointment: any) => appointment.select('id, appointment_date').limit(5),
        },
      ]
      
      const { duration } = await performanceTestHelpers.measureTime(async () => {
        const results = await db.batch(queries)
        expect(results).toHaveLength(3)
        results.forEach(result => {
          expect(result.data).toBeDefined()
        })
      })
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.DATABASE_QUERY * 2) // Allow 2x for batch operations
    })

    it('should benefit from query caching', async () => {
      const queryBuilder = (client: any) => client.select('*').limit(10)
      const cacheConfig = {
        cache: { enabled: true, ttl: 300, tags: ['clients'], namespace: 'perf-test' },
      }
      
      // First query (cache miss)
      const { duration: firstDuration } = await performanceTestHelpers.measureTime(async () => {
        const result = await db.query('clients', queryBuilder, cacheConfig)
        expect(result.fromCache).toBe(false)
      })
      
      // Second query (cache hit)
      const { duration: secondDuration } = await performanceTestHelpers.measureTime(async () => {
        const result = await db.query('clients', queryBuilder, cacheConfig)
        expect(result.fromCache).toBe(true)
      })
      
      // Cache hit should be significantly faster
      expect(secondDuration).toBeLessThan(firstDuration * 0.5)
      expect(secondDuration).toBeLessThan(PERFORMANCE_THRESHOLDS.CACHE_OPERATION)
      
      console.log(`Query caching: First query ${firstDuration.toFixed(2)}ms, cached query ${secondDuration.toFixed(2)}ms`)
    })
  })

  describe('API Performance', () => {
    it('should handle concurrent API requests efficiently', async () => {
      const concurrentRequests = 10
      const requests = Array.from({ length: concurrentRequests }, () => 
        performanceTestHelpers.measureTime(() => apiClient.clients.getAll())
      )
      
      const { duration } = await performanceTestHelpers.measureTime(async () => {
        const results = await Promise.all(requests)
        results.forEach(({ result }) => {
          expect(result).toBeDefined()
        })
      })
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.CONCURRENT_OPERATIONS)
      
      const averageTime = duration / concurrentRequests
      console.log(`Concurrent API performance: ${concurrentRequests} requests in ${duration.toFixed(2)}ms (${averageTime.toFixed(2)}ms avg)`)
    })

    it('should maintain performance under sustained load', async () => {
      const iterations = 50
      const results: number[] = []
      
      for (let i = 0; i < iterations; i++) {
        const { duration } = await performanceTestHelpers.measureTime(async () => {
          await apiClient.clients.getAll({ page: 1, limit: 10 })
        })
        results.push(duration)
        
        // Small delay to simulate real usage
        await new Promise(resolve => setTimeout(resolve, 10))
      }
      
      const averageTime = results.reduce((sum, time) => sum + time, 0) / results.length
      const maxTime = Math.max(...results)
      const minTime = Math.min(...results)
      
      expect(averageTime).toBeLessThan(PERFORMANCE_THRESHOLDS.API_RESPONSE)
      expect(maxTime).toBeLessThan(PERFORMANCE_THRESHOLDS.API_RESPONSE * 3) // Allow 3x for outliers
      
      // Check for performance degradation (max should not be more than 5x average)
      expect(maxTime).toBeLessThan(averageTime * 5)
      
      console.log(`Sustained load performance: avg ${averageTime.toFixed(2)}ms, min ${minTime.toFixed(2)}ms, max ${maxTime.toFixed(2)}ms`)
    })

    it('should efficiently handle search operations', async () => {
      const searchQueries = ['张', '李', '王', 'test', 'clinic']
      
      for (const query of searchQueries) {
        const { duration } = await performanceTestHelpers.measureTime(async () => {
          await apiClient.clients.search(query)
        })
        
        expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.API_RESPONSE)
      }
    })
  })

  describe('Memory and Resource Management', () => {
    it('should not leak memory during mixed operations', async () => {
      const initialMemory = process.memoryUsage()
      const iterations = 100
      
      for (let i = 0; i < iterations; i++) {
        // Mix of cache, database, and API operations
        await cache.set(`mixed-test-${i}`, { id: i, data: 'test data' })
        
        if (i % 10 === 0) {
          await db.query('clients', (client) => client.select('id').limit(1))
          await cache.get(`mixed-test-${i - 5}`)
        }
        
        if (i % 20 === 0 && global.gc) {
          global.gc()
        }
      }
      
      const finalMemory = process.memoryUsage()
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed
      
      expect(memoryIncrease).toBeLessThan(PERFORMANCE_THRESHOLDS.MEMORY_LEAK_THRESHOLD)
      
      console.log(`Mixed operations memory usage: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB increase`)
    })

    it('should handle resource cleanup properly', async () => {
      const resourceCount = 1000
      const resources: any[] = []
      
      // Create resources
      for (let i = 0; i < resourceCount; i++) {
        const resource = {
          id: i,
          data: new Array(1000).fill('x'), // 1KB per resource
          cleanup: () => { /* cleanup logic */ },
        }
        resources.push(resource)
        await cache.set(`resource-${i}`, resource)
      }
      
      const beforeCleanup = process.memoryUsage()
      
      // Cleanup resources
      for (let i = 0; i < resourceCount; i++) {
        await cache.delete(`resource-${i}`)
        resources[i] = null
      }
      
      // Force garbage collection
      if (global.gc) {
        global.gc()
      }
      
      const afterCleanup = process.memoryUsage()
      const memoryFreed = beforeCleanup.heapUsed - afterCleanup.heapUsed
      
      // Should free at least 50% of allocated memory
      expect(memoryFreed).toBeGreaterThan(0)
      
      console.log(`Resource cleanup: ${(memoryFreed / 1024 / 1024).toFixed(2)}MB freed`)
    })
  })

  describe('Performance Regression Detection', () => {
    it('should maintain consistent performance characteristics', async () => {
      const testCases = [
        {
          name: 'Cache Set Operation',
          operation: () => cache.set('perf-test', { data: 'test' }),
          threshold: PERFORMANCE_THRESHOLDS.CACHE_OPERATION,
        },
        {
          name: 'Cache Get Operation',
          operation: () => cache.get('perf-test'),
          threshold: PERFORMANCE_THRESHOLDS.CACHE_OPERATION,
        },
        {
          name: 'Database Query',
          operation: () => db.query('clients', (client) => client.select('id').limit(1)),
          threshold: PERFORMANCE_THRESHOLDS.DATABASE_QUERY,
        },
      ]
      
      const results: Record<string, number> = {}
      
      for (const testCase of testCases) {
        const { duration } = await performanceTestHelpers.measureTime(testCase.operation)
        results[testCase.name] = duration
        
        expect(duration).toBeLessThan(testCase.threshold)
      }
      
      // Log performance baseline for future comparison
      console.log('Performance baseline:')
      Object.entries(results).forEach(([name, duration]) => {
        console.log(`  ${name}: ${duration.toFixed(2)}ms`)
      })
    })
  })
})
