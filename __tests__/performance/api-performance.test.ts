/**
 * Performance Tests for API Endpoints
 * Tests response times, throughput, and resource usage under various loads
 */

import { createMockRequest, testDataFactory, performanceTestHelpers } from '../../src/lib/test-utils'
import { GET as clientsGET, POST as clientsPOST } from '../../src/app/api/clients/route'

describe('API Performance Tests', () => {
  // Performance thresholds (in milliseconds)
  const PERFORMANCE_THRESHOLDS = {
    FAST_RESPONSE: 100,
    ACCEPTABLE_RESPONSE: 500,
    SLOW_RESPONSE: 1000,
    CONCURRENT_REQUESTS: 10,
    LOAD_TEST_REQUESTS: 100,
  }

  describe('Response Time Performance', () => {
    it('should respond to GET requests within acceptable time', async () => {
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost:3000/api/clients',
      })

      const { duration } = await performanceTestHelpers.measureTime(async () => {
        const response = await clientsGET(request)
        expect(response.status).toBe(200)
        return response
      })

      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.ACCEPTABLE_RESPONSE)
      
      if (duration > PERFORMANCE_THRESHOLDS.FAST_RESPONSE) {
        console.warn(`GET /api/clients took ${duration.toFixed(2)}ms (slower than ${PERFORMANCE_THRESHOLDS.FAST_RESPONSE}ms threshold)`)
      }
    })

    it('should respond to POST requests within acceptable time', async () => {
      const clientData = testDataFactory.client({
        id: undefined,
        created_at: undefined,
        updated_at: undefined,
      })

      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: clientData,
      })

      const { duration } = await performanceTestHelpers.measureTime(async () => {
        const response = await clientsPOST(request)
        expect([200, 201, 409]).toContain(response.status) // Allow conflict for duplicate data
        return response
      })

      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.ACCEPTABLE_RESPONSE)
      
      if (duration > PERFORMANCE_THRESHOLDS.FAST_RESPONSE) {
        console.warn(`POST /api/clients took ${duration.toFixed(2)}ms (slower than ${PERFORMANCE_THRESHOLDS.FAST_RESPONSE}ms threshold)`)
      }
    })

    it('should handle validation errors quickly', async () => {
      const invalidData = {
        first_name: '', // Invalid empty field
        last_name: 'Doe',
        phone: 'invalid-phone',
      }

      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: invalidData,
      })

      const { duration } = await performanceTestHelpers.measureTime(async () => {
        const response = await clientsPOST(request)
        expect(response.status).toBe(400)
        return response
      })

      // Validation errors should be very fast
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.FAST_RESPONSE)
    })
  })

  describe('Concurrent Request Performance', () => {
    it('should handle concurrent GET requests efficiently', async () => {
      const requests = Array.from({ length: PERFORMANCE_THRESHOLDS.CONCURRENT_REQUESTS }, () =>
        createMockRequest({
          method: 'GET',
          url: 'http://localhost:3000/api/clients',
        })
      )

      const { duration } = await performanceTestHelpers.measureTime(async () => {
        const responses = await Promise.all(requests.map(req => clientsGET(req)))
        
        responses.forEach(response => {
          expect(response.status).toBe(200)
        })
        
        return responses
      })

      // Concurrent requests should not take much longer than a single request
      const averageTimePerRequest = duration / PERFORMANCE_THRESHOLDS.CONCURRENT_REQUESTS
      expect(averageTimePerRequest).toBeLessThan(PERFORMANCE_THRESHOLDS.ACCEPTABLE_RESPONSE)

      console.log(`${PERFORMANCE_THRESHOLDS.CONCURRENT_REQUESTS} concurrent GET requests completed in ${duration.toFixed(2)}ms (${averageTimePerRequest.toFixed(2)}ms average per request)`)
    })

    it('should handle mixed concurrent requests', async () => {
      const getRequests = Array.from({ length: 5 }, () =>
        createMockRequest({
          method: 'GET',
          url: 'http://localhost:3000/api/clients',
        })
      )

      const postRequests = Array.from({ length: 5 }, (_, index) => {
        const clientData = testDataFactory.client({
          id: undefined,
          created_at: undefined,
          updated_at: undefined,
          phone: `555000${index.toString().padStart(4, '0')}`, // Unique phone numbers
        })

        return createMockRequest({
          method: 'POST',
          url: 'http://localhost:3000/api/clients',
          body: clientData,
        })
      })

      const { duration } = await performanceTestHelpers.measureTime(async () => {
        const getPromises = getRequests.map(req => clientsGET(req))
        const postPromises = postRequests.map(req => clientsPOST(req))
        
        const [getResponses, postResponses] = await Promise.all([
          Promise.all(getPromises),
          Promise.all(postPromises),
        ])

        getResponses.forEach(response => {
          expect(response.status).toBe(200)
        })

        postResponses.forEach(response => {
          expect([200, 201, 409]).toContain(response.status)
        })

        return { getResponses, postResponses }
      })

      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.SLOW_RESPONSE)
      console.log(`Mixed concurrent requests (5 GET + 5 POST) completed in ${duration.toFixed(2)}ms`)
    })
  })

  describe('Load Testing', () => {
    it('should maintain performance under sustained load', async () => {
      const iterations = 50 // Reduced for test performance
      
      const benchmark = await performanceTestHelpers.benchmarkFunction(
        async () => {
          const request = createMockRequest({
            method: 'GET',
            url: 'http://localhost:3000/api/clients',
          })
          
          const response = await clientsGET(request)
          expect(response.status).toBe(200)
          return response
        },
        iterations
      )

      expect(benchmark.averageTime).toBeLessThan(PERFORMANCE_THRESHOLDS.ACCEPTABLE_RESPONSE)
      expect(benchmark.maxTime).toBeLessThan(PERFORMANCE_THRESHOLDS.SLOW_RESPONSE)

      console.log(`Load test results (${iterations} iterations):`)
      console.log(`  Average: ${benchmark.averageTime.toFixed(2)}ms`)
      console.log(`  Min: ${benchmark.minTime.toFixed(2)}ms`)
      console.log(`  Max: ${benchmark.maxTime.toFixed(2)}ms`)
      console.log(`  Total: ${benchmark.totalTime.toFixed(2)}ms`)

      // Check for performance degradation (max should not be more than 3x average)
      expect(benchmark.maxTime).toBeLessThan(benchmark.averageTime * 3)
    })

    it('should handle burst traffic patterns', async () => {
      // Simulate burst traffic: many requests at once, then pause, then repeat
      const burstSize = 20
      const burstCount = 3
      const burstDelay = 100 // ms between bursts

      const allDurations: number[] = []

      for (let burst = 0; burst < burstCount; burst++) {
        const requests = Array.from({ length: burstSize }, () =>
          createMockRequest({
            method: 'GET',
            url: 'http://localhost:3000/api/clients',
          })
        )

        const { duration } = await performanceTestHelpers.measureTime(async () => {
          const responses = await Promise.all(requests.map(req => clientsGET(req)))
          
          responses.forEach(response => {
            expect(response.status).toBe(200)
          })
          
          return responses
        })

        allDurations.push(duration)
        
        if (burst < burstCount - 1) {
          await new Promise(resolve => setTimeout(resolve, burstDelay))
        }
      }

      // All bursts should complete within reasonable time
      allDurations.forEach((duration, index) => {
        expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.SLOW_RESPONSE * 2)
        console.log(`Burst ${index + 1}: ${burstSize} requests in ${duration.toFixed(2)}ms`)
      })

      // Performance should not degrade significantly across bursts
      const firstBurst = allDurations[0]
      const lastBurst = allDurations[allDurations.length - 1]
      expect(lastBurst).toBeLessThan(firstBurst * 2) // Should not be more than 2x slower
    })
  })

  describe('Memory and Resource Usage', () => {
    it('should not leak memory during repeated requests', async () => {
      const initialMemory = process.memoryUsage()
      const iterations = 100

      // Perform many requests
      for (let i = 0; i < iterations; i++) {
        const request = createMockRequest({
          method: 'GET',
          url: 'http://localhost:3000/api/clients',
        })

        const response = await clientsGET(request)
        expect(response.status).toBe(200)

        // Force garbage collection every 10 iterations if available
        if (i % 10 === 0 && global.gc) {
          global.gc()
        }
      }

      const finalMemory = process.memoryUsage()
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed
      const memoryIncreasePerRequest = memoryIncrease / iterations

      console.log(`Memory usage after ${iterations} requests:`)
      console.log(`  Initial heap: ${(initialMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`)
      console.log(`  Final heap: ${(finalMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`)
      console.log(`  Increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)} MB`)
      console.log(`  Per request: ${(memoryIncreasePerRequest / 1024).toFixed(2)} KB`)

      // Memory increase should be reasonable (less than 1MB per 100 requests)
      expect(memoryIncrease).toBeLessThan(1024 * 1024) // 1MB
    })

    it('should handle large payloads efficiently', async () => {
      const largeClientData = {
        ...testDataFactory.client({
          id: undefined,
          created_at: undefined,
          updated_at: undefined,
        }),
        notes: 'Large notes field: ' + 'x'.repeat(1000), // 1KB of notes
      }

      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost:3000/api/clients',
        body: largeClientData,
      })

      const { duration } = await performanceTestHelpers.measureTime(async () => {
        const response = await clientsPOST(request)
        expect([200, 201, 400]).toContain(response.status) // May fail validation due to length
        return response
      })

      // Large payloads should still be processed reasonably quickly
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.ACCEPTABLE_RESPONSE * 2)
    })
  })

  describe('Performance Regression Detection', () => {
    it('should maintain consistent performance characteristics', async () => {
      // This test would typically compare against baseline metrics
      // For now, we'll establish current performance as baseline
      
      const testCases = [
        { name: 'GET /api/clients', method: 'GET' as const },
        { name: 'POST /api/clients (valid)', method: 'POST' as const, valid: true },
        { name: 'POST /api/clients (invalid)', method: 'POST' as const, valid: false },
      ]

      const results: Record<string, number> = {}

      for (const testCase of testCases) {
        let request
        
        if (testCase.method === 'GET') {
          request = createMockRequest({
            method: 'GET',
            url: 'http://localhost:3000/api/clients',
          })
        } else {
          const data = testCase.valid 
            ? testDataFactory.client({ id: undefined, created_at: undefined, updated_at: undefined })
            : { invalid: 'data' }
          
          request = createMockRequest({
            method: 'POST',
            url: 'http://localhost:3000/api/clients',
            body: data,
          })
        }

        const { duration } = await performanceTestHelpers.measureTime(async () => {
          if (testCase.method === 'GET') {
            return await clientsGET(request)
          } else {
            return await clientsPOST(request)
          }
        })

        results[testCase.name] = duration
      }

      // Log performance baseline for future comparison
      console.log('Performance baseline:')
      Object.entries(results).forEach(([name, duration]) => {
        console.log(`  ${name}: ${duration.toFixed(2)}ms`)
      })

      // All operations should be within acceptable limits
      Object.entries(results).forEach(([name, duration]) => {
        expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.ACCEPTABLE_RESPONSE)
      })
    })
  })
})
