import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ClientModal } from '@/components/modals/ClientModal'

// Mock the toast function
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}))

// Mock fetch
global.fetch = jest.fn()

describe('ClientModal Component', () => {
  const mockOnClose = jest.fn()
  const mockOnSuccess = jest.fn()

  const defaultProps = {
    isOpen: true,
    onClose: mockOnClose,
    onSuccess: mockOnSuccess,
  }

  beforeEach(() => {
    jest.clearAllMocks()
    ;(global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ client: { id: '1', first_name: '测试' } }),
    })
  })

  describe('Create Mode', () => {
    it('should render create form correctly', () => {
      render(<ClientModal {...defaultProps} />)

      expect(screen.getByText('新建客户')).toBeInTheDocument()
      expect(screen.getByLabelText('姓')).toBeInTheDocument()
      expect(screen.getByLabelText('名')).toBeInTheDocument()
      expect(screen.getByLabelText('电话')).toBeInTheDocument()
      expect(screen.getByLabelText('邮箱')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: '创建客户' })).toBeInTheDocument()
    })

    it('should validate required fields', async () => {
      const user = userEvent.setup()
      render(<ClientModal {...defaultProps} />)

      const submitButton = screen.getByRole('button', { name: '创建客户' })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('姓名为必填项')).toBeInTheDocument()
        expect(screen.getByText('电话为必填项')).toBeInTheDocument()
      })
    })

    it('should validate phone number format', async () => {
      const user = userEvent.setup()
      render(<ClientModal {...defaultProps} />)

      const phoneInput = screen.getByLabelText('电话')
      await user.type(phoneInput, 'invalid-phone')

      const submitButton = screen.getByRole('button', { name: '创建客户' })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('请输入有效的电话号码')).toBeInTheDocument()
      })
    })

    it('should validate email format', async () => {
      const user = userEvent.setup()
      render(<ClientModal {...defaultProps} />)

      const emailInput = screen.getByLabelText('邮箱')
      await user.type(emailInput, 'invalid-email')

      const submitButton = screen.getByRole('button', { name: '创建客户' })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('请输入有效的邮箱地址')).toBeInTheDocument()
      })
    })

    it('should submit form with valid data', async () => {
      const user = userEvent.setup()
      render(<ClientModal {...defaultProps} />)

      // Fill in the form
      await user.type(screen.getByLabelText('姓'), '张')
      await user.type(screen.getByLabelText('名'), '三')
      await user.type(screen.getByLabelText('电话'), '13800138000')
      await user.type(screen.getByLabelText('邮箱'), '<EMAIL>')

      const submitButton = screen.getByRole('button', { name: '创建客户' })
      await user.click(submitButton)

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/clients', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            first_name: '张',
            last_name: '三',
            phone: '13800138000',
            email: '<EMAIL>',
            status: 'active',
            preferred_language: 'zh-CN',
          }),
        })
      })

      expect(mockOnSuccess).toHaveBeenCalled()
      expect(mockOnClose).toHaveBeenCalled()
    })

    it('should handle API errors', async () => {
      const user = userEvent.setup()
      ;(global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        json: () => Promise.resolve({ error: 'Phone number already exists' }),
      })

      render(<ClientModal {...defaultProps} />)

      // Fill in the form
      await user.type(screen.getByLabelText('姓'), '张')
      await user.type(screen.getByLabelText('名'), '三')
      await user.type(screen.getByLabelText('电话'), '13800138000')

      const submitButton = screen.getByRole('button', { name: '创建客户' })
      await user.click(submitButton)

      await waitFor(() => {
        const toast = require('sonner').toast
        expect(toast.error).toHaveBeenCalledWith('创建客户失败')
      })
    })
  })

  describe('Edit Mode', () => {
    const existingClient = {
      id: '1',
      first_name: '李',
      last_name: '四',
      phone: '13900139000',
      email: '<EMAIL>',
      status: 'active',
      preferred_language: 'zh-CN',
    }

    it('should render edit form with existing data', () => {
      render(<ClientModal {...defaultProps} client={existingClient} />)

      expect(screen.getByText('编辑客户')).toBeInTheDocument()
      expect(screen.getByDisplayValue('李')).toBeInTheDocument()
      expect(screen.getByDisplayValue('四')).toBeInTheDocument()
      expect(screen.getByDisplayValue('13900139000')).toBeInTheDocument()
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: '更新客户' })).toBeInTheDocument()
    })

    it('should update client with modified data', async () => {
      const user = userEvent.setup()
      render(<ClientModal {...defaultProps} client={existingClient} />)

      // Modify the email
      const emailInput = screen.getByDisplayValue('<EMAIL>')
      await user.clear(emailInput)
      await user.type(emailInput, '<EMAIL>')

      const submitButton = screen.getByRole('button', { name: '更新客户' })
      await user.click(submitButton)

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/clients/1', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            first_name: '李',
            last_name: '四',
            phone: '13900139000',
            email: '<EMAIL>',
            status: 'active',
            preferred_language: 'zh-CN',
          }),
        })
      })

      expect(mockOnSuccess).toHaveBeenCalled()
      expect(mockOnClose).toHaveBeenCalled()
    })
  })

  describe('View Mode', () => {
    const existingClient = {
      id: '1',
      first_name: '王',
      last_name: '五',
      phone: '13700137000',
      email: '<EMAIL>',
      status: 'active',
      preferred_language: 'zh-CN',
    }

    it('should render view mode correctly', () => {
      render(<ClientModal {...defaultProps} client={existingClient} mode="view" />)

      expect(screen.getByText('客户详情')).toBeInTheDocument()
      expect(screen.getByText('王')).toBeInTheDocument()
      expect(screen.getByText('五')).toBeInTheDocument()
      expect(screen.getByText('13700137000')).toBeInTheDocument()
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      
      // Should not have form inputs
      expect(screen.queryByLabelText('姓')).not.toBeInTheDocument()
      expect(screen.queryByRole('button', { name: '创建客户' })).not.toBeInTheDocument()
      expect(screen.queryByRole('button', { name: '更新客户' })).not.toBeInTheDocument()
    })
  })

  describe('Modal Interactions', () => {
    it('should close modal when cancel button is clicked', async () => {
      const user = userEvent.setup()
      render(<ClientModal {...defaultProps} />)

      const cancelButton = screen.getByRole('button', { name: '取消' })
      await user.click(cancelButton)

      expect(mockOnClose).toHaveBeenCalled()
    })

    it('should close modal when X button is clicked', async () => {
      const user = userEvent.setup()
      render(<ClientModal {...defaultProps} />)

      const closeButton = screen.getByRole('button', { name: /close/i })
      await user.click(closeButton)

      expect(mockOnClose).toHaveBeenCalled()
    })

    it('should not render when isOpen is false', () => {
      render(<ClientModal {...defaultProps} isOpen={false} />)

      expect(screen.queryByText('新建客户')).not.toBeInTheDocument()
    })
  })

  describe('Loading States', () => {
    it('should show loading state during form submission', async () => {
      const user = userEvent.setup()
      
      // Mock a delayed response
      ;(global.fetch as jest.Mock).mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve({
          ok: true,
          json: () => Promise.resolve({ client: { id: '1' } })
        }), 100))
      )

      render(<ClientModal {...defaultProps} />)

      // Fill in required fields
      await user.type(screen.getByLabelText('姓'), '张')
      await user.type(screen.getByLabelText('名'), '三')
      await user.type(screen.getByLabelText('电话'), '13800138000')

      const submitButton = screen.getByRole('button', { name: '创建客户' })
      await user.click(submitButton)

      // Should show loading state
      expect(screen.getByText('创建中...')).toBeInTheDocument()
      expect(submitButton).toBeDisabled()
    })
  })
})
