import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { TreatmentModal } from '@/components/modals/TreatmentModal'

// Mock the toast function
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}))

// Mock fetch
global.fetch = jest.fn()

describe('TreatmentModal Component', () => {
  const mockOnClose = jest.fn()
  const mockOnSuccess = jest.fn()

  const defaultProps = {
    isOpen: true,
    onClose: mockOnClose,
    onSuccess: mockOnSuccess,
  }

  beforeEach(() => {
    jest.clearAllMocks()
    ;(global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ treatment: { id: '1', name: 'Test Treatment' } }),
    })
  })

  describe('Create Mode', () => {
    it('should render create form correctly', () => {
      render(<TreatmentModal {...defaultProps} />)

      expect(screen.getByText('新建治疗项目')).toBeInTheDocument()
      expect(screen.getByLabelText('英文名称')).toBeInTheDocument()
      expect(screen.getByLabelText('中文名称')).toBeInTheDocument()
      expect(screen.getByLabelText('默认价格')).toBeInTheDocument()
      expect(screen.getByLabelText('固定定金金额')).toBeInTheDocument()
      expect(screen.getByLabelText('咨询费')).toBeInTheDocument()
      expect(screen.getByLabelText('治疗时长 (分钟)')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: '创建治疗项目' })).toBeInTheDocument()
    })

    it('should validate required fields', async () => {
      const user = userEvent.setup()
      render(<TreatmentModal {...defaultProps} />)

      const submitButton = screen.getByRole('button', { name: '创建治疗项目' })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('英文名称为必填项')).toBeInTheDocument()
        expect(screen.getByText('中文名称为必填项')).toBeInTheDocument()
        expect(screen.getByText('默认价格为必填项')).toBeInTheDocument()
      })
    })

    it('should validate price is positive number', async () => {
      const user = userEvent.setup()
      render(<TreatmentModal {...defaultProps} />)

      await user.type(screen.getByLabelText('英文名称'), 'Test Treatment')
      await user.type(screen.getByLabelText('中文名称'), '测试治疗')
      await user.type(screen.getByLabelText('默认价格'), '-100')

      const submitButton = screen.getByRole('button', { name: '创建治疗项目' })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('价格必须大于0')).toBeInTheDocument()
      })
    })

    it('should validate duration is positive number', async () => {
      const user = userEvent.setup()
      render(<TreatmentModal {...defaultProps} />)

      await user.type(screen.getByLabelText('英文名称'), 'Test Treatment')
      await user.type(screen.getByLabelText('中文名称'), '测试治疗')
      await user.type(screen.getByLabelText('默认价格'), '100')
      await user.type(screen.getByLabelText('治疗时长 (分钟)'), '-30')

      const submitButton = screen.getByRole('button', { name: '创建治疗项目' })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('治疗时长必须大于0')).toBeInTheDocument()
      })
    })

    it('should submit form with valid data', async () => {
      const user = userEvent.setup()
      render(<TreatmentModal {...defaultProps} />)

      // Fill in the form
      await user.type(screen.getByLabelText('英文名称'), 'Facial Treatment')
      await user.type(screen.getByLabelText('中文名称'), '面部护理')
      await user.type(screen.getByLabelText('默认价格'), '299.99')
      await user.type(screen.getByLabelText('固定定金金额'), '100')
      await user.type(screen.getByLabelText('咨询费'), '50')
      await user.type(screen.getByLabelText('治疗时长 (分钟)'), '90')
      
      // Select category
      await user.click(screen.getByRole('combobox'))
      await user.click(screen.getByText('面部护理'))

      const submitButton = screen.getByRole('button', { name: '创建治疗项目' })
      await user.click(submitButton)

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/treatments', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: 'Facial Treatment',
            name_chinese: '面部护理',
            default_price: 299.99,
            fixed_deposit_amount: 100,
            consultation_fee: 50,
            duration_minutes: 90,
            category: '面部护理',
            is_active: true,
            requires_consultation: false,
          }),
        })
      })

      expect(mockOnSuccess).toHaveBeenCalled()
      expect(mockOnClose).toHaveBeenCalled()
    })

    it('should handle requires consultation checkbox', async () => {
      const user = userEvent.setup()
      render(<TreatmentModal {...defaultProps} />)

      // Fill in required fields
      await user.type(screen.getByLabelText('英文名称'), 'Consultation Treatment')
      await user.type(screen.getByLabelText('中文名称'), '咨询治疗')
      await user.type(screen.getByLabelText('默认价格'), '150')

      // Check the requires consultation checkbox
      const consultationCheckbox = screen.getByLabelText('需要咨询')
      await user.click(consultationCheckbox)

      const submitButton = screen.getByRole('button', { name: '创建治疗项目' })
      await user.click(submitButton)

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/treatments', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: expect.objectContaining({
            requires_consultation: true,
          }),
        })
      })
    })
  })

  describe('Edit Mode', () => {
    const existingTreatment = {
      id: '1',
      name: 'Existing Treatment',
      name_chinese: '现有治疗',
      default_price: 199.99,
      fixed_deposit_amount: 80,
      consultation_fee: 30,
      duration_minutes: 60,
      category: '身体护理',
      is_active: true,
      requires_consultation: false,
    }

    it('should render edit form with existing data', () => {
      render(<TreatmentModal {...defaultProps} treatment={existingTreatment} />)

      expect(screen.getByText('编辑治疗项目')).toBeInTheDocument()
      expect(screen.getByDisplayValue('Existing Treatment')).toBeInTheDocument()
      expect(screen.getByDisplayValue('现有治疗')).toBeInTheDocument()
      expect(screen.getByDisplayValue('199.99')).toBeInTheDocument()
      expect(screen.getByDisplayValue('80')).toBeInTheDocument()
      expect(screen.getByDisplayValue('30')).toBeInTheDocument()
      expect(screen.getByDisplayValue('60')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: '更新治疗项目' })).toBeInTheDocument()
    })

    it('should update treatment with modified data', async () => {
      const user = userEvent.setup()
      render(<TreatmentModal {...defaultProps} treatment={existingTreatment} />)

      // Modify the price
      const priceInput = screen.getByDisplayValue('199.99')
      await user.clear(priceInput)
      await user.type(priceInput, '249.99')

      const submitButton = screen.getByRole('button', { name: '更新治疗项目' })
      await user.click(submitButton)

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/treatments/1', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: expect.objectContaining({
            default_price: 249.99,
          }),
        })
      })

      expect(mockOnSuccess).toHaveBeenCalled()
      expect(mockOnClose).toHaveBeenCalled()
    })
  })

  describe('View Mode', () => {
    const existingTreatment = {
      id: '1',
      name: 'View Treatment',
      name_chinese: '查看治疗',
      default_price: 299.99,
      fixed_deposit_amount: 100,
      consultation_fee: 50,
      duration_minutes: 90,
      category: '面部护理',
      is_active: true,
      requires_consultation: true,
    }

    it('should render view mode correctly', () => {
      render(<TreatmentModal {...defaultProps} treatment={existingTreatment} mode="view" />)

      expect(screen.getByText('治疗项目详情')).toBeInTheDocument()
      expect(screen.getByText('View Treatment')).toBeInTheDocument()
      expect(screen.getByText('查看治疗')).toBeInTheDocument()
      expect(screen.getByText('¥299.99')).toBeInTheDocument()
      expect(screen.getByText('¥100.00')).toBeInTheDocument()
      expect(screen.getByText('¥50.00')).toBeInTheDocument()
      expect(screen.getByText('90 分钟')).toBeInTheDocument()
      expect(screen.getByText('面部护理')).toBeInTheDocument()
      expect(screen.getByText('是')).toBeInTheDocument() // requires_consultation

      // Should not have form inputs
      expect(screen.queryByLabelText('英文名称')).not.toBeInTheDocument()
      expect(screen.queryByRole('button', { name: '创建治疗项目' })).not.toBeInTheDocument()
    })

    it('should show inactive status correctly', () => {
      const inactiveTreatment = { ...existingTreatment, is_active: false }
      render(<TreatmentModal {...defaultProps} treatment={inactiveTreatment} mode="view" />)

      expect(screen.getByText('非活跃')).toBeInTheDocument()
    })
  })

  describe('Category Selection', () => {
    it('should show all available categories', async () => {
      const user = userEvent.setup()
      render(<TreatmentModal {...defaultProps} />)

      const categorySelect = screen.getByRole('combobox')
      await user.click(categorySelect)

      // Check for expected categories
      expect(screen.getByText('面部护理')).toBeInTheDocument()
      expect(screen.getByText('身体护理')).toBeInTheDocument()
      expect(screen.getByText('注射类')).toBeInTheDocument()
      expect(screen.getByText('激光类')).toBeInTheDocument()
      expect(screen.getByText('其他')).toBeInTheDocument()
    })

    it('should select category correctly', async () => {
      const user = userEvent.setup()
      render(<TreatmentModal {...defaultProps} />)

      const categorySelect = screen.getByRole('combobox')
      await user.click(categorySelect)
      await user.click(screen.getByText('注射类'))

      expect(screen.getByText('注射类')).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('should handle API errors', async () => {
      const user = userEvent.setup()
      ;(global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        json: () => Promise.resolve({ error: 'Treatment name already exists' }),
      })

      render(<TreatmentModal {...defaultProps} />)

      // Fill in the form
      await user.type(screen.getByLabelText('英文名称'), 'Duplicate Treatment')
      await user.type(screen.getByLabelText('中文名称'), '重复治疗')
      await user.type(screen.getByLabelText('默认价格'), '100')

      const submitButton = screen.getByRole('button', { name: '创建治疗项目' })
      await user.click(submitButton)

      await waitFor(() => {
        const toast = require('sonner').toast
        expect(toast.error).toHaveBeenCalledWith('创建治疗项目失败')
      })
    })

    it('should handle network errors', async () => {
      const user = userEvent.setup()
      ;(global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'))

      render(<TreatmentModal {...defaultProps} />)

      // Fill in the form
      await user.type(screen.getByLabelText('英文名称'), 'Network Test')
      await user.type(screen.getByLabelText('中文名称'), '网络测试')
      await user.type(screen.getByLabelText('默认价格'), '100')

      const submitButton = screen.getByRole('button', { name: '创建治疗项目' })
      await user.click(submitButton)

      await waitFor(() => {
        const toast = require('sonner').toast
        expect(toast.error).toHaveBeenCalledWith('创建治疗项目失败')
      })
    })
  })

  describe('Loading States', () => {
    it('should show loading state during form submission', async () => {
      const user = userEvent.setup()
      
      // Mock a delayed response
      ;(global.fetch as jest.Mock).mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve({
          ok: true,
          json: () => Promise.resolve({ treatment: { id: '1' } })
        }), 100))
      )

      render(<TreatmentModal {...defaultProps} />)

      // Fill in required fields
      await user.type(screen.getByLabelText('英文名称'), 'Loading Test')
      await user.type(screen.getByLabelText('中文名称'), '加载测试')
      await user.type(screen.getByLabelText('默认价格'), '100')

      const submitButton = screen.getByRole('button', { name: '创建治疗项目' })
      await user.click(submitButton)

      // Should show loading state
      expect(screen.getByText('创建中...')).toBeInTheDocument()
      expect(submitButton).toBeDisabled()
    })
  })
})
