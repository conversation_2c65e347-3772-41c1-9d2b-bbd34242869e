/**
 * Comprehensive Unit Tests for Error Handling System
 * Tests all error classes, utilities, and error handling patterns
 */

import { z } from 'zod'
import {
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  InternalServerError,
  DatabaseError,
  ExternalServiceError,
  createErrorResponse,
  isOperationalError,
  normalizeError,
  handleApiError,
  asyncHandler,
  success,
  failure,
  safeAsync,
} from '../../src/lib/errors'

describe('Error Handling System', () => {
  describe('AppError Base Class', () => {
    class TestError extends AppError {
      readonly statusCode = 400
      readonly errorCode = 'TEST_ERROR'
      readonly isOperational = true
    }

    it('should create error with message and context', () => {
      const context = { userId: '123', action: 'test' }
      const error = new TestError('Test error message', context)

      expect(error.message).toBe('Test error message')
      expect(error.context).toEqual(context)
      expect(error.name).toBe('TestError')
      expect(error.statusCode).toBe(400)
      expect(error.errorCode).toBe('TEST_ERROR')
      expect(error.isOperational).toBe(true)
    })

    it('should maintain proper stack trace', () => {
      const error = new TestError('Test error')
      expect(error.stack).toBeDefined()
      expect(error.stack).toContain('TestError')
    })
  })

  describe('Specific Error Classes', () => {
    it('should create ValidationError with proper properties', () => {
      const zodError = new z.ZodError([
        {
          code: 'invalid_type',
          expected: 'string',
          received: 'number',
          path: ['name'],
          message: 'Expected string, received number',
        },
      ])

      const error = new ValidationError('Validation failed', zodError, { field: 'name' })

      expect(error.statusCode).toBe(400)
      expect(error.errorCode).toBe('VALIDATION_ERROR')
      expect(error.isOperational).toBe(true)
      expect(error.validationErrors).toBe(zodError)
      expect(error.context).toEqual({ field: 'name' })
    })

    it('should create AuthenticationError with proper properties', () => {
      const error = new AuthenticationError('Invalid credentials')

      expect(error.statusCode).toBe(401)
      expect(error.errorCode).toBe('AUTHENTICATION_ERROR')
      expect(error.isOperational).toBe(true)
    })

    it('should create AuthorizationError with proper properties', () => {
      const error = new AuthorizationError('Access denied')

      expect(error.statusCode).toBe(403)
      expect(error.errorCode).toBe('AUTHORIZATION_ERROR')
      expect(error.isOperational).toBe(true)
    })

    it('should create NotFoundError with proper properties', () => {
      const error = new NotFoundError('Resource not found')

      expect(error.statusCode).toBe(404)
      expect(error.errorCode).toBe('NOT_FOUND_ERROR')
      expect(error.isOperational).toBe(true)
    })

    it('should create ConflictError with proper properties', () => {
      const error = new ConflictError('Resource already exists')

      expect(error.statusCode).toBe(409)
      expect(error.errorCode).toBe('CONFLICT_ERROR')
      expect(error.isOperational).toBe(true)
    })

    it('should create RateLimitError with proper properties', () => {
      const error = new RateLimitError('Too many requests')

      expect(error.statusCode).toBe(429)
      expect(error.errorCode).toBe('RATE_LIMIT_ERROR')
      expect(error.isOperational).toBe(true)
    })

    it('should create InternalServerError with proper properties', () => {
      const error = new InternalServerError('Internal server error')

      expect(error.statusCode).toBe(500)
      expect(error.errorCode).toBe('INTERNAL_SERVER_ERROR')
      expect(error.isOperational).toBe(false)
    })

    it('should create DatabaseError with proper properties', () => {
      const error = new DatabaseError('Database connection failed')

      expect(error.statusCode).toBe(500)
      expect(error.errorCode).toBe('DATABASE_ERROR')
      expect(error.isOperational).toBe(false)
    })

    it('should create ExternalServiceError with proper properties', () => {
      const error = new ExternalServiceError('External service unavailable')

      expect(error.statusCode).toBe(502)
      expect(error.errorCode).toBe('EXTERNAL_SERVICE_ERROR')
      expect(error.isOperational).toBe(false)
    })
  })

  describe('Error Response Creation', () => {
    it('should create standardized error response', () => {
      const error = new ValidationError('Validation failed', undefined, { field: 'email' })
      const requestId = 'req-123'
      const response = createErrorResponse(error, requestId)

      expect(response.error.code).toBe('VALIDATION_ERROR')
      expect(response.error.message).toBe('Validation failed')
      expect(response.error.details).toEqual({ field: 'email' })
      expect(response.error.requestId).toBe(requestId)
      expect(response.error.timestamp).toBeDefined()
      expect(new Date(response.error.timestamp)).toBeInstanceOf(Date)
    })

    it('should create error response without request ID', () => {
      const error = new NotFoundError('Not found')
      const response = createErrorResponse(error)

      expect(response.error.code).toBe('NOT_FOUND_ERROR')
      expect(response.error.requestId).toBeUndefined()
    })
  })

  describe('Error Classification', () => {
    it('should identify operational errors', () => {
      const operationalError = new ValidationError('Validation failed')
      const programmingError = new InternalServerError('Programming error')

      expect(isOperationalError(operationalError)).toBe(true)
      expect(isOperationalError(programmingError)).toBe(false)
      expect(isOperationalError(new Error('Generic error'))).toBe(false)
    })
  })

  describe('Error Normalization', () => {
    it('should return AppError as-is', () => {
      const appError = new ValidationError('Test error')
      const normalized = normalizeError(appError)

      expect(normalized).toBe(appError)
    })

    it('should convert ZodError to ValidationError', () => {
      const zodError = new z.ZodError([
        {
          code: 'invalid_type',
          expected: 'string',
          received: 'number',
          path: ['name'],
          message: 'Expected string, received number',
        },
      ])

      const normalized = normalizeError(zodError)

      expect(normalized).toBeInstanceOf(ValidationError)
      expect(normalized.message).toBe('输入数据验证失败')
      expect((normalized as ValidationError).validationErrors).toBe(zodError)
    })

    it('should convert database errors', () => {
      const dbError = new Error('Database error') as Error & { code: string }
      dbError.code = '23505' // Unique constraint violation

      const normalized = normalizeError(dbError)

      expect(normalized).toBeInstanceOf(ConflictError)
      expect(normalized.message).toBe('数据已存在，请检查重复项')
    })

    it('should handle foreign key constraint violation', () => {
      const dbError = new Error('FK violation') as Error & { code: string }
      dbError.code = '23503'

      const normalized = normalizeError(dbError)

      expect(normalized).toBeInstanceOf(ValidationError)
      expect(normalized.message).toBe('关联数据不存在')
    })

    it('should handle not null constraint violation', () => {
      const dbError = new Error('Not null violation') as Error & { code: string }
      dbError.code = '23502'

      const normalized = normalizeError(dbError)

      expect(normalized).toBeInstanceOf(ValidationError)
      expect(normalized.message).toBe('必填字段不能为空')
    })

    it('should convert generic Error to InternalServerError', () => {
      const genericError = new Error('Generic error')
      const normalized = normalizeError(genericError)

      expect(normalized).toBeInstanceOf(InternalServerError)
      expect(normalized.message).toBe('Generic error')
    })

    it('should handle unknown error types', () => {
      const unknownError = 'string error'
      const normalized = normalizeError(unknownError)

      expect(normalized).toBeInstanceOf(InternalServerError)
      expect(normalized.message).toBe('未知错误')
    })
  })

  describe('API Error Handler', () => {
    it('should handle operational errors', () => {
      const error = new ValidationError('Validation failed')
      const result = handleApiError(error)

      expect(result.status).toBe(400)
      expect(result.body.error.code).toBe('VALIDATION_ERROR')
      expect(result.body.error.message).toBe('Validation failed')
    })

    it('should handle non-operational errors', () => {
      const error = new InternalServerError('Internal error')
      const result = handleApiError(error)

      expect(result.status).toBe(500)
      expect(result.body.error.code).toBe('INTERNAL_SERVER_ERROR')
    })

    it('should normalize unknown errors', () => {
      const error = new Error('Unknown error')
      const result = handleApiError(error)

      expect(result.status).toBe(500)
      expect(result.body.error.code).toBe('INTERNAL_SERVER_ERROR')
    })
  })

  describe('Async Handler', () => {
    it('should handle successful async operations', async () => {
      const successFn = async (value: number) => value * 2
      const wrappedFn = asyncHandler(successFn)

      const result = await wrappedFn(5)
      expect(result).toBe(10)
    })

    it('should normalize errors from async operations', async () => {
      const errorFn = async () => {
        throw new Error('Async error')
      }
      const wrappedFn = asyncHandler(errorFn)

      await expect(wrappedFn()).rejects.toBeInstanceOf(InternalServerError)
    })
  })

  describe('Result Pattern', () => {
    it('should create success result', () => {
      const data = { id: 1, name: 'Test' }
      const result = success(data)

      expect(result.success).toBe(true)
      expect(result.data).toEqual(data)
    })

    it('should create failure result', () => {
      const error = new ValidationError('Validation failed')
      const result = failure(error)

      expect(result.success).toBe(false)
      expect(result.error).toBe(error)
    })
  })

  describe('Safe Async', () => {
    it('should return success result for successful operations', async () => {
      const successFn = async () => ({ id: 1, name: 'Test' })
      const result = await safeAsync(successFn)

      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data).toEqual({ id: 1, name: 'Test' })
      }
    })

    it('should return failure result for failed operations', async () => {
      const errorFn = async () => {
        throw new ValidationError('Validation failed')
      }
      const result = await safeAsync(errorFn)

      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error).toBeInstanceOf(ValidationError)
      }
    })

    it('should normalize unknown errors', async () => {
      const errorFn = async () => {
        throw new Error('Unknown error')
      }
      const result = await safeAsync(errorFn)

      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error).toBeInstanceOf(InternalServerError)
      }
    })
  })
})
