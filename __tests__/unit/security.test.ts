/**
 * Comprehensive Unit Tests for Security System
 * Tests encryption, rate limiting, audit logging, and security utilities
 */

import { NextRequest } from 'next/server'
import {
  Encryption,
  RateLimiter,
  AuditLogger,
  InputSanitizer,
  SecurityHeaders,
  CSRFProtection,
  SessionSecurity,
  DataMasking,
} from '../../src/lib/security'
import { RateLimitError } from '../../src/lib/errors'

// Mock environment variables for testing
const originalEnv = process.env
beforeEach(() => {
  process.env = {
    ...originalEnv,
    NODE_ENV: 'test',
    DATABASE_ENCRYPTION_KEY: 'a'.repeat(64), // 32 bytes in hex
  }
})

afterEach(() => {
  process.env = originalEnv
})

describe('Security System', () => {
  describe('Encryption', () => {
    describe('encrypt and decrypt', () => {
      it('should encrypt and decrypt text correctly', () => {
        const originalText = 'sensitive medical data'
        const encrypted = Encryption.encrypt(originalText)
        const decrypted = Encryption.decrypt(encrypted)

        expect(decrypted).toBe(originalText)
        expect(encrypted).not.toBe(originalText)
        expect(encrypted).toContain(':') // Should have IV:tag:data format
      })

      it('should produce different encrypted values for same input', () => {
        const text = 'same input'
        const encrypted1 = Encryption.encrypt(text)
        const encrypted2 = Encryption.encrypt(text)

        expect(encrypted1).not.toBe(encrypted2)
        expect(Encryption.decrypt(encrypted1)).toBe(text)
        expect(Encryption.decrypt(encrypted2)).toBe(text)
      })

      it('should handle empty strings', () => {
        const empty = ''
        const encrypted = Encryption.encrypt(empty)
        const decrypted = Encryption.decrypt(encrypted)

        expect(decrypted).toBe(empty)
      })

      it('should handle Unicode characters', () => {
        const unicode = '测试数据 🔒 emoji'
        const encrypted = Encryption.encrypt(unicode)
        const decrypted = Encryption.decrypt(encrypted)

        expect(decrypted).toBe(unicode)
      })

      it('should throw error for invalid encrypted data format', () => {
        const invalidEncrypted = 'invalid:format'

        expect(() => Encryption.decrypt(invalidEncrypted)).toThrow('Failed to decrypt data')
      })

      it('should throw error for corrupted encrypted data', () => {
        const text = 'test'
        const encrypted = Encryption.encrypt(text)
        const corrupted = encrypted.replace(/.$/, 'x') // Change last character

        expect(() => Encryption.decrypt(corrupted)).toThrow('Failed to decrypt data')
      })
    })

    describe('password hashing', () => {
      it('should hash passwords with salt', () => {
        const password = 'securePassword123'
        const hashed = Encryption.hashPassword(password)

        expect(hashed).toContain(':')
        expect(hashed).not.toBe(password)
        expect(hashed.length).toBeGreaterThan(100) // Salt + hash should be long
      })

      it('should verify correct passwords', () => {
        const password = 'securePassword123'
        const hashed = Encryption.hashPassword(password)

        expect(Encryption.verifyPassword(password, hashed)).toBe(true)
      })

      it('should reject incorrect passwords', () => {
        const password = 'securePassword123'
        const wrongPassword = 'wrongPassword'
        const hashed = Encryption.hashPassword(password)

        expect(Encryption.verifyPassword(wrongPassword, hashed)).toBe(false)
      })

      it('should handle invalid hash format', () => {
        const password = 'test'
        const invalidHash = 'invalid-hash-format'

        expect(Encryption.verifyPassword(password, invalidHash)).toBe(false)
      })

      it('should produce different hashes for same password', () => {
        const password = 'samePassword'
        const hash1 = Encryption.hashPassword(password)
        const hash2 = Encryption.hashPassword(password)

        expect(hash1).not.toBe(hash2)
        expect(Encryption.verifyPassword(password, hash1)).toBe(true)
        expect(Encryption.verifyPassword(password, hash2)).toBe(true)
      })
    })
  })

  describe('Rate Limiter', () => {
    beforeEach(() => {
      // Clear rate limiter state before each test
      RateLimiter['requests'].clear()
    })

    describe('checkRateLimit', () => {
      it('should allow requests within limit', async () => {
        const identifier = 'test-user'
        const limit = 5
        const windowMs = 60000

        const result = await RateLimiter.checkRateLimit(identifier, limit, windowMs)

        expect(result.allowed).toBe(true)
        expect(result.remaining).toBe(4)
        expect(result.resetTime).toBeGreaterThan(Date.now())
      })

      it('should track multiple requests', async () => {
        const identifier = 'test-user'
        const limit = 3
        const windowMs = 60000

        // First request
        const result1 = await RateLimiter.checkRateLimit(identifier, limit, windowMs)
        expect(result1.allowed).toBe(true)
        expect(result1.remaining).toBe(2)

        // Second request
        const result2 = await RateLimiter.checkRateLimit(identifier, limit, windowMs)
        expect(result2.allowed).toBe(true)
        expect(result2.remaining).toBe(1)

        // Third request
        const result3 = await RateLimiter.checkRateLimit(identifier, limit, windowMs)
        expect(result3.allowed).toBe(true)
        expect(result3.remaining).toBe(0)
      })

      it('should block requests exceeding limit', async () => {
        const identifier = 'test-user'
        const limit = 2
        const windowMs = 60000

        // Use up the limit
        await RateLimiter.checkRateLimit(identifier, limit, windowMs)
        await RateLimiter.checkRateLimit(identifier, limit, windowMs)

        // This should be blocked
        const result = await RateLimiter.checkRateLimit(identifier, limit, windowMs)
        expect(result.allowed).toBe(false)
        expect(result.remaining).toBe(0)
      })

      it('should reset after window expires', async () => {
        const identifier = 'test-user'
        const limit = 1
        const windowMs = 100 // Very short window

        // Use up the limit
        await RateLimiter.checkRateLimit(identifier, limit, windowMs)

        // Wait for window to expire
        await new Promise(resolve => setTimeout(resolve, 150))

        // Should be allowed again
        const result = await RateLimiter.checkRateLimit(identifier, limit, windowMs)
        expect(result.allowed).toBe(true)
        expect(result.remaining).toBe(0)
      })

      it('should handle different identifiers separately', async () => {
        const limit = 1
        const windowMs = 60000

        const result1 = await RateLimiter.checkRateLimit('user1', limit, windowMs)
        const result2 = await RateLimiter.checkRateLimit('user2', limit, windowMs)

        expect(result1.allowed).toBe(true)
        expect(result2.allowed).toBe(true)
      })
    })

    describe('middleware', () => {
      it('should allow requests within limit', async () => {
        const request = new NextRequest('http://localhost:3000/api/test', {
          headers: { 'x-forwarded-for': '***********' },
        })

        await expect(RateLimiter.middleware(request, 100, 60000)).resolves.not.toThrow()
      })

      it('should throw RateLimitError when limit exceeded', async () => {
        const request = new NextRequest('http://localhost:3000/api/test', {
          headers: { 'x-forwarded-for': '***********' },
        })

        const limit = 1
        const windowMs = 60000

        // First request should pass
        await expect(RateLimiter.middleware(request, limit, windowMs)).resolves.not.toThrow()

        // Second request should fail
        await expect(RateLimiter.middleware(request, limit, windowMs)).rejects.toThrow(RateLimitError)
      })

      it('should handle missing IP headers', async () => {
        const request = new NextRequest('http://localhost:3000/api/test')

        await expect(RateLimiter.middleware(request, 100, 60000)).resolves.not.toThrow()
      })
    })
  })

  describe('Input Sanitizer', () => {
    describe('sanitizeHtml', () => {
      it('should escape HTML characters', () => {
        const input = '<script>alert("xss")</script>'
        const expected = '&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;'

        expect(InputSanitizer.sanitizeHtml(input)).toBe(expected)
      })

      it('should handle all dangerous characters', () => {
        const input = `<>"'&/`
        const expected = '&lt;&gt;&quot;&#x27;&amp;&#x2F;'

        expect(InputSanitizer.sanitizeHtml(input)).toBe(expected)
      })

      it('should handle empty strings', () => {
        expect(InputSanitizer.sanitizeHtml('')).toBe('')
      })
    })

    describe('sanitizeSql', () => {
      it('should escape SQL injection characters', () => {
        const input = "'; DROP TABLE users; --"
        const result = InputSanitizer.sanitizeSql(input)

        expect(result).not.toContain(';')
        expect(result).not.toContain('--')
        expect(result).toContain("''") // Escaped single quote
      })

      it('should remove SQL comments', () => {
        const input = 'SELECT * FROM users /* comment */'
        const result = InputSanitizer.sanitizeSql(input)

        expect(result).not.toContain('/*')
        expect(result).not.toContain('*/')
      })
    })

    describe('sanitizePhone', () => {
      it('should keep only valid phone characters', () => {
        const input = '+**************** ext. 890'
        const result = InputSanitizer.sanitizePhone(input)

        expect(result).toBe('+****************  890')
        expect(result).not.toContain('ext.')
      })
    })

    describe('sanitizeEmail', () => {
      it('should convert to lowercase and trim', () => {
        const input = '  <EMAIL>  '
        const result = InputSanitizer.sanitizeEmail(input)

        expect(result).toBe('<EMAIL>')
      })
    })
  })

  describe('Security Headers', () => {
    it('should return comprehensive security headers', () => {
      const headers = SecurityHeaders.getHeaders()

      expect(headers['X-Content-Type-Options']).toBe('nosniff')
      expect(headers['X-Frame-Options']).toBe('DENY')
      expect(headers['X-XSS-Protection']).toBe('1; mode=block')
      expect(headers['Referrer-Policy']).toBe('strict-origin-when-cross-origin')
      expect(headers['Strict-Transport-Security']).toContain('max-age=31536000')
      expect(headers['Content-Security-Policy']).toContain("default-src 'self'")
    })
  })

  describe('CSRF Protection', () => {
    describe('generateToken and verifyToken', () => {
      it('should generate and verify valid tokens', () => {
        const sessionId = 'test-session-123'
        const token = CSRFProtection.generateToken(sessionId)

        expect(typeof token).toBe('string')
        expect(token.length).toBeGreaterThan(0)
        expect(CSRFProtection.verifyToken(token, sessionId)).toBe(true)
      })

      it('should reject tokens for different sessions', () => {
        const sessionId1 = 'session-1'
        const sessionId2 = 'session-2'
        const token = CSRFProtection.generateToken(sessionId1)

        expect(CSRFProtection.verifyToken(token, sessionId2)).toBe(false)
      })

      it('should reject invalid token format', () => {
        const sessionId = 'test-session'
        const invalidToken = 'invalid-token'

        expect(CSRFProtection.verifyToken(invalidToken, sessionId)).toBe(false)
      })

      it('should reject expired tokens', () => {
        // This test would require mocking time or using a very short expiry
        // For now, we'll test the structure
        const sessionId = 'test-session'
        const token = CSRFProtection.generateToken(sessionId)

        expect(CSRFProtection.verifyToken(token, sessionId)).toBe(true)
      })
    })
  })

  describe('Session Security', () => {
    describe('generateSessionId', () => {
      it('should generate valid session IDs', () => {
        const sessionId = SessionSecurity.generateSessionId()

        expect(typeof sessionId).toBe('string')
        expect(sessionId.length).toBe(64) // 32 bytes in hex
        expect(/^[a-f0-9]{64}$/.test(sessionId)).toBe(true)
      })

      it('should generate unique session IDs', () => {
        const id1 = SessionSecurity.generateSessionId()
        const id2 = SessionSecurity.generateSessionId()

        expect(id1).not.toBe(id2)
      })
    })

    describe('isValidSessionId', () => {
      it('should validate correct session ID format', () => {
        const validId = 'a'.repeat(64)
        expect(SessionSecurity.isValidSessionId(validId)).toBe(true)
      })

      it('should reject invalid session ID formats', () => {
        const invalidIds = [
          'too-short',
          'a'.repeat(63), // too short
          'a'.repeat(65), // too long
          'g'.repeat(64), // invalid character
          'A'.repeat(64), // uppercase
        ]

        invalidIds.forEach(id => {
          expect(SessionSecurity.isValidSessionId(id)).toBe(false)
        })
      })
    })
  })

  describe('Data Masking', () => {
    describe('maskSensitiveData', () => {
      it('should mask sensitive fields', () => {
        const data = {
          name: 'John Doe',
          password: 'secret123',
          phone: '1234567890',
          email: '<EMAIL>',
          ssn: '***********',
          public_info: 'not sensitive',
        }

        const masked = DataMasking.maskSensitiveData(data)

        expect(masked.name).toBe('John Doe') // not sensitive
        expect(masked.password).toBe('***MASKED***')
        expect(masked.phone).toBe('12******90') // partially masked
        expect(masked.email).toBe('jo**@example.com') // partially masked
        expect(masked.ssn).toBe('***MASKED***')
        expect(masked.public_info).toBe('not sensitive')
      })

      it('should handle non-object inputs', () => {
        expect(DataMasking.maskSensitiveData('string')).toBe('string')
        expect(DataMasking.maskSensitiveData(123)).toBe(123)
        expect(DataMasking.maskSensitiveData(null)).toBe(null)
        expect(DataMasking.maskSensitiveData(undefined)).toBe(undefined)
      })

      it('should handle short values', () => {
        const data = {
          phone: '123',
          email: 'a@b',
        }

        const masked = DataMasking.maskSensitiveData(data)

        expect(masked.phone).toBe('***') // too short for partial masking
        expect(masked.email).toBe('****') // too short for partial masking
      })

      it('should not modify original object', () => {
        const original = {
          name: 'John',
          password: 'secret',
        }

        const masked = DataMasking.maskSensitiveData(original)

        expect(original.password).toBe('secret') // unchanged
        expect(masked.password).toBe('***MASKED***')
      })
    })
  })
})
