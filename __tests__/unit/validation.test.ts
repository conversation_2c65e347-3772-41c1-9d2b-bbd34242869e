/**
 * Comprehensive Unit Tests for Validation System
 * Tests all validation schemas, utilities, and sanitization functions
 */

import { z } from 'zod'
import {
  commonSchemas,
  clientSchemas,
  treatmentSchemas,
  appointmentSchemas,
  invoiceSchemas,
  paymentSchemas,
  validateRequest,
  validateQuery,
  sanitizeHtml,
  sanitizeText,
  safeValidate,
} from '../../src/lib/validation'
import { ValidationError } from '../../src/lib/errors'

describe('Validation System', () => {
  describe('Common Schemas', () => {
    describe('UUID validation', () => {
      it('should validate correct UUID format', () => {
        const validUuid = '123e4567-e89b-12d3-a456-************'
        expect(() => commonSchemas.uuid.parse(validUuid)).not.toThrow()
      })

      it('should reject invalid UUID format', () => {
        const invalidUuid = 'not-a-uuid'
        expect(() => commonSchemas.uuid.parse(invalidUuid)).toThrow()
      })
    })

    describe('Email validation', () => {
      it('should validate correct email formats', () => {
        const validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ]

        validEmails.forEach(email => {
          expect(() => commonSchemas.email.parse(email)).not.toThrow()
        })
      })

      it('should reject invalid email formats', () => {
        const invalidEmails = [
          'invalid-email',
          '@domain.com',
          'user@',
          'user@domain',
        ]

        invalidEmails.forEach(email => {
          expect(() => commonSchemas.email.parse(email)).toThrow()
        })
      })
    })

    describe('Phone validation', () => {
      it('should validate correct phone formats', () => {
        const validPhones = [
          '1234567890',
          '+1234567890',
          '+86138000000000',
          '8613800000000',
        ]

        validPhones.forEach(phone => {
          expect(() => commonSchemas.phone.parse(phone)).not.toThrow()
        })
      })

      it('should reject invalid phone formats', () => {
        const invalidPhones = [
          'abc123',
          '************',
          '(*************',
          '+',
          '0123456789', // starts with 0
        ]

        invalidPhones.forEach(phone => {
          expect(() => commonSchemas.phone.parse(phone)).toThrow()
        })
      })
    })

    describe('Date validation', () => {
      it('should validate correct date format', () => {
        const validDates = ['2023-12-25', '2024-01-01', '2025-02-29']

        validDates.forEach(date => {
          expect(() => commonSchemas.date.parse(date)).not.toThrow()
        })
      })

      it('should reject invalid date formats', () => {
        const invalidDates = [
          '25-12-2023',
          '2023/12/25',
          '2023-13-01',
          '2023-12-32',
          'invalid-date',
        ]

        invalidDates.forEach(date => {
          expect(() => commonSchemas.date.parse(date)).toThrow()
        })
      })
    })

    describe('Time validation', () => {
      it('should validate correct time format', () => {
        const validTimes = ['09:00', '23:59', '00:00', '12:30']

        validTimes.forEach(time => {
          expect(() => commonSchemas.time.parse(time)).not.toThrow()
        })
      })

      it('should reject invalid time formats', () => {
        const invalidTimes = [
          '24:00',
          '09:60',
          '9:00',
          '09:0',
          'invalid-time',
        ]

        invalidTimes.forEach(time => {
          expect(() => commonSchemas.time.parse(time)).toThrow()
        })
      })
    })

    describe('Currency validation', () => {
      it('should validate positive currency amounts', () => {
        const validAmounts = [0, 10.50, 999999.99, 100]

        validAmounts.forEach(amount => {
          expect(() => commonSchemas.currency.parse(amount)).not.toThrow()
        })
      })

      it('should reject invalid currency amounts', () => {
        const invalidAmounts = [-10, 1000000, -0.01]

        invalidAmounts.forEach(amount => {
          expect(() => commonSchemas.currency.parse(amount)).toThrow()
        })
      })
    })

    describe('Pagination validation', () => {
      it('should validate correct pagination parameters', () => {
        const validPagination = { page: 1, limit: 20 }
        const result = commonSchemas.pagination.parse(validPagination)

        expect(result.page).toBe(1)
        expect(result.limit).toBe(20)
      })

      it('should apply default values', () => {
        const result = commonSchemas.pagination.parse({})

        expect(result.page).toBe(1)
        expect(result.limit).toBe(20)
      })

      it('should reject invalid pagination parameters', () => {
        const invalidPagination = [
          { page: 0, limit: 20 },
          { page: 1, limit: 0 },
          { page: 1, limit: 101 },
        ]

        invalidPagination.forEach(pagination => {
          expect(() => commonSchemas.pagination.parse(pagination)).toThrow()
        })
      })
    })
  })

  describe('Client Schemas', () => {
    describe('Client creation validation', () => {
      it('should validate correct client data', () => {
        const validClient = {
          first_name: 'John',
          last_name: 'Doe',
          phone: '1234567890',
          email: '<EMAIL>',
          date_of_birth: '1990-01-01',
          gender: 'male' as const,
          address: '123 Main St',
          emergency_contact_name: 'Jane Doe',
          emergency_contact_phone: '0987654321',
          referral_source: 'Google',
          notes: 'Test notes',
        }

        expect(() => clientSchemas.create.parse(validClient)).not.toThrow()
      })

      it('should require mandatory fields', () => {
        const incompleteClient = {
          first_name: 'John',
          // missing last_name and phone
        }

        expect(() => clientSchemas.create.parse(incompleteClient)).toThrow()
      })

      it('should validate field lengths', () => {
        const clientWithLongFields = {
          first_name: 'a'.repeat(51), // too long
          last_name: 'Doe',
          phone: '1234567890',
        }

        expect(() => clientSchemas.create.parse(clientWithLongFields)).toThrow()
      })

      it('should validate optional fields', () => {
        const minimalClient = {
          first_name: 'John',
          last_name: 'Doe',
          phone: '1234567890',
        }

        expect(() => clientSchemas.create.parse(minimalClient)).not.toThrow()
      })
    })

    describe('Client update validation', () => {
      it('should allow partial updates', () => {
        const partialUpdate = {
          first_name: 'Jane',
          email: '<EMAIL>',
        }

        expect(() => clientSchemas.update.parse(partialUpdate)).not.toThrow()
      })

      it('should validate status field', () => {
        const updateWithStatus = {
          status: 'active' as const,
        }

        expect(() => clientSchemas.update.parse(updateWithStatus)).not.toThrow()

        const updateWithInvalidStatus = {
          status: 'invalid-status',
        }

        expect(() => clientSchemas.update.parse(updateWithInvalidStatus)).toThrow()
      })
    })
  })

  describe('Treatment Schemas', () => {
    describe('Treatment creation validation', () => {
      it('should validate correct treatment data', () => {
        const validTreatment = {
          name_chinese: '面部护理',
          name_english: 'Facial Treatment',
          category: 'skincare',
          price: 150.00,
          duration_minutes: 60,
          fixed_deposit_amount: 50.00,
          requires_consultation: true,
          description: 'Professional facial treatment',
          is_active: true,
        }

        expect(() => treatmentSchemas.create.parse(validTreatment)).not.toThrow()
      })

      it('should validate duration constraints', () => {
        const treatmentWithInvalidDuration = {
          name_chinese: '面部护理',
          name_english: 'Facial Treatment',
          category: 'skincare',
          price: 150.00,
          duration_minutes: 10, // too short
          fixed_deposit_amount: 50.00,
        }

        expect(() => treatmentSchemas.create.parse(treatmentWithInvalidDuration)).toThrow()
      })
    })
  })

  describe('Appointment Schemas', () => {
    describe('Appointment creation validation', () => {
      it('should validate correct appointment data', () => {
        const validAppointment = {
          client_id: '123e4567-e89b-12d3-a456-************',
          treatment_id: '123e4567-e89b-12d3-a456-426614174001',
          appointment_date: '2024-12-25',
          start_time: '10:00',
          end_time: '11:00',
          notes: 'Regular appointment',
        }

        expect(() => appointmentSchemas.create.parse(validAppointment)).not.toThrow()
      })

      it('should validate time constraints', () => {
        const appointmentWithInvalidTime = {
          client_id: '123e4567-e89b-12d3-a456-************',
          treatment_id: '123e4567-e89b-12d3-a456-426614174001',
          appointment_date: '2024-12-25',
          start_time: '11:00',
          end_time: '10:00', // end before start
        }

        expect(() => appointmentSchemas.create.parse(appointmentWithInvalidTime)).toThrow()
      })
    })

    describe('Date range validation', () => {
      it('should validate correct date range', () => {
        const validRange = {
          start_date: '2024-01-01',
          end_date: '2024-12-31',
        }

        expect(() => appointmentSchemas.dateRange.parse(validRange)).not.toThrow()
      })

      it('should reject invalid date range', () => {
        const invalidRange = {
          start_date: '2024-12-31',
          end_date: '2024-01-01', // end before start
        }

        expect(() => appointmentSchemas.dateRange.parse(invalidRange)).toThrow()
      })
    })
  })

  describe('Validation Utilities', () => {
    describe('validateRequest', () => {
      it('should validate correct data', () => {
        const schema = z.object({ name: z.string(), age: z.number() })
        const validator = validateRequest(schema)
        const data = { name: 'John', age: 30 }

        expect(() => validator(data)).not.toThrow()
        expect(validator(data)).toEqual(data)
      })

      it('should throw ValidationError for invalid data', () => {
        const schema = z.object({ name: z.string(), age: z.number() })
        const validator = validateRequest(schema)
        const invalidData = { name: 'John', age: 'thirty' }

        expect(() => validator(invalidData)).toThrow(ValidationError)
      })
    })

    describe('validateQuery', () => {
      it('should validate and convert query parameters', () => {
        const schema = z.object({
          page: z.number(),
          active: z.boolean(),
          name: z.string(),
        })

        const query = {
          page: '1',
          active: 'true',
          name: 'test',
        }

        const result = validateQuery(schema, query)

        expect(result.page).toBe(1)
        expect(result.active).toBe(true)
        expect(result.name).toBe('test')
      })

      it('should handle array parameters', () => {
        const schema = z.object({
          tags: z.array(z.string()),
        })

        const query = {
          tags: ['tag1', 'tag2'],
        }

        const result = validateQuery(schema, query)

        expect(result.tags).toEqual(['tag1', 'tag2'])
      })
    })

    describe('sanitizeHtml', () => {
      it('should escape HTML characters', () => {
        const input = '<script>alert("xss")</script>'
        const expected = '&lt;script&gt;alert(&quot;xss&quot;)&lt;&#x2F;script&gt;'

        expect(sanitizeHtml(input)).toBe(expected)
      })

      it('should handle all dangerous characters', () => {
        const input = `<>"'&/`
        const expected = '&lt;&gt;&quot;&#x27;&amp;&#x2F;'

        expect(sanitizeHtml(input)).toBe(expected)
      })
    })

    describe('sanitizeText', () => {
      it('should trim and sanitize text', () => {
        const input = '  <script>alert("xss")</script>  '
        const result = sanitizeText(input)

        expect(result).not.toContain('<script>')
        expect(result).not.toStartWith(' ')
        expect(result).not.toEndWith(' ')
      })
    })

    describe('safeValidate', () => {
      it('should return success result for valid data', () => {
        const schema = z.object({ name: z.string() })
        const data = { name: 'John' }

        const result = safeValidate(schema, data)

        expect(result.success).toBe(true)
        if (result.success) {
          expect(result.data).toEqual(data)
        }
      })

      it('should return failure result for invalid data', () => {
        const schema = z.object({ name: z.string() })
        const data = { name: 123 }

        const result = safeValidate(schema, data)

        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error).toBeInstanceOf(z.ZodError)
        }
      })
    })
  })

  describe('Edge Cases and Security', () => {
    it('should handle null and undefined values', () => {
      const schema = z.object({ name: z.string().optional() })

      expect(() => validateRequest(schema)({ name: undefined })).not.toThrow()
      expect(() => validateRequest(schema)({ name: null })).toThrow()
    })

    it('should prevent prototype pollution', () => {
      const schema = z.object({ name: z.string() })
      const maliciousData = JSON.parse('{"__proto__": {"polluted": true}, "name": "test"}')

      const result = validateRequest(schema)(maliciousData)
      expect(result).toEqual({ name: 'test' })
      expect((result as any).polluted).toBeUndefined()
    })

    it('should handle very large strings', () => {
      const largeString = 'a'.repeat(10000)
      const schema = z.object({ text: z.string().max(1000) })

      expect(() => validateRequest(schema)({ text: largeString })).toThrow()
    })

    it('should handle special Unicode characters', () => {
      const unicodeText = '测试文本 🎉 emoji'
      const schema = z.object({ text: z.string() })

      expect(() => validateRequest(schema)({ text: unicodeText })).not.toThrow()
    })
  })
})
