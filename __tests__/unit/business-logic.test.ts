import { businessLogic } from '@/lib/supabase/queries'
import { supabase } from '@/lib/supabase/client'

// Mock Supabase client
jest.mock('@/lib/supabase/client')
const mockSupabase = supabase as jest.Mocked<typeof supabase>

describe('Business Logic Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Fixed Deposit Calculation', () => {
    it('should calculate the highest fixed deposit amount for same-day appointments', async () => {
      const appointments = [
        { id: '1', treatment_id: 'treatment-1', appointment_date: '2025-07-15' },
        { id: '2', treatment_id: 'treatment-2', appointment_date: '2025-07-15' },
        { id: '3', treatment_id: 'treatment-3', appointment_date: '2025-07-15' },
      ]

      // Mock treatment responses with different deposit amounts
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn()
          .mockResolvedValueOnce({ data: { fixed_deposit_amount: 100 }, error: null })
          .mockResolvedValueOnce({ data: { fixed_deposit_amount: 150 }, error: null })
          .mockResolvedValueOnce({ data: { fixed_deposit_amount: 120 }, error: null }),
      } as any)

      const result = await businessLogic.calculateFixedDeposit(appointments as any)

      expect(result).toBe(150) // Should return the highest amount
      expect(mockSupabase.from).toHaveBeenCalledWith('treatments')
    })

    it('should return 0 if no treatments have deposit amounts', async () => {
      const appointments = [
        { id: '1', treatment_id: 'treatment-1', appointment_date: '2025-07-15' },
      ]

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: { fixed_deposit_amount: 0 }, error: null }),
      } as any)

      const result = await businessLogic.calculateFixedDeposit(appointments as any)

      expect(result).toBe(0)
    })

    it('should handle database errors gracefully', async () => {
      const appointments = [
        { id: '1', treatment_id: 'treatment-1', appointment_date: '2025-07-15' },
      ]

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: null, error: new Error('Database error') }),
      } as any)

      const result = await businessLogic.calculateFixedDeposit(appointments as any)

      expect(result).toBe(0) // Should handle error gracefully
    })
  })

  describe('Appointment Conflict Detection', () => {
    it('should detect time conflicts correctly', async () => {
      const conflictingAppointments = [
        { id: '1', start_time: '10:00', end_time: '11:00' },
        { id: '2', start_time: '10:30', end_time: '11:30' },
      ]

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        neq: jest.fn().mockReturnThis(),
        or: jest.fn().mockResolvedValue({ data: conflictingAppointments, error: null }),
      } as any)

      const hasConflict = await businessLogic.checkAppointmentConflict(
        '2025-07-15',
        '10:15',
        '10:45'
      )

      expect(hasConflict).toBe(true)
    })

    it('should not detect conflicts when excluding specific appointment', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        neq: jest.fn().mockReturnThis(),
        or: jest.fn().mockResolvedValue({ data: [], error: null }),
      } as any)

      const hasConflict = await businessLogic.checkAppointmentConflict(
        '2025-07-15',
        '10:00',
        '11:00',
        'exclude-id'
      )

      expect(hasConflict).toBe(false)
    })

    it('should handle no conflicts', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        neq: jest.fn().mockReturnThis(),
        or: jest.fn().mockResolvedValue({ data: [], error: null }),
      } as any)

      const hasConflict = await businessLogic.checkAppointmentConflict(
        '2025-07-15',
        '14:00',
        '15:00'
      )

      expect(hasConflict).toBe(false)
    })
  })

  describe('Invoice Number Generation', () => {
    it('should generate unique invoice numbers with correct format', () => {
      const invoiceNumber1 = businessLogic.generateInvoiceNumber()
      const invoiceNumber2 = businessLogic.generateInvoiceNumber()

      expect(invoiceNumber1).toMatch(/^INV-\d{8}-\d{4}$/)
      expect(invoiceNumber2).toMatch(/^INV-\d{8}-\d{4}$/)
      expect(invoiceNumber1).not.toBe(invoiceNumber2)
    })

    it('should include current date in invoice number', () => {
      const invoiceNumber = businessLogic.generateInvoiceNumber()
      const today = new Date()
      const expectedDatePart = `${today.getFullYear()}${String(today.getMonth() + 1).padStart(2, '0')}${String(today.getDate()).padStart(2, '0')}`
      
      expect(invoiceNumber).toContain(expectedDatePart)
    })
  })

  describe('Consultation Fee Calculation', () => {
    it('should calculate consultation fees for consultation appointments', async () => {
      const appointments = [
        { id: '1', treatment_id: 'treatment-1', appointment_type: 'consultation' },
        { id: '2', treatment_id: 'treatment-2', appointment_type: 'treatment' },
        { id: '3', treatment_id: 'treatment-3', appointment_type: 'consultation' },
      ]

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn()
          .mockResolvedValueOnce({ 
            data: { consultation_fee: 50, requires_consultation: true }, 
            error: null 
          })
          .mockResolvedValueOnce({ 
            data: { consultation_fee: 75, requires_consultation: true }, 
            error: null 
          }),
      } as any)

      const result = await businessLogic.calculateConsultationFees(appointments as any)

      expect(result).toBe(125) // 50 + 75
    })

    it('should not include fees for treatments that do not require consultation', async () => {
      const appointments = [
        { id: '1', treatment_id: 'treatment-1', appointment_type: 'consultation' },
      ]

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ 
          data: { consultation_fee: 50, requires_consultation: false }, 
          error: null 
        }),
      } as any)

      const result = await businessLogic.calculateConsultationFees(appointments as any)

      expect(result).toBe(0)
    })
  })

  describe('Legacy Deposit Calculation (Backward Compatibility)', () => {
    it('should calculate percentage-based deposits correctly', () => {
      expect(businessLogic.calculateDeposit(200, 50)).toBe(100)
      expect(businessLogic.calculateDeposit(150, 30)).toBe(45)
      expect(businessLogic.calculateDeposit(100, 25)).toBe(25)
    })

    it('should use default 50% if no percentage provided', () => {
      expect(businessLogic.calculateDeposit(200)).toBe(100)
    })

    it('should round to 2 decimal places', () => {
      expect(businessLogic.calculateDeposit(33.33, 33)).toBe(11)
    })
  })
})
