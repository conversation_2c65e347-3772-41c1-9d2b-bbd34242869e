/**
 * RBAC and Security System Tests
 * Comprehensive testing of role-based access control, authentication, and security monitoring
 */

import { rbacManager, Permission, Role, User, Session } from '../../src/lib/auth/rbac'
import { HIPAARole } from '../../src/lib/compliance/hipaa'
import { securityMonitor, SecurityEventType, SecuritySeverity } from '../../src/lib/security/monitoring'

describe('Role-Based Access Control (RBAC) System', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Role Management', () => {
    it('should initialize with default roles', () => {
      const roles = rbacManager.getAllRoles()
      
      expect(roles.length).toBeGreaterThan(0)
      
      // Check for essential roles
      const roleNames = roles.map(role => role.name)
      expect(roleNames).toContain('系统管理员')
      expect(roleNames).toContain('医疗服务提供者')
      expect(roleNames).toContain('前台接待')
      expect(roleNames).toContain('财务人员')
    })

    it('should create new roles with proper permissions', async () => {
      const newRole = await rbacManager.createRole({
        name: '测试角色',
        description: '用于测试的角色',
        hipaaRole: HIPAARole.ADMINISTRATIVE_STAFF,
        permissions: [Permission.CLIENT_READ, Permission.APPOINTMENT_READ],
        isActive: true,
      })

      expect(newRole.id).toBeDefined()
      expect(newRole.name).toBe('测试角色')
      expect(newRole.permissions).toContain(Permission.CLIENT_READ)
      expect(newRole.permissions).toContain(Permission.APPOINTMENT_READ)
      expect(newRole.createdAt).toBeInstanceOf(Date)
    })

    it('should retrieve roles by ID', () => {
      const adminRole = rbacManager.getRole('admin')
      
      expect(adminRole).toBeDefined()
      expect(adminRole?.name).toBe('系统管理员')
      expect(adminRole?.hipaaRole).toBe(HIPAARole.COVERED_ENTITY_ADMIN)
      expect(adminRole?.permissions).toContain(Permission.USER_CREATE)
    })

    it('should update existing roles', async () => {
      const originalRole = rbacManager.getRole('front_desk')
      expect(originalRole).toBeDefined()

      const updatedRole = await rbacManager.updateRole('front_desk', {
        description: '更新后的前台接待角色描述',
        permissions: [...originalRole!.permissions, Permission.REPORT_VIEW],
      })

      expect(updatedRole).toBeDefined()
      expect(updatedRole?.description).toBe('更新后的前台接待角色描述')
      expect(updatedRole?.permissions).toContain(Permission.REPORT_VIEW)
      expect(updatedRole?.updatedAt.getTime()).toBeGreaterThan(originalRole!.updatedAt.getTime())
    })

    it('should handle role updates for non-existent roles', async () => {
      const result = await rbacManager.updateRole('non-existent-role', {
        name: '不存在的角色',
      })

      expect(result).toBeNull()
    })
  })

  describe('Permission Management', () => {
    const testUserId = 'test-user-123'

    beforeEach(() => {
      // Mock user with healthcare provider role
      rbacManager['users'].set(testUserId, {
        id: testUserId,
        email: '<EMAIL>',
        firstName: '测试',
        lastName: '用户',
        roles: ['healthcare_provider'],
        isActive: true,
        mfaEnabled: true,
        hipaaTrainingCompleted: true,
        hipaaTrainingDate: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      })
    })

    it('should check individual permissions correctly', async () => {
      // Healthcare provider should have client read permission
      const hasClientRead = await rbacManager.hasPermission(testUserId, Permission.CLIENT_READ)
      expect(hasClientRead).toBe(true)

      // Healthcare provider should have treatment permissions
      const hasTreatmentCreate = await rbacManager.hasPermission(testUserId, Permission.TREATMENT_CREATE)
      expect(hasTreatmentCreate).toBe(true)

      // Healthcare provider should NOT have user management permissions
      const hasUserCreate = await rbacManager.hasPermission(testUserId, Permission.USER_CREATE)
      expect(hasUserCreate).toBe(false)
    })

    it('should check multiple permissions efficiently', async () => {
      const permissions = [
        Permission.CLIENT_READ,
        Permission.TREATMENT_CREATE,
        Permission.USER_CREATE,
        Permission.SYSTEM_CONFIG,
      ]

      const result = await rbacManager.hasPermissions(testUserId, permissions)

      expect(result.hasAll).toBe(false)
      expect(result.hasAny).toBe(true)
      expect(result.allowed).toContain(Permission.CLIENT_READ)
      expect(result.allowed).toContain(Permission.TREATMENT_CREATE)
      expect(result.denied).toContain(Permission.USER_CREATE)
      expect(result.denied).toContain(Permission.SYSTEM_CONFIG)
    })

    it('should return empty permissions for inactive users', async () => {
      const inactiveUserId = 'inactive-user'
      rbacManager['users'].set(inactiveUserId, {
        id: inactiveUserId,
        email: '<EMAIL>',
        firstName: '非活跃',
        lastName: '用户',
        roles: ['admin'],
        isActive: false, // Inactive user
        mfaEnabled: false,
        hipaaTrainingCompleted: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      })

      const hasPermission = await rbacManager.hasPermission(inactiveUserId, Permission.CLIENT_READ)
      expect(hasPermission).toBe(false)

      const permissions = rbacManager.getUserPermissions(inactiveUserId)
      expect(permissions).toHaveLength(0)
    })

    it('should get all user permissions', () => {
      const permissions = rbacManager.getUserPermissions(testUserId)
      
      expect(permissions.length).toBeGreaterThan(0)
      expect(permissions).toContain(Permission.CLIENT_READ)
      expect(permissions).toContain(Permission.TREATMENT_CREATE)
      expect(permissions).not.toContain(Permission.USER_CREATE)
    })

    it('should get user roles', () => {
      const roles = rbacManager.getUserRoles(testUserId)
      
      expect(roles.length).toBe(1)
      expect(roles[0].id).toBe('healthcare_provider')
      expect(roles[0].name).toBe('医疗服务提供者')
    })
  })

  describe('Role Assignment', () => {
    const testUserId = 'role-test-user'

    beforeEach(() => {
      rbacManager['users'].set(testUserId, {
        id: testUserId,
        email: '<EMAIL>',
        firstName: '角色测试',
        lastName: '用户',
        roles: [],
        isActive: true,
        mfaEnabled: false,
        hipaaTrainingCompleted: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
    })

    it('should assign roles to users', async () => {
      const success = await rbacManager.assignRole(testUserId, 'front_desk')
      expect(success).toBe(true)

      const userRoles = rbacManager.getUserRoles(testUserId)
      expect(userRoles).toHaveLength(1)
      expect(userRoles[0].id).toBe('front_desk')
    })

    it('should not assign duplicate roles', async () => {
      await rbacManager.assignRole(testUserId, 'front_desk')
      await rbacManager.assignRole(testUserId, 'front_desk') // Duplicate

      const userRoles = rbacManager.getUserRoles(testUserId)
      expect(userRoles).toHaveLength(1) // Should still be 1
    })

    it('should assign multiple roles', async () => {
      await rbacManager.assignRole(testUserId, 'front_desk')
      await rbacManager.assignRole(testUserId, 'billing_staff')

      const userRoles = rbacManager.getUserRoles(testUserId)
      expect(userRoles).toHaveLength(2)

      const roleIds = userRoles.map(role => role.id)
      expect(roleIds).toContain('front_desk')
      expect(roleIds).toContain('billing_staff')
    })

    it('should remove roles from users', async () => {
      await rbacManager.assignRole(testUserId, 'front_desk')
      await rbacManager.assignRole(testUserId, 'billing_staff')

      const removeSuccess = await rbacManager.removeRole(testUserId, 'front_desk')
      expect(removeSuccess).toBe(true)

      const userRoles = rbacManager.getUserRoles(testUserId)
      expect(userRoles).toHaveLength(1)
      expect(userRoles[0].id).toBe('billing_staff')
    })

    it('should handle role assignment for non-existent users or roles', async () => {
      const nonExistentUser = await rbacManager.assignRole('non-existent', 'admin')
      expect(nonExistentUser).toBe(false)

      const nonExistentRole = await rbacManager.assignRole(testUserId, 'non-existent-role')
      expect(nonExistentRole).toBe(false)
    })
  })

  describe('Session Management', () => {
    const testUserId = 'session-test-user'
    const deviceId = 'device-123'
    const ipAddress = '*************'
    const userAgent = 'Mozilla/5.0 Test Browser'

    it('should create new sessions', async () => {
      const session = await rbacManager.createSession(testUserId, deviceId, ipAddress, userAgent)

      expect(session.id).toBeDefined()
      expect(session.userId).toBe(testUserId)
      expect(session.deviceId).toBe(deviceId)
      expect(session.ipAddress).toBe(ipAddress)
      expect(session.userAgent).toBe(userAgent)
      expect(session.isActive).toBe(true)
      expect(session.expiresAt.getTime()).toBeGreaterThan(Date.now())
    })

    it('should validate active sessions', async () => {
      const session = await rbacManager.createSession(testUserId, deviceId, ipAddress, userAgent)
      
      const validatedSession = await rbacManager.validateSession(session.id)
      expect(validatedSession).toBeDefined()
      expect(validatedSession?.id).toBe(session.id)
      expect(validatedSession?.isActive).toBe(true)
    })

    it('should reject expired sessions', async () => {
      const session = await rbacManager.createSession(testUserId, deviceId, ipAddress, userAgent)
      
      // Manually expire the session
      session.expiresAt = new Date(Date.now() - 1000)
      
      const validatedSession = await rbacManager.validateSession(session.id)
      expect(validatedSession).toBeNull()
    })

    it('should terminate sessions', async () => {
      const session = await rbacManager.createSession(testUserId, deviceId, ipAddress, userAgent)
      
      const terminated = await rbacManager.terminateSession(session.id)
      expect(terminated).toBe(true)

      const validatedSession = await rbacManager.validateSession(session.id)
      expect(validatedSession).toBeNull()
    })

    it('should get user sessions', async () => {
      const session1 = await rbacManager.createSession(testUserId, 'device-1', ipAddress, userAgent)
      const session2 = await rbacManager.createSession(testUserId, 'device-2', ipAddress, userAgent)

      const userSessions = rbacManager.getUserSessions(testUserId)
      expect(userSessions).toHaveLength(2)

      const sessionIds = userSessions.map(s => s.id)
      expect(sessionIds).toContain(session1.id)
      expect(sessionIds).toContain(session2.id)
    })

    it('should terminate all user sessions', async () => {
      await rbacManager.createSession(testUserId, 'device-1', ipAddress, userAgent)
      await rbacManager.createSession(testUserId, 'device-2', ipAddress, userAgent)
      await rbacManager.createSession(testUserId, 'device-3', ipAddress, userAgent)

      const terminatedCount = await rbacManager.terminateAllUserSessions(testUserId)
      expect(terminatedCount).toBe(3)

      const remainingSessions = rbacManager.getUserSessions(testUserId)
      expect(remainingSessions).toHaveLength(0)
    })
  })
})

describe('Security Monitoring System', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Security Event Logging', () => {
    it('should log security events with proper metadata', async () => {
      const event = await securityMonitor.logSecurityEvent(
        SecurityEventType.LOGIN_FAILURE,
        SecuritySeverity.MEDIUM,
        { reason: 'Invalid password', attempts: 3 },
        'user-123',
        'session-456',
        '*************',
        'Mozilla/5.0 Test Browser'
      )

      expect(event.id).toBeDefined()
      expect(event.type).toBe(SecurityEventType.LOGIN_FAILURE)
      expect(event.severity).toBe(SecuritySeverity.MEDIUM)
      expect(event.userId).toBe('user-123')
      expect(event.sessionId).toBe('session-456')
      expect(event.ipAddress).toBe('*************')
      expect(event.details.reason).toBe('Invalid password')
      expect(event.resolved).toBe(false)
    })

    it('should handle critical events with immediate response', async () => {
      const criticalEvent = await securityMonitor.logSecurityEvent(
        SecurityEventType.PRIVILEGE_ESCALATION,
        SecuritySeverity.CRITICAL,
        { attemptedRole: 'admin', currentRole: 'user' },
        'user-123',
        undefined,
        '*************'
      )

      expect(criticalEvent.severity).toBe(SecuritySeverity.CRITICAL)
      
      // Critical events should trigger immediate blocking
      expect(securityMonitor.isIPBlocked('*************')).toBe(true)
    })

    it('should track suspicious user activity', async () => {
      const userId = 'suspicious-user'
      
      // Log multiple high-severity events
      await securityMonitor.logSecurityEvent(
        SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT,
        SecuritySeverity.HIGH,
        { resource: '/admin/users' },
        userId
      )
      
      await securityMonitor.logSecurityEvent(
        SecurityEventType.PHI_EXPORT,
        SecuritySeverity.HIGH,
        { recordCount: 100 },
        userId
      )
      
      await securityMonitor.logSecurityEvent(
        SecurityEventType.BULK_DATA_ACCESS,
        SecuritySeverity.HIGH,
        { recordCount: 500 },
        userId
      )

      // Should trigger suspicious activity alert
      const alerts = securityMonitor.getSecurityAlerts(10, true)
      const suspiciousAlert = alerts.find(alert => 
        alert.title.includes('可疑活动') && alert.description.includes(userId)
      )
      
      expect(suspiciousAlert).toBeDefined()
    })
  })

  describe('Threat Detection Rules', () => {
    it('should detect brute force login attempts', async () => {
      const userId = 'brute-force-user'
      const ipAddress = '*************'
      
      // Simulate multiple login failures
      for (let i = 0; i < 6; i++) {
        await securityMonitor.logSecurityEvent(
          SecurityEventType.LOGIN_FAILURE,
          SecuritySeverity.MEDIUM,
          { attempt: i + 1 },
          userId,
          undefined,
          ipAddress
        )
      }

      // Wait for threat detection to run
      await new Promise(resolve => setTimeout(resolve, 100))

      // Should trigger brute force detection
      const alerts = securityMonitor.getSecurityAlerts()
      const bruteForceAlert = alerts.find(alert => 
        alert.title.includes('暴力破解')
      )
      
      expect(bruteForceAlert).toBeDefined()
      expect(securityMonitor.isIPBlocked(ipAddress)).toBe(true)
    })

    it('should detect suspicious PHI access patterns', async () => {
      const userId = 'phi-access-user'
      
      // Simulate excessive PHI access
      for (let i = 0; i < 55; i++) {
        await securityMonitor.logSecurityEvent(
          SecurityEventType.PHI_ACCESS,
          SecuritySeverity.LOW,
          { recordId: `record-${i}` },
          userId
        )
      }

      // Wait for threat detection
      await new Promise(resolve => setTimeout(resolve, 100))

      const alerts = securityMonitor.getSecurityAlerts()
      const phiAlert = alerts.find(alert => 
        alert.title.includes('PHI访问')
      )
      
      expect(phiAlert).toBeDefined()
    })

    it('should detect bulk data export attempts', async () => {
      const userId = 'export-user'
      
      // Simulate multiple data exports
      for (let i = 0; i < 4; i++) {
        await securityMonitor.logSecurityEvent(
          SecurityEventType.PHI_EXPORT,
          SecuritySeverity.MEDIUM,
          { recordCount: 100 + i * 50 },
          userId
        )
      }

      await new Promise(resolve => setTimeout(resolve, 100))

      const alerts = securityMonitor.getSecurityAlerts()
      const exportAlert = alerts.find(alert => 
        alert.title.includes('数据导出')
      )
      
      expect(exportAlert).toBeDefined()
    })
  })

  describe('Security Metrics and Reporting', () => {
    beforeEach(async () => {
      // Set up test data
      await securityMonitor.logSecurityEvent(
        SecurityEventType.LOGIN_SUCCESS,
        SecuritySeverity.LOW,
        {},
        'user-1'
      )
      
      await securityMonitor.logSecurityEvent(
        SecurityEventType.LOGIN_FAILURE,
        SecuritySeverity.MEDIUM,
        {},
        'user-2'
      )
      
      await securityMonitor.logSecurityEvent(
        SecurityEventType.PRIVILEGE_ESCALATION,
        SecuritySeverity.CRITICAL,
        {},
        'user-3'
      )
    })

    it('should provide comprehensive security metrics', () => {
      const metrics = securityMonitor.getSecurityMetrics()
      
      expect(metrics.totalEvents).toBeGreaterThan(0)
      expect(metrics.eventsBySeverity).toBeDefined()
      expect(metrics.eventsBySeverity[SecuritySeverity.LOW]).toBeGreaterThanOrEqual(1)
      expect(metrics.eventsBySeverity[SecuritySeverity.MEDIUM]).toBeGreaterThanOrEqual(1)
      expect(metrics.eventsBySeverity[SecuritySeverity.CRITICAL]).toBeGreaterThanOrEqual(1)
      expect(metrics.totalAlerts).toBeGreaterThanOrEqual(0)
      expect(metrics.blockedIPs).toBeGreaterThanOrEqual(0)
    })

    it('should filter security events by criteria', () => {
      const allEvents = securityMonitor.getSecurityEvents(100)
      const criticalEvents = securityMonitor.getSecurityEvents(100, SecuritySeverity.CRITICAL)
      const loginEvents = securityMonitor.getSecurityEvents(100, undefined, SecurityEventType.LOGIN_FAILURE)
      
      expect(allEvents.length).toBeGreaterThanOrEqual(3)
      expect(criticalEvents.length).toBeGreaterThanOrEqual(1)
      expect(loginEvents.length).toBeGreaterThanOrEqual(1)
      
      // Verify filtering works correctly
      criticalEvents.forEach(event => {
        expect(event.severity).toBe(SecuritySeverity.CRITICAL)
      })
      
      loginEvents.forEach(event => {
        expect(event.type).toBe(SecurityEventType.LOGIN_FAILURE)
      })
    })

    it('should manage security alerts lifecycle', async () => {
      // Create a test alert
      const event = await securityMonitor.logSecurityEvent(
        SecurityEventType.SUSPICIOUS_ACTIVITY,
        SecuritySeverity.HIGH,
        { description: 'Test suspicious activity' },
        'test-user'
      )

      // Get unacknowledged alerts
      const unacknowledgedAlerts = securityMonitor.getSecurityAlerts(50, true)
      expect(unacknowledgedAlerts.length).toBeGreaterThan(0)

      // Acknowledge an alert
      if (unacknowledgedAlerts.length > 0) {
        const alertId = unacknowledgedAlerts[0].id
        const acknowledged = await securityMonitor.acknowledgeAlert(alertId, 'admin-user')
        expect(acknowledged).toBe(true)

        // Verify alert is acknowledged
        const allAlerts = securityMonitor.getSecurityAlerts()
        const acknowledgedAlert = allAlerts.find(alert => alert.id === alertId)
        expect(acknowledgedAlert?.acknowledged).toBe(true)
        expect(acknowledgedAlert?.acknowledgedBy).toBe('admin-user')
      }
    })
  })

  describe('IP Blocking and User Management', () => {
    it('should block and track malicious IPs', async () => {
      const maliciousIP = '192.168.1.999'
      
      expect(securityMonitor.isIPBlocked(maliciousIP)).toBe(false)
      
      // Log critical event that should trigger IP blocking
      await securityMonitor.logSecurityEvent(
        SecurityEventType.MALWARE_DETECTED,
        SecuritySeverity.CRITICAL,
        { malwareType: 'trojan' },
        undefined,
        undefined,
        maliciousIP
      )
      
      expect(securityMonitor.isIPBlocked(maliciousIP)).toBe(true)
    })

    it('should track suspicious users across multiple events', async () => {
      const suspiciousUserId = 'suspicious-user-123'
      
      // Log multiple high-severity events
      await securityMonitor.logSecurityEvent(
        SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT,
        SecuritySeverity.HIGH,
        {},
        suspiciousUserId
      )
      
      await securityMonitor.logSecurityEvent(
        SecurityEventType.PHI_EXPORT,
        SecuritySeverity.HIGH,
        {},
        suspiciousUserId
      )
      
      await securityMonitor.logSecurityEvent(
        SecurityEventType.CONFIGURATION_CHANGE,
        SecuritySeverity.HIGH,
        {},
        suspiciousUserId
      )

      const metrics = securityMonitor.getSecurityMetrics()
      expect(metrics.suspiciousUsers).toBeGreaterThan(0)
    })
  })

  describe('Performance and Scalability', () => {
    it('should handle high-volume event logging efficiently', async () => {
      const startTime = Date.now()
      const eventCount = 100
      
      const promises = Array.from({ length: eventCount }, (_, i) =>
        securityMonitor.logSecurityEvent(
          SecurityEventType.LOGIN_SUCCESS,
          SecuritySeverity.LOW,
          { eventNumber: i },
          `user-${i}`
        )
      )

      await Promise.all(promises)
      const duration = Date.now() - startTime
      
      expect(duration).toBeLessThan(5000) // Should complete within 5 seconds
      
      const events = securityMonitor.getSecurityEvents(eventCount + 10)
      expect(events.length).toBeGreaterThanOrEqual(eventCount)
    })

    it('should clean up old events automatically', () => {
      // This test would verify the cleanup mechanism
      // In a real implementation, you might mock the cleanup interval
      const metrics = securityMonitor.getSecurityMetrics()
      expect(metrics.totalEvents).toBeGreaterThanOrEqual(0)
    })
  })
})
