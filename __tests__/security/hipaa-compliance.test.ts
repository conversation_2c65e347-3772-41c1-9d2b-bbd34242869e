/**
 * HIPAA Compliance Tests
 * Comprehensive testing of HIPAA compliance features and PHI protection
 */

import { hipaaManager, PHIClassification, DataSensitivity } from '../../src/lib/compliance/hipaa'
import { HIPAARole, AccessPurpose } from '../../src/lib/compliance/hipaa'
import { Encryption } from '../../src/lib/security'

describe('HIPAA Compliance System', () => {
  beforeEach(() => {
    // Reset any test state
    jest.clearAllMocks()
  })

  describe('PHI Classification and Registry', () => {
    it('should correctly identify PHI fields', () => {
      expect(hipaaManager.isPHIField('clients.first_name')).toBe(true)
      expect(hipaaManager.isPHIField('clients.last_name')).toBe(true)
      expect(hipaaManager.isPHIField('clients.phone')).toBe(true)
      expect(hipaaManager.isPHIField('clients.email')).toBe(true)
      expect(hipaaManager.isPHIField('clients.date_of_birth')).toBe(true)
      
      // Non-PHI fields
      expect(hipaaManager.isPHIField('treatments.price')).toBe(false)
      expect(hipaaManager.isPHIField('appointments.status')).toBe(false)
    })

    it('should provide correct PHI metadata', () => {
      const metadata = hipaaManager.getPHIMetadata('clients.first_name')
      
      expect(metadata).toBeDefined()
      expect(metadata?.classification).toBe(PHIClassification.NAMES)
      expect(metadata?.sensitivity).toBe(DataSensitivity.RESTRICTED)
      expect(metadata?.encryptionRequired).toBe(true)
      expect(metadata?.auditRequired).toBe(true)
      expect(metadata?.retentionPeriod).toBe(2555) // 7 years
    })

    it('should handle medical record fields correctly', () => {
      const treatmentMetadata = hipaaManager.getPHIMetadata('treatments.description')
      const notesMetadata = hipaaManager.getPHIMetadata('appointments.notes')
      
      expect(treatmentMetadata?.classification).toBe(PHIClassification.TREATMENT_RECORDS)
      expect(notesMetadata?.classification).toBe(PHIClassification.MEDICAL_HISTORY)
      
      expect(treatmentMetadata?.encryptionRequired).toBe(true)
      expect(notesMetadata?.encryptionRequired).toBe(true)
    })
  })

  describe('PHI Encryption and Decryption', () => {
    const testClientData = {
      id: 'test-client-1',
      first_name: '张三',
      last_name: '李',
      phone: '13800138000',
      email: '<EMAIL>',
      date_of_birth: '1990-01-01',
      address_line_1: '北京市朝阳区测试街道123号',
      // Non-PHI field
      created_at: '2024-01-01T00:00:00Z',
    }

    it('should encrypt PHI fields during storage', async () => {
      const encryptedData = await hipaaManager.encryptPHI(testClientData, 'clients')
      
      // PHI fields should be encrypted
      expect(encryptedData.first_name).not.toBe(testClientData.first_name)
      expect(encryptedData.last_name).not.toBe(testClientData.last_name)
      expect(encryptedData.phone).not.toBe(testClientData.phone)
      expect(encryptedData.email).not.toBe(testClientData.email)
      expect(encryptedData.date_of_birth).not.toBe(testClientData.date_of_birth)
      expect(encryptedData.address_line_1).not.toBe(testClientData.address_line_1)
      
      // Non-PHI fields should remain unchanged
      expect(encryptedData.id).toBe(testClientData.id)
      expect(encryptedData.created_at).toBe(testClientData.created_at)
    })

    it('should decrypt PHI fields during retrieval', async () => {
      const encryptedData = await hipaaManager.encryptPHI(testClientData, 'clients')
      const decryptedData = await hipaaManager.decryptPHI(encryptedData, 'clients')
      
      // PHI fields should be decrypted back to original values
      expect(decryptedData.first_name).toBe(testClientData.first_name)
      expect(decryptedData.last_name).toBe(testClientData.last_name)
      expect(decryptedData.phone).toBe(testClientData.phone)
      expect(decryptedData.email).toBe(testClientData.email)
      expect(decryptedData.date_of_birth).toBe(testClientData.date_of_birth)
      expect(decryptedData.address_line_1).toBe(testClientData.address_line_1)
    })

    it('should handle null and undefined values gracefully', async () => {
      const dataWithNulls = {
        first_name: null,
        last_name: undefined,
        phone: '13800138000',
        email: '',
      }

      const encryptedData = await hipaaManager.encryptPHI(dataWithNulls, 'clients')
      const decryptedData = await hipaaManager.decryptPHI(encryptedData, 'clients')
      
      expect(encryptedData.first_name).toBeNull()
      expect(encryptedData.last_name).toBeUndefined()
      expect(decryptedData.first_name).toBeNull()
      expect(decryptedData.last_name).toBeUndefined()
    })

    it('should handle encryption failures gracefully', async () => {
      // Mock encryption failure
      const originalEncrypt = Encryption.encrypt
      Encryption.encrypt = jest.fn().mockImplementation(() => {
        throw new Error('Encryption failed')
      })

      await expect(hipaaManager.encryptPHI(testClientData, 'clients'))
        .rejects.toThrow('Failed to encrypt PHI field')

      // Restore original function
      Encryption.encrypt = originalEncrypt
    })
  })

  describe('PHI Access Control and Logging', () => {
    const testUserId = 'user-123'
    const testFields = ['clients.first_name', 'clients.phone', 'clients.email']

    it('should check PHI access permissions correctly', async () => {
      // Healthcare provider should have access to medical fields
      const providerAccess = await hipaaManager.checkPHIAccess(
        testUserId,
        HIPAARole.HEALTHCARE_PROVIDER,
        AccessPurpose.TREATMENT,
        ['clients.first_name', 'treatments.description', 'appointments.notes']
      )

      expect(providerAccess.allowed).toBe(true)
      expect(providerAccess.allowedFields).toContain('clients.first_name')
      expect(providerAccess.allowedFields).toContain('treatments.description')
      expect(providerAccess.deniedFields).toHaveLength(0)

      // Administrative staff should have limited access
      const adminAccess = await hipaaManager.checkPHIAccess(
        testUserId,
        HIPAARole.ADMINISTRATIVE_STAFF,
        AccessPurpose.PAYMENT,
        ['clients.first_name', 'clients.phone', 'treatments.description']
      )

      expect(adminAccess.allowedFields).toContain('clients.first_name')
      expect(adminAccess.allowedFields).toContain('clients.phone')
      expect(adminAccess.deniedFields).toContain('treatments.description')
    })

    it('should deny access for insufficient permissions', async () => {
      const businessAssociateAccess = await hipaaManager.checkPHIAccess(
        testUserId,
        HIPAARole.BUSINESS_ASSOCIATE,
        AccessPurpose.TREATMENT,
        testFields
      )

      expect(businessAssociateAccess.allowed).toBe(false)
      expect(businessAssociateAccess.deniedFields.length).toBeGreaterThan(0)
      expect(businessAssociateAccess.reason).toBeDefined()
    })

    it('should log PHI access for audit trail', async () => {
      await hipaaManager.logPHIAccess(
        testUserId,
        AccessPurpose.TREATMENT,
        testFields,
        { sessionId: 'session-123', ipAddress: '***********' }
      )

      const accessHistory = hipaaManager.getPHIAccessHistory(testUserId, 1)
      
      expect(accessHistory).toHaveLength(1)
      expect(accessHistory[0].purpose).toBe(AccessPurpose.TREATMENT)
      expect(accessHistory[0].dataAccessed).toEqual(testFields)
      expect(accessHistory[0].timestamp).toBeInstanceOf(Date)
    })

    it('should track access history over time', async () => {
      // Log multiple access events
      await hipaaManager.logPHIAccess(testUserId, AccessPurpose.TREATMENT, ['clients.first_name'])
      await hipaaManager.logPHIAccess(testUserId, AccessPurpose.PAYMENT, ['clients.phone'])
      await hipaaManager.logPHIAccess(testUserId, AccessPurpose.HEALTHCARE_OPERATIONS, ['clients.email'])

      const accessHistory = hipaaManager.getPHIAccessHistory(testUserId, 30)
      
      expect(accessHistory.length).toBeGreaterThanOrEqual(3)
      
      // Check that different purposes are recorded
      const purposes = accessHistory.map(log => log.purpose)
      expect(purposes).toContain(AccessPurpose.TREATMENT)
      expect(purposes).toContain(AccessPurpose.PAYMENT)
      expect(purposes).toContain(AccessPurpose.HEALTHCARE_OPERATIONS)
    })
  })

  describe('HIPAA Compliance Reporting', () => {
    it('should generate comprehensive compliance report', async () => {
      const report = await hipaaManager.generateComplianceReport()
      
      expect(report.phiFieldsCount).toBeGreaterThan(0)
      expect(report.encryptedFieldsCount).toBeGreaterThan(0)
      expect(report.complianceScore).toBeGreaterThanOrEqual(0)
      expect(report.complianceScore).toBeLessThanOrEqual(100)
      expect(report.recommendations).toBeInstanceOf(Array)
    })

    it('should provide accurate compliance metrics', async () => {
      const report = await hipaaManager.generateComplianceReport()
      
      // All PHI fields should be encrypted
      expect(report.encryptedFieldsCount).toBe(report.phiFieldsCount)
      
      // Compliance score should be high if all fields are encrypted
      if (report.encryptedFieldsCount === report.phiFieldsCount) {
        expect(report.complianceScore).toBeGreaterThanOrEqual(90)
      }
    })

    it('should provide actionable recommendations', async () => {
      const report = await hipaaManager.generateComplianceReport()
      
      if (report.complianceScore < 100) {
        expect(report.recommendations.length).toBeGreaterThan(0)
        expect(report.recommendations[0]).toMatch(/encrypt|audit|implement/i)
      }
    })
  })

  describe('Data Sanitization and Logging', () => {
    const testData = {
      id: 'client-123',
      first_name: '张三',
      last_name: '李',
      phone: '13800138000',
      email: '<EMAIL>',
      created_at: '2024-01-01T00:00:00Z',
    }

    it('should sanitize PHI data for logging', () => {
      const sanitized = hipaaManager.sanitizeForLogging(testData, 'clients')
      
      // PHI fields should be masked
      expect(sanitized.first_name).toBe('***PHI***')
      expect(sanitized.last_name).toBe('***PHI***')
      expect(sanitized.phone).toBe('***PHI***')
      expect(sanitized.email).toBe('***PHI***')
      
      // Non-PHI fields should remain unchanged
      expect(sanitized.id).toBe(testData.id)
      expect(sanitized.created_at).toBe(testData.created_at)
    })

    it('should correctly identify data containing PHI', () => {
      expect(hipaaManager.containsPHI(testData, 'clients')).toBe(true)
      
      const nonPHIData = {
        id: 'client-123',
        created_at: '2024-01-01T00:00:00Z',
        status: 'active',
      }
      expect(hipaaManager.containsPHI(nonPHIData, 'clients')).toBe(false)
    })
  })

  describe('Minimum Necessary Rule', () => {
    it('should implement minimum necessary access principle', () => {
      const allFields = ['clients.first_name', 'clients.last_name', 'clients.phone', 'clients.email']
      
      const treatmentFields = hipaaManager.getMinimumNecessaryFields(
        allFields,
        'clients',
        AccessPurpose.TREATMENT
      )
      
      const paymentFields = hipaaManager.getMinimumNecessaryFields(
        allFields,
        'clients',
        AccessPurpose.PAYMENT
      )
      
      // For now, this returns all fields, but in a real implementation
      // it would filter based on the purpose
      expect(treatmentFields).toBeInstanceOf(Array)
      expect(paymentFields).toBeInstanceOf(Array)
    })
  })

  describe('Error Handling and Edge Cases', () => {
    it('should handle invalid field paths gracefully', () => {
      expect(hipaaManager.isPHIField('invalid.field.path')).toBe(false)
      expect(hipaaManager.getPHIMetadata('invalid.field.path')).toBeUndefined()
    })

    it('should handle empty data objects', async () => {
      const emptyData = {}
      
      const encrypted = await hipaaManager.encryptPHI(emptyData, 'clients')
      const decrypted = await hipaaManager.decryptPHI(encrypted, 'clients')
      
      expect(encrypted).toEqual({})
      expect(decrypted).toEqual({})
    })

    it('should handle access checks for unknown roles', async () => {
      const unknownRoleAccess = await hipaaManager.checkPHIAccess(
        'user-123',
        'unknown_role' as HIPAARole,
        AccessPurpose.TREATMENT,
        ['clients.first_name']
      )

      expect(unknownRoleAccess.allowed).toBe(false)
      expect(unknownRoleAccess.deniedFields).toContain('clients.first_name')
    })

    it('should handle concurrent access logging', async () => {
      const promises = Array.from({ length: 10 }, (_, i) =>
        hipaaManager.logPHIAccess(
          `user-${i}`,
          AccessPurpose.TREATMENT,
          [`clients.field_${i}`]
        )
      )

      await expect(Promise.all(promises)).resolves.not.toThrow()
    })
  })

  describe('Performance and Scalability', () => {
    it('should handle large datasets efficiently', async () => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        [`field_${i}`]: `value_${i}`,
        first_name: `Name_${i}`,
        phone: `1380013800${i.toString().padStart(2, '0')}`,
      })).reduce((acc, item) => ({ ...acc, ...item }), {})

      const startTime = Date.now()
      const encrypted = await hipaaManager.encryptPHI(largeDataset, 'clients')
      const encryptionTime = Date.now() - startTime

      expect(encryptionTime).toBeLessThan(5000) // Should complete within 5 seconds
      expect(Object.keys(encrypted)).toHaveLength(Object.keys(largeDataset).length)
    })

    it('should maintain performance with frequent access logging', async () => {
      const startTime = Date.now()
      
      const promises = Array.from({ length: 100 }, (_, i) =>
        hipaaManager.logPHIAccess(
          'performance-test-user',
          AccessPurpose.TREATMENT,
          [`clients.field_${i}`]
        )
      )

      await Promise.all(promises)
      const totalTime = Date.now() - startTime

      expect(totalTime).toBeLessThan(2000) // Should complete within 2 seconds
    })
  })
})
