import { test, expect } from '@playwright/test'

test.describe('Client Management Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the clients page
    await page.goto('/dashboard/clients')
    await page.waitForLoadState('networkidle')
  })

  test('should create a new client successfully', async ({ page }) => {
    // Click the "新建客户" button
    await page.click('button:has-text("新建客户")')
    
    // Wait for modal to appear
    await expect(page.locator('[role="dialog"]')).toBeVisible()
    await expect(page.locator('text=新建客户')).toBeVisible()

    // Fill in the form
    await page.fill('input[name="first_name"]', '测试')
    await page.fill('input[name="last_name"]', '用户')
    await page.fill('input[name="phone"]', '18888888888')
    await page.fill('input[name="email"]', '<EMAIL>')

    // Submit the form
    await page.click('button:has-text("创建客户")')

    // Wait for success message
    await expect(page.locator('text=客户创建成功')).toBeVisible()

    // Verify the modal closes
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()

    // Verify the new client appears in the list
    await expect(page.locator('text=测试 用户')).toBeVisible()
    await expect(page.locator('text=18888888888')).toBeVisible()
  })

  test('should validate required fields', async ({ page }) => {
    await page.click('button:has-text("新建客户")')
    await expect(page.locator('[role="dialog"]')).toBeVisible()

    // Try to submit without filling required fields
    await page.click('button:has-text("创建客户")')

    // Check for validation errors
    await expect(page.locator('text=姓名为必填项')).toBeVisible()
    await expect(page.locator('text=电话为必填项')).toBeVisible()
  })

  test('should validate phone number format', async ({ page }) => {
    await page.click('button:has-text("新建客户")')
    await expect(page.locator('[role="dialog"]')).toBeVisible()

    // Fill in invalid phone number
    await page.fill('input[name="first_name"]', '测试')
    await page.fill('input[name="last_name"]', '用户')
    await page.fill('input[name="phone"]', 'invalid-phone')

    await page.click('button:has-text("创建客户")')

    // Check for phone validation error
    await expect(page.locator('text=请输入有效的电话号码')).toBeVisible()
  })

  test('should edit an existing client', async ({ page }) => {
    // First, ensure there's a client to edit (create one if needed)
    await page.click('button:has-text("新建客户")')
    await page.fill('input[name="first_name"]', '编辑')
    await page.fill('input[name="last_name"]', '测试')
    await page.fill('input[name="phone"]', '19999999999')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.click('button:has-text("创建客户")')
    await expect(page.locator('text=客户创建成功')).toBeVisible()

    // Find and click the edit button for the client
    const clientRow = page.locator('tr:has-text("编辑 测试")')
    await clientRow.locator('button:has-text("编辑")').click()

    // Wait for edit modal
    await expect(page.locator('text=编辑客户')).toBeVisible()

    // Modify the email
    await page.fill('input[name="email"]', '<EMAIL>')

    // Submit the update
    await page.click('button:has-text("更新客户")')

    // Wait for success message
    await expect(page.locator('text=客户更新成功')).toBeVisible()

    // Verify the updated information appears
    await expect(page.locator('text=<EMAIL>')).toBeVisible()
  })

  test('should view client details', async ({ page }) => {
    // Create a client first
    await page.click('button:has-text("新建客户")')
    await page.fill('input[name="first_name"]', '查看')
    await page.fill('input[name="last_name"]', '详情')
    await page.fill('input[name="phone"]', '17777777777')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.click('button:has-text("创建客户")')
    await expect(page.locator('text=客户创建成功')).toBeVisible()

    // Find and click the view button
    const clientRow = page.locator('tr:has-text("查看 详情")')
    await clientRow.locator('button:has-text("查看")').click()

    // Wait for view modal
    await expect(page.locator('text=客户详情')).toBeVisible()

    // Verify client information is displayed
    await expect(page.locator('text=查看')).toBeVisible()
    await expect(page.locator('text=详情')).toBeVisible()
    await expect(page.locator('text=17777777777')).toBeVisible()
    await expect(page.locator('text=<EMAIL>')).toBeVisible()

    // Verify it's in view mode (no form inputs)
    await expect(page.locator('input[name="first_name"]')).not.toBeVisible()
  })

  test('should search for clients', async ({ page }) => {
    // Create a few clients first
    const clients = [
      { first: '搜索', last: '测试1', phone: '16666666661', email: '<EMAIL>' },
      { first: '搜索', last: '测试2', phone: '16666666662', email: '<EMAIL>' },
      { first: '其他', last: '用户', phone: '16666666663', email: '<EMAIL>' },
    ]

    for (const client of clients) {
      await page.click('button:has-text("新建客户")')
      await page.fill('input[name="first_name"]', client.first)
      await page.fill('input[name="last_name"]', client.last)
      await page.fill('input[name="phone"]', client.phone)
      await page.fill('input[name="email"]', client.email)
      await page.click('button:has-text("创建客户")')
      await expect(page.locator('text=客户创建成功')).toBeVisible()
    }

    // Search for "搜索"
    await page.fill('input[placeholder*="搜索"]', '搜索')
    await page.press('input[placeholder*="搜索"]', 'Enter')

    // Wait for search results
    await page.waitForTimeout(1000)

    // Verify search results
    await expect(page.locator('text=搜索 测试1')).toBeVisible()
    await expect(page.locator('text=搜索 测试2')).toBeVisible()
    await expect(page.locator('text=其他 用户')).not.toBeVisible()
  })

  test('should handle duplicate phone numbers', async ({ page }) => {
    // Create first client
    await page.click('button:has-text("新建客户")')
    await page.fill('input[name="first_name"]', '第一个')
    await page.fill('input[name="last_name"]', '用户')
    await page.fill('input[name="phone"]', '15555555555')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.click('button:has-text("创建客户")')
    await expect(page.locator('text=客户创建成功')).toBeVisible()

    // Try to create second client with same phone
    await page.click('button:has-text("新建客户")')
    await page.fill('input[name="first_name"]', '第二个')
    await page.fill('input[name="last_name"]', '用户')
    await page.fill('input[name="phone"]', '15555555555') // Same phone number
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.click('button:has-text("创建客户")')

    // Should show error message
    await expect(page.locator('text=电话号码已存在')).toBeVisible()
  })

  test('should cancel client creation', async ({ page }) => {
    await page.click('button:has-text("新建客户")')
    await expect(page.locator('[role="dialog"]')).toBeVisible()

    // Fill in some data
    await page.fill('input[name="first_name"]', '取消')
    await page.fill('input[name="last_name"]', '测试')

    // Click cancel
    await page.click('button:has-text("取消")')

    // Modal should close
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()

    // Client should not be created
    await expect(page.locator('text=取消 测试')).not.toBeVisible()
  })

  test('should close modal with X button', async ({ page }) => {
    await page.click('button:has-text("新建客户")')
    await expect(page.locator('[role="dialog"]')).toBeVisible()

    // Click the X button
    await page.click('[role="dialog"] button[aria-label="Close"]')

    // Modal should close
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()
  })

  test('should show loading state during creation', async ({ page }) => {
    await page.click('button:has-text("新建客户")')
    
    await page.fill('input[name="first_name"]', '加载')
    await page.fill('input[name="last_name"]', '测试')
    await page.fill('input[name="phone"]', '14444444444')
    await page.fill('input[name="email"]', '<EMAIL>')

    // Click submit and immediately check for loading state
    await page.click('button:has-text("创建客户")')
    
    // Should show loading text
    await expect(page.locator('text=创建中...')).toBeVisible()
    
    // Button should be disabled
    await expect(page.locator('button:has-text("创建中...")')).toBeDisabled()
  })
})
