/**
 * Service Worker for Medical Aesthetics CRM
 * Provides offline functionality, caching, and background sync
 */

const CACHE_NAME = 'medical-crm-v1.0.0'
const STATIC_CACHE_NAME = 'medical-crm-static-v1.0.0'
const DYNAMIC_CACHE_NAME = 'medical-crm-dynamic-v1.0.0'
const API_CACHE_NAME = 'medical-crm-api-v1.0.0'

// Static assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/offline',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  // Add other critical static assets
]

// API endpoints to cache
const API_ENDPOINTS = [
  '/api/clients',
  '/api/appointments',
  '/api/treatments',
  '/api/payments',
]

// Maximum cache sizes
const MAX_DYNAMIC_CACHE_SIZE = 50
const MAX_API_CACHE_SIZE = 100

/**
 * Install event - cache static assets
 */
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...')
  
  event.waitUntil(
    Promise.all([
      // Cache static assets
      caches.open(STATIC_CACHE_NAME).then((cache) => {
        console.log('Service Worker: Caching static assets')
        return cache.addAll(STATIC_ASSETS)
      }),
      // Skip waiting to activate immediately
      self.skipWaiting()
    ])
  )
})

/**
 * Activate event - clean up old caches
 */
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...')
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (
              cacheName !== CACHE_NAME &&
              cacheName !== STATIC_CACHE_NAME &&
              cacheName !== DYNAMIC_CACHE_NAME &&
              cacheName !== API_CACHE_NAME
            ) {
              console.log('Service Worker: Deleting old cache:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      }),
      // Claim all clients
      self.clients.claim()
    ])
  )
})

/**
 * Fetch event - handle network requests
 */
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)
  
  // Skip non-GET requests and chrome-extension requests
  if (request.method !== 'GET' || url.protocol === 'chrome-extension:') {
    return
  }
  
  // Handle different types of requests
  if (url.pathname.startsWith('/api/')) {
    // API requests - network first with cache fallback
    event.respondWith(handleApiRequest(request))
  } else if (isStaticAsset(url.pathname)) {
    // Static assets - cache first
    event.respondWith(handleStaticAsset(request))
  } else {
    // HTML pages - network first with cache fallback
    event.respondWith(handlePageRequest(request))
  }
})

/**
 * Handle API requests with network-first strategy
 */
async function handleApiRequest(request) {
  const cache = await caches.open(API_CACHE_NAME)
  
  try {
    // Try network first
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      // Cache successful responses
      const responseClone = networkResponse.clone()
      await cache.put(request, responseClone)
      
      // Limit cache size
      await limitCacheSize(API_CACHE_NAME, MAX_API_CACHE_SIZE)
    }
    
    return networkResponse
  } catch (error) {
    console.log('Service Worker: Network failed, trying cache for:', request.url)
    
    // Network failed, try cache
    const cachedResponse = await cache.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    
    // Return offline response for API requests
    return new Response(
      JSON.stringify({
        error: 'Offline',
        message: '当前处于离线状态，请检查网络连接',
        offline: true
      }),
      {
        status: 503,
        statusText: 'Service Unavailable',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )
  }
}

/**
 * Handle static assets with cache-first strategy
 */
async function handleStaticAsset(request) {
  const cache = await caches.open(STATIC_CACHE_NAME)
  
  // Try cache first
  const cachedResponse = await cache.match(request)
  if (cachedResponse) {
    return cachedResponse
  }
  
  try {
    // Cache miss, try network
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      // Cache the response
      const responseClone = networkResponse.clone()
      await cache.put(request, responseClone)
    }
    
    return networkResponse
  } catch (error) {
    console.log('Service Worker: Failed to fetch static asset:', request.url)
    
    // Return a fallback response or throw
    throw error
  }
}

/**
 * Handle page requests with network-first strategy
 */
async function handlePageRequest(request) {
  const cache = await caches.open(DYNAMIC_CACHE_NAME)
  
  try {
    // Try network first
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      // Cache successful HTML responses
      if (networkResponse.headers.get('content-type')?.includes('text/html')) {
        const responseClone = networkResponse.clone()
        await cache.put(request, responseClone)
        
        // Limit cache size
        await limitCacheSize(DYNAMIC_CACHE_NAME, MAX_DYNAMIC_CACHE_SIZE)
      }
    }
    
    return networkResponse
  } catch (error) {
    console.log('Service Worker: Network failed, trying cache for:', request.url)
    
    // Network failed, try cache
    const cachedResponse = await cache.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    
    // Return offline page
    const offlineResponse = await cache.match('/offline')
    if (offlineResponse) {
      return offlineResponse
    }
    
    // Fallback offline response
    return new Response(
      `
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>离线状态 - 医美诊所管理系统</title>
        <style>
          body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex; 
            align-items: center; 
            justify-content: center; 
            min-height: 100vh; 
            margin: 0; 
            background: #f9fafb;
            color: #374151;
          }
          .offline-container {
            text-align: center;
            padding: 2rem;
            max-width: 400px;
          }
          .offline-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
          }
          .offline-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
          }
          .offline-message {
            color: #6b7280;
            margin-bottom: 2rem;
          }
          .retry-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.2s;
          }
          .retry-button:hover {
            background: #2563eb;
          }
        </style>
      </head>
      <body>
        <div class="offline-container">
          <div class="offline-icon">📱</div>
          <h1 class="offline-title">当前处于离线状态</h1>
          <p class="offline-message">
            请检查您的网络连接，然后重试。
          </p>
          <button class="retry-button" onclick="window.location.reload()">
            重新加载
          </button>
        </div>
      </body>
      </html>
      `,
      {
        status: 200,
        headers: {
          'Content-Type': 'text/html',
        },
      }
    )
  }
}

/**
 * Check if a path is a static asset
 */
function isStaticAsset(pathname) {
  const staticExtensions = ['.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2']
  return staticExtensions.some(ext => pathname.endsWith(ext)) || pathname.startsWith('/icons/')
}

/**
 * Limit cache size by removing oldest entries
 */
async function limitCacheSize(cacheName, maxSize) {
  const cache = await caches.open(cacheName)
  const keys = await cache.keys()
  
  if (keys.length > maxSize) {
    // Remove oldest entries (FIFO)
    const keysToDelete = keys.slice(0, keys.length - maxSize)
    await Promise.all(keysToDelete.map(key => cache.delete(key)))
    console.log(`Service Worker: Cleaned ${keysToDelete.length} entries from ${cacheName}`)
  }
}

/**
 * Background sync for offline actions
 */
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync triggered:', event.tag)
  
  if (event.tag === 'background-sync-appointments') {
    event.waitUntil(syncAppointments())
  } else if (event.tag === 'background-sync-payments') {
    event.waitUntil(syncPayments())
  }
})

/**
 * Sync appointments when back online
 */
async function syncAppointments() {
  try {
    // Get pending appointments from IndexedDB or localStorage
    const pendingAppointments = await getPendingAppointments()
    
    for (const appointment of pendingAppointments) {
      try {
        const response = await fetch('/api/appointments', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(appointment),
        })
        
        if (response.ok) {
          await removePendingAppointment(appointment.id)
          console.log('Service Worker: Synced appointment:', appointment.id)
        }
      } catch (error) {
        console.error('Service Worker: Failed to sync appointment:', error)
      }
    }
  } catch (error) {
    console.error('Service Worker: Background sync failed:', error)
  }
}

/**
 * Sync payments when back online
 */
async function syncPayments() {
  try {
    // Get pending payments from IndexedDB or localStorage
    const pendingPayments = await getPendingPayments()
    
    for (const payment of pendingPayments) {
      try {
        const response = await fetch('/api/payments', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payment),
        })
        
        if (response.ok) {
          await removePendingPayment(payment.id)
          console.log('Service Worker: Synced payment:', payment.id)
        }
      } catch (error) {
        console.error('Service Worker: Failed to sync payment:', error)
      }
    }
  } catch (error) {
    console.error('Service Worker: Background sync failed:', error)
  }
}

/**
 * Placeholder functions for offline data management
 * These would be implemented with IndexedDB in a real application
 */
async function getPendingAppointments() {
  // TODO: Implement with IndexedDB
  return []
}

async function removePendingAppointment(id) {
  // TODO: Implement with IndexedDB
  console.log('Removing pending appointment:', id)
}

async function getPendingPayments() {
  // TODO: Implement with IndexedDB
  return []
}

async function removePendingPayment(id) {
  // TODO: Implement with IndexedDB
  console.log('Removing pending payment:', id)
}

/**
 * Push notification handling
 */
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push notification received')
  
  const options = {
    body: '您有新的预约提醒',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1,
    },
    actions: [
      {
        action: 'explore',
        title: '查看详情',
        icon: '/icons/checkmark.png',
      },
      {
        action: 'close',
        title: '关闭',
        icon: '/icons/xmark.png',
      },
    ],
  }
  
  if (event.data) {
    const data = event.data.json()
    options.body = data.body || options.body
    options.data = { ...options.data, ...data }
  }
  
  event.waitUntil(
    self.registration.showNotification('医美诊所管理系统', options)
  )
})

/**
 * Notification click handling
 */
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification clicked')
  
  event.notification.close()
  
  if (event.action === 'explore') {
    // Open the app to the relevant page
    event.waitUntil(
      clients.openWindow('/appointments')
    )
  } else if (event.action === 'close') {
    // Just close the notification
    return
  } else {
    // Default action - open the app
    event.waitUntil(
      clients.openWindow('/')
    )
  }
})

console.log('Service Worker: Loaded successfully')
