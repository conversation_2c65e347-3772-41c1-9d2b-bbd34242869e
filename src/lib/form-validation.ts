/**
 * 统一的表单验证工具库
 * 提供常用的验证规则和错误消息
 */

import { useState, useCallback } from 'react'

export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  min?: number
  max?: number
  custom?: (value: any) => string | null
}

export interface ValidationResult {
  isValid: boolean
  errors: Record<string, string>
}

// 常用的正则表达式
export const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[\d\s\-\+\(\)]{10,}$/,
  phoneStrict: /^1[3-9]\d{9}$/, // 中国手机号
  postalCode: /^\d{5,6}$/,
  currency: /^\d+(\.\d{1,2})?$/,
  positiveNumber: /^\d*\.?\d+$/,
  integer: /^\d+$/,
  time: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
  date: /^\d{4}-\d{2}-\d{2}$/
}

// 错误消息模板
export const ERROR_MESSAGES = {
  required: '此字段为必填项',
  email: '请输入有效的邮箱地址',
  phone: '请输入有效的电话号码',
  phoneStrict: '请输入有效的手机号码（11位数字）',
  minLength: (min: number) => `至少需要${min}个字符`,
  maxLength: (max: number) => `不能超过${max}个字符`,
  min: (min: number) => `最小值为${min}`,
  max: (max: number) => `最大值为${max}`,
  currency: '请输入有效的金额（最多两位小数）',
  positiveNumber: '请输入有效的正数',
  integer: '请输入有效的整数',
  time: '请输入有效的时间格式（HH:MM）',
  date: '请输入有效的日期格式',
  custom: '输入格式不正确'
}

/**
 * 验证单个字段
 */
export function validateField(value: any, rules: ValidationRule): string | null {
  // 必填验证
  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    return ERROR_MESSAGES.required
  }

  // 如果值为空且不是必填，跳过其他验证
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return null
  }

  const stringValue = String(value).trim()

  // 长度验证
  if (rules.minLength && stringValue.length < rules.minLength) {
    return ERROR_MESSAGES.minLength(rules.minLength)
  }

  if (rules.maxLength && stringValue.length > rules.maxLength) {
    return ERROR_MESSAGES.maxLength(rules.maxLength)
  }

  // 数值验证
  if (rules.min !== undefined) {
    const numValue = Number(value)
    if (isNaN(numValue) || numValue < rules.min) {
      return ERROR_MESSAGES.min(rules.min)
    }
  }

  if (rules.max !== undefined) {
    const numValue = Number(value)
    if (isNaN(numValue) || numValue > rules.max) {
      return ERROR_MESSAGES.max(rules.max)
    }
  }

  // 正则表达式验证
  if (rules.pattern && !rules.pattern.test(stringValue)) {
    // 根据常用模式返回特定错误消息
    if (rules.pattern === VALIDATION_PATTERNS.email) {
      return ERROR_MESSAGES.email
    } else if (rules.pattern === VALIDATION_PATTERNS.phone) {
      return ERROR_MESSAGES.phone
    } else if (rules.pattern === VALIDATION_PATTERNS.phoneStrict) {
      return ERROR_MESSAGES.phoneStrict
    } else if (rules.pattern === VALIDATION_PATTERNS.currency) {
      return ERROR_MESSAGES.currency
    } else if (rules.pattern === VALIDATION_PATTERNS.positiveNumber) {
      return ERROR_MESSAGES.positiveNumber
    } else if (rules.pattern === VALIDATION_PATTERNS.integer) {
      return ERROR_MESSAGES.integer
    } else if (rules.pattern === VALIDATION_PATTERNS.time) {
      return ERROR_MESSAGES.time
    } else if (rules.pattern === VALIDATION_PATTERNS.date) {
      return ERROR_MESSAGES.date
    } else {
      return ERROR_MESSAGES.custom
    }
  }

  // 自定义验证
  if (rules.custom) {
    return rules.custom(value)
  }

  return null
}

/**
 * 验证整个表单
 */
export function validateForm(data: Record<string, any>, rules: Record<string, ValidationRule>): ValidationResult {
  const errors: Record<string, string> = {}

  for (const [field, fieldRules] of Object.entries(rules)) {
    const error = validateField(data[field], fieldRules)
    if (error) {
      errors[field] = error
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

/**
 * 常用的验证规则预设
 */
export const COMMON_RULES = {
  // 客户相关
  firstName: { required: true, minLength: 1, maxLength: 50 },
  lastName: { required: true, minLength: 1, maxLength: 50 },
  email: { pattern: VALIDATION_PATTERNS.email, maxLength: 100 },
  phone: { required: true, pattern: VALIDATION_PATTERNS.phone },
  phoneStrict: { required: true, pattern: VALIDATION_PATTERNS.phoneStrict },

  // 地址相关
  addressLine: { maxLength: 200 },
  city: { maxLength: 100 },
  state: { maxLength: 100 },
  postalCode: { pattern: VALIDATION_PATTERNS.postalCode },
  country: { maxLength: 100 },

  // 金额相关
  currency: { required: true, pattern: VALIDATION_PATTERNS.currency, min: 0 },
  percentage: { required: true, pattern: VALIDATION_PATTERNS.positiveNumber, min: 0, max: 100 },
  positiveNumber: { required: true, pattern: VALIDATION_PATTERNS.positiveNumber, min: 0 },

  // 日期时间相关
  date: { required: true, pattern: VALIDATION_PATTERNS.date },
  time: { required: true, pattern: VALIDATION_PATTERNS.time },

  // 文本相关
  shortText: { maxLength: 100 },
  mediumText: { maxLength: 500 },
  longText: { maxLength: 2000 },
  notes: { maxLength: 1000 },

  // 治疗项目相关
  treatmentName: { required: true, minLength: 2, maxLength: 100 },
  duration: { required: true, pattern: VALIDATION_PATTERNS.integer, min: 1, max: 480 }, // 最多8小时
  category: { required: true, maxLength: 50 }
}

/**
 * 格式化输入值
 */
export const formatters = {
  // 格式化电话号码
  phone: (value: string): string => {
    return value.replace(/\D/g, '').slice(0, 11)
  },

  // 格式化金额
  currency: (value: string): string => {
    const cleaned = value.replace(/[^\d.]/g, '')
    const parts = cleaned.split('.')
    if (parts.length > 2) {
      return parts[0] + '.' + parts[1]
    }
    if (parts[1] && parts[1].length > 2) {
      return parts[0] + '.' + parts[1].slice(0, 2)
    }
    return cleaned
  },

  // 格式化百分比
  percentage: (value: string): string => {
    const cleaned = value.replace(/[^\d.]/g, '')
    const num = parseFloat(cleaned)
    if (isNaN(num)) return ''
    return Math.min(100, Math.max(0, num)).toString()
  },

  // 格式化时间
  time: (value: string): string => {
    const cleaned = value.replace(/[^\d:]/g, '')
    const parts = cleaned.split(':')
    if (parts.length >= 2) {
      const hours = Math.min(23, Math.max(0, parseInt(parts[0]) || 0))
      const minutes = Math.min(59, Math.max(0, parseInt(parts[1]) || 0))
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
    }
    return cleaned
  },

  // 格式化日期
  date: (value: string): string => {
    // 保持 YYYY-MM-DD 格式
    return value.replace(/[^\d-]/g, '').slice(0, 10)
  }
}

/**
 * 实时验证钩子
 */
export function useFormValidation(initialData: Record<string, any>, rules: Record<string, ValidationRule>) {
  const [data, setData] = useState(initialData)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})

  const validateFieldValue = useCallback((field: string, value: any) => {
    if (rules[field]) {
      const error = validateField(value, rules[field])
      setErrors(prev => ({
        ...prev,
        [field]: error || ''
      }))
      return !error
    }
    return true
  }, [rules])

  const updateField = useCallback((field: string, value: any) => {
    setData(prev => ({ ...prev, [field]: value }))
    if (touched[field]) {
      validateFieldValue(field, value)
    }
  }, [touched, validateFieldValue])

  const touchField = useCallback((field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }))
    validateFieldValue(field, data[field])
  }, [data, validateFieldValue])

  const validateAll = useCallback(() => {
    const result = validateForm(data, rules)
    setErrors(result.errors)
    setTouched(Object.keys(rules).reduce((acc, key) => ({ ...acc, [key]: true }), {}))
    return result.isValid
  }, [data, rules])

  return {
    data,
    errors,
    touched,
    updateField,
    touchField,
    validateAll,
    isValid: Object.keys(errors).every(key => !errors[key])
  }
}
