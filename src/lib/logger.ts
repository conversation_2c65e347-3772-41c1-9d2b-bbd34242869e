/**
 * Professional Logging System
 * Provides structured logging with different levels and contexts
 */

import { env, isDevelopment, isProduction } from './env';

/**
 * Log levels in order of severity
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4,
}

/**
 * Log entry structure
 */
export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context?: Record<string, unknown>;
  error?: Error;
  requestId?: string;
  userId?: string;
  sessionId?: string;
}

/**
 * Logger configuration
 */
interface LoggerConfig {
  minLevel: LogLevel;
  enableConsole: boolean;
  enableFile: boolean;
  enableRemote: boolean;
  serviceName: string;
  version: string;
}

/**
 * Default logger configuration
 */
const defaultConfig: LoggerConfig = {
  minLevel: isDevelopment ? LogLevel.DEBUG : LogLevel.INFO,
  enableConsole: true,
  enableFile: false, // Would be enabled in production with proper file rotation
  enableRemote: isProduction, // Send to external logging service in production
  serviceName: 'medical-crm',
  version: process.env.npm_package_version || '1.0.0',
};

/**
 * Professional Logger Class
 */
class Logger {
  private config: LoggerConfig;
  private context: Record<string, unknown> = {};

  constructor(config: LoggerConfig = defaultConfig) {
    this.config = config;
  }

  /**
   * Set global context that will be included in all log entries
   */
  setContext(context: Record<string, unknown>): void {
    this.context = { ...this.context, ...context };
  }

  /**
   * Clear global context
   */
  clearContext(): void {
    this.context = {};
  }

  /**
   * Create a child logger with additional context
   */
  child(context: Record<string, unknown>): Logger {
    const childLogger = new Logger(this.config);
    childLogger.context = { ...this.context, ...context };
    return childLogger;
  }

  /**
   * Log a debug message
   */
  debug(message: string, context?: Record<string, unknown>): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  /**
   * Log an info message
   */
  info(message: string, context?: Record<string, unknown>): void {
    this.log(LogLevel.INFO, message, context);
  }

  /**
   * Log a warning message
   */
  warn(message: string, context?: Record<string, unknown>): void {
    this.log(LogLevel.WARN, message, context);
  }

  /**
   * Log an error message
   */
  error(message: string, error?: Error, context?: Record<string, unknown>): void {
    this.log(LogLevel.ERROR, message, context, error);
  }

  /**
   * Log a fatal error message
   */
  fatal(message: string, error?: Error, context?: Record<string, unknown>): void {
    this.log(LogLevel.FATAL, message, context, error);
  }

  /**
   * Core logging method
   */
  private log(
    level: LogLevel,
    message: string,
    context?: Record<string, unknown>,
    error?: Error
  ): void {
    // Skip if below minimum level
    if (level < this.config.minLevel) {
      return;
    }

    const logEntry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      context: { ...this.context, ...context },
      error,
    };

    // Console logging
    if (this.config.enableConsole) {
      this.logToConsole(logEntry);
    }

    // File logging (would be implemented for production)
    if (this.config.enableFile) {
      this.logToFile(logEntry);
    }

    // Remote logging (would be implemented for production)
    if (this.config.enableRemote) {
      this.logToRemote(logEntry);
    }
  }

  /**
   * Log to console with appropriate formatting
   */
  private logToConsole(entry: LogEntry): void {
    const levelName = LogLevel[entry.level];
    const prefix = `[${entry.timestamp}] ${levelName}:`;
    
    const contextStr = entry.context && Object.keys(entry.context).length > 0
      ? `\nContext: ${JSON.stringify(entry.context, null, 2)}`
      : '';

    const errorStr = entry.error
      ? `\nError: ${entry.error.message}\nStack: ${entry.error.stack}`
      : '';

    const fullMessage = `${prefix} ${entry.message}${contextStr}${errorStr}`;

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(fullMessage);
        break;
      case LogLevel.INFO:
        console.info(fullMessage);
        break;
      case LogLevel.WARN:
        console.warn(fullMessage);
        break;
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(fullMessage);
        break;
    }
  }

  /**
   * Log to file (placeholder for production implementation)
   */
  private logToFile(entry: LogEntry): void {
    // In production, this would write to a log file with proper rotation
    // For now, we'll just skip this in development
    if (isDevelopment) {
      return;
    }
    
    // TODO: Implement file logging with rotation
    // - Use a library like winston or pino
    // - Configure log rotation
    // - Handle file permissions
    // - Implement log compression
  }

  /**
   * Log to remote service (placeholder for production implementation)
   */
  private logToRemote(entry: LogEntry): void {
    // In production, this would send logs to a service like:
    // - Sentry for errors
    // - DataDog for general logging
    // - CloudWatch for AWS
    // - Google Cloud Logging for GCP
    
    if (isDevelopment) {
      return;
    }

    // TODO: Implement remote logging
    // - Send to external logging service
    // - Handle network failures gracefully
    // - Implement batching for performance
    // - Add retry logic
  }
}

/**
 * Default logger instance
 */
export const logger = new Logger();

/**
 * Request logger middleware helper
 */
export function createRequestLogger(requestId: string, userId?: string): Logger {
  return logger.child({
    requestId,
    userId,
    service: 'api',
  });
}

/**
 * Database operation logger
 */
export function createDbLogger(operation: string, table: string): Logger {
  return logger.child({
    operation,
    table,
    service: 'database',
  });
}

/**
 * Authentication logger
 */
export function createAuthLogger(action: string, userId?: string): Logger {
  return logger.child({
    action,
    userId,
    service: 'auth',
  });
}

/**
 * Business logic logger
 */
export function createBusinessLogger(domain: string, operation: string): Logger {
  return logger.child({
    domain,
    operation,
    service: 'business',
  });
}

/**
 * Performance timing utility
 */
export class PerformanceTimer {
  private startTime: number;
  private logger: Logger;
  private operation: string;

  constructor(logger: Logger, operation: string) {
    this.logger = logger;
    this.operation = operation;
    this.startTime = Date.now();
    this.logger.debug(`Starting ${operation}`);
  }

  /**
   * End timing and log duration
   */
  end(context?: Record<string, unknown>): void {
    const duration = Date.now() - this.startTime;
    this.logger.info(`Completed ${this.operation}`, {
      duration: `${duration}ms`,
      ...context,
    });
  }

  /**
   * End timing with error
   */
  error(error: Error, context?: Record<string, unknown>): void {
    const duration = Date.now() - this.startTime;
    this.logger.error(`Failed ${this.operation}`, error, {
      duration: `${duration}ms`,
      ...context,
    });
  }
}

/**
 * Create a performance timer
 */
export function createTimer(logger: Logger, operation: string): PerformanceTimer {
  return new PerformanceTimer(logger, operation);
}
