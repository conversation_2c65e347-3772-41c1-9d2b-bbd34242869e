export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

// Database types for medical clinic CRM
export type ClientStatus = 'active' | 'inactive' | 'archived'
export type AppointmentStatus = 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'
export type AppointmentType = 'consultation' | 'treatment' | 'follow_up'
export type InvoiceStatus = 'draft' | 'deposit_pending' | 'deposit_paid' | 'paid_in_full' | 'overdue' | 'cancelled'
export type PaymentMethod = 'cash' | 'credit_card' | 'debit_card' | 'bank_transfer' | 'check' | 'other'
export type PaymentType = 'deposit' | 'full_payment' | 'partial_payment'
export type ContactType = 'phone_call' | 'email' | 'in_person' | 'sms' | 'other'
export type ContactDirection = 'inbound' | 'outbound'

export interface Database {
  public: {
    Tables: {
      clients: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          first_name: string
          last_name: string
          phone: string
          email: string | null
          date_of_birth: string | null
          address_line_1: string | null
          address_line_2: string | null
          city: string | null
          state_province: string | null
          postal_code: string | null
          country: string | null
          latitude: number | null
          longitude: number | null
          emergency_contact_name: string | null
          emergency_contact_phone: string | null
          notes: string | null
          status: ClientStatus
          preferred_language: string
          referral_source: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          first_name: string
          last_name: string
          phone: string
          email?: string | null
          date_of_birth?: string | null
          address_line_1?: string | null
          address_line_2?: string | null
          city?: string | null
          state_province?: string | null
          postal_code?: string | null
          country?: string | null
          latitude?: number | null
          longitude?: number | null
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          notes?: string | null
          status?: ClientStatus
          preferred_language?: string
          referral_source?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          first_name?: string
          last_name?: string
          phone?: string
          email?: string | null
          date_of_birth?: string | null
          address_line_1?: string | null
          address_line_2?: string | null
          city?: string | null
          state_province?: string | null
          postal_code?: string | null
          country?: string | null
          latitude?: number | null
          longitude?: number | null
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          notes?: string | null
          status?: ClientStatus
          preferred_language?: string
          referral_source?: string | null
        }
      }
      treatments: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          name: string
          name_chinese: string
          description: string | null
          description_chinese: string | null
          default_price: number
          fixed_deposit_amount: number
          consultation_fee: number
          duration_minutes: number
          category: string
          is_active: boolean
          requires_consultation: boolean
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          name: string
          name_chinese: string
          description?: string | null
          description_chinese?: string | null
          default_price: number
          fixed_deposit_amount?: number
          consultation_fee?: number
          duration_minutes: number
          category: string
          is_active?: boolean
          requires_consultation?: boolean
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          name?: string
          name_chinese?: string
          description?: string | null
          description_chinese?: string | null
          default_price?: number
          fixed_deposit_amount?: number
          consultation_fee?: number
          duration_minutes?: number
          category?: string
          is_active?: boolean
          requires_consultation?: boolean
        }
      }
      appointments: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          client_id: string
          treatment_id: string
          appointment_date: string
          start_time: string
          end_time: string
          status: AppointmentStatus
          appointment_type: AppointmentType
          notes: string | null
          staff_notes: string | null
          custom_price: number | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          client_id: string
          treatment_id: string
          appointment_date: string
          start_time: string
          end_time: string
          status?: AppointmentStatus
          appointment_type?: AppointmentType
          notes?: string | null
          staff_notes?: string | null
          custom_price?: number | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          client_id?: string
          treatment_id?: string
          appointment_date?: string
          start_time?: string
          end_time?: string
          status?: AppointmentStatus
          appointment_type?: AppointmentType
          notes?: string | null
          staff_notes?: string | null
          custom_price?: number | null
        }
      }
      invoices: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          client_id: string
          invoice_number: string
          invoice_date: string
          treatment_date: string
          total_amount: number
          deposit_amount: number
          deposit_percentage: number
          consultation_fee_waived: boolean
          original_consultation_fee: number
          status: InvoiceStatus
          due_date: string
          notes: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          client_id: string
          invoice_number: string
          invoice_date: string
          treatment_date: string
          total_amount: number
          deposit_amount: number
          deposit_percentage?: number
          consultation_fee_waived?: boolean
          original_consultation_fee?: number
          status?: InvoiceStatus
          due_date: string
          notes?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          client_id?: string
          invoice_number?: string
          invoice_date?: string
          treatment_date?: string
          total_amount?: number
          deposit_amount?: number
          deposit_percentage?: number
          consultation_fee_waived?: boolean
          original_consultation_fee?: number
          status?: InvoiceStatus
          due_date?: string
          notes?: string | null
        }
      }
      invoice_items: {
        Row: {
          id: string
          created_at: string
          invoice_id: string
          appointment_id: string
          treatment_name: string
          treatment_name_chinese: string
          quantity: number
          unit_price: number
          total_price: number
        }
        Insert: {
          id?: string
          created_at?: string
          invoice_id: string
          appointment_id: string
          treatment_name: string
          treatment_name_chinese: string
          quantity?: number
          unit_price: number
          total_price: number
        }
        Update: {
          id?: string
          created_at?: string
          invoice_id?: string
          appointment_id?: string
          treatment_name?: string
          treatment_name_chinese?: string
          quantity?: number
          unit_price?: number
          total_price?: number
        }
      }
      payments: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          invoice_id: string
          client_id: string
          amount: number
          payment_date: string
          payment_method: PaymentMethod
          payment_type: PaymentType
          reference_number: string | null
          notes: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          invoice_id: string
          client_id: string
          amount: number
          payment_date: string
          payment_method: PaymentMethod
          payment_type: PaymentType
          reference_number?: string | null
          notes?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          invoice_id?: string
          client_id?: string
          amount?: number
          payment_date?: string
          payment_method?: PaymentMethod
          payment_type?: PaymentType
          reference_number?: string | null
          notes?: string | null
        }
      }
      contact_logs: {
        Row: {
          id: string
          created_at: string
          client_id: string
          contact_type: ContactType
          contact_direction: ContactDirection
          subject: string
          notes: string
          staff_member: string | null
          follow_up_required: boolean
          follow_up_date: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          client_id: string
          contact_type: ContactType
          contact_direction: ContactDirection
          subject: string
          notes: string
          staff_member?: string | null
          follow_up_required?: boolean
          follow_up_date?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          client_id?: string
          contact_type?: ContactType
          contact_direction?: ContactDirection
          subject?: string
          notes?: string
          staff_member?: string | null
          follow_up_required?: boolean
          follow_up_date?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
