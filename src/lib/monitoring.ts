/**
 * Professional Monitoring and Observability System
 * Provides comprehensive monitoring, metrics collection, and performance tracking
 */

import { logger } from './logger'
import { env, isProduction } from './env'

/**
 * Metric types for different kinds of measurements
 */
export enum MetricType {
  COUNTER = 'counter',
  GAUGE = 'gauge',
  HISTOGRAM = 'histogram',
  TIMER = 'timer',
}

/**
 * Metric data structure
 */
export interface Metric {
  name: string
  type: MetricType
  value: number
  tags?: Record<string, string>
  timestamp: number
}

/**
 * Performance measurement data
 */
export interface PerformanceMeasurement {
  name: string
  duration: number
  startTime: number
  endTime: number
  metadata?: Record<string, unknown>
}

/**
 * Health check status
 */
export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy'
  checks: Record<string, {
    status: 'pass' | 'fail' | 'warn'
    message?: string
    duration?: number
  }>
  timestamp: number
}

/**
 * Professional Monitoring Class
 */
class Monitor {
  private metrics: Metric[] = []
  private healthChecks: Map<string, () => Promise<{ status: 'pass' | 'fail' | 'warn'; message?: string }>> = new Map()

  /**
   * Record a counter metric (incremental value)
   */
  counter(name: string, value: number = 1, tags?: Record<string, string>): void {
    this.recordMetric({
      name,
      type: MetricType.COUNTER,
      value,
      tags,
      timestamp: Date.now(),
    })
  }

  /**
   * Record a gauge metric (current value)
   */
  gauge(name: string, value: number, tags?: Record<string, string>): void {
    this.recordMetric({
      name,
      type: MetricType.GAUGE,
      value,
      tags,
      timestamp: Date.now(),
    })
  }

  /**
   * Record a histogram metric (distribution of values)
   */
  histogram(name: string, value: number, tags?: Record<string, string>): void {
    this.recordMetric({
      name,
      type: MetricType.HISTOGRAM,
      value,
      tags,
      timestamp: Date.now(),
    })
  }

  /**
   * Create a timer for measuring duration
   */
  timer(name: string, tags?: Record<string, string>): Timer {
    return new Timer(name, tags, this)
  }

  /**
   * Record a metric
   */
  private recordMetric(metric: Metric): void {
    this.metrics.push(metric)
    
    // Log metric in development
    if (!isProduction) {
      logger.debug(`Metric recorded: ${metric.name}`, {
        type: metric.type,
        value: metric.value,
        tags: metric.tags,
      })
    }

    // In production, send to monitoring service
    if (isProduction) {
      this.sendToMonitoringService(metric)
    }

    // Keep only recent metrics in memory (last 1000)
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000)
    }
  }

  /**
   * Send metric to external monitoring service
   */
  private sendToMonitoringService(metric: Metric): void {
    // TODO: Implement integration with monitoring services like:
    // - DataDog
    // - New Relic
    // - Prometheus
    // - CloudWatch
    // - Google Cloud Monitoring
    
    // For now, just log in production
    logger.info('Metric sent to monitoring service', { metric })
  }

  /**
   * Register a health check
   */
  registerHealthCheck(
    name: string,
    check: () => Promise<{ status: 'pass' | 'fail' | 'warn'; message?: string }>
  ): void {
    this.healthChecks.set(name, check)
  }

  /**
   * Run all health checks
   */
  async runHealthChecks(): Promise<HealthStatus> {
    const checks: HealthStatus['checks'] = {}
    let overallStatus: HealthStatus['status'] = 'healthy'

    for (const [name, check] of this.healthChecks) {
      const startTime = Date.now()
      try {
        const result = await check()
        const duration = Date.now() - startTime
        
        checks[name] = {
          ...result,
          duration,
        }

        if (result.status === 'fail') {
          overallStatus = 'unhealthy'
        } else if (result.status === 'warn' && overallStatus === 'healthy') {
          overallStatus = 'degraded'
        }
      } catch (error) {
        checks[name] = {
          status: 'fail',
          message: error instanceof Error ? error.message : 'Unknown error',
          duration: Date.now() - startTime,
        }
        overallStatus = 'unhealthy'
      }
    }

    return {
      status: overallStatus,
      checks,
      timestamp: Date.now(),
    }
  }

  /**
   * Get current metrics
   */
  getMetrics(): Metric[] {
    return [...this.metrics]
  }

  /**
   * Clear metrics
   */
  clearMetrics(): void {
    this.metrics = []
  }
}

/**
 * Timer class for measuring durations
 */
class Timer {
  private startTime: number
  private name: string
  private tags?: Record<string, string>
  private monitor: Monitor

  constructor(name: string, tags: Record<string, string> | undefined, monitor: Monitor) {
    this.name = name
    this.tags = tags
    this.monitor = monitor
    this.startTime = Date.now()
  }

  /**
   * Stop the timer and record the duration
   */
  stop(additionalTags?: Record<string, string>): number {
    const duration = Date.now() - this.startTime
    
    this.monitor.recordMetric({
      name: this.name,
      type: MetricType.TIMER,
      value: duration,
      tags: { ...this.tags, ...additionalTags },
      timestamp: Date.now(),
    })

    return duration
  }
}

/**
 * Default monitor instance
 */
export const monitor = new Monitor()

/**
 * API monitoring helpers
 */
export const apiMonitoring = {
  /**
   * Monitor API request
   */
  request: (method: string, endpoint: string, statusCode: number, duration: number) => {
    monitor.counter('api.requests.total', 1, {
      method,
      endpoint,
      status_code: statusCode.toString(),
    })

    monitor.histogram('api.request.duration', duration, {
      method,
      endpoint,
    })

    if (statusCode >= 400) {
      monitor.counter('api.errors.total', 1, {
        method,
        endpoint,
        status_code: statusCode.toString(),
      })
    }
  },

  /**
   * Monitor database query
   */
  dbQuery: (table: string, operation: string, duration: number, success: boolean) => {
    monitor.counter('db.queries.total', 1, {
      table,
      operation,
      success: success.toString(),
    })

    monitor.histogram('db.query.duration', duration, {
      table,
      operation,
    })

    if (!success) {
      monitor.counter('db.errors.total', 1, {
        table,
        operation,
      })
    }
  },
}

/**
 * Business metrics helpers
 */
export const businessMetrics = {
  /**
   * Track client operations
   */
  clientOperation: (operation: 'create' | 'update' | 'delete' | 'view') => {
    monitor.counter('business.clients.operations', 1, { operation })
  },

  /**
   * Track appointment operations
   */
  appointmentOperation: (operation: 'create' | 'update' | 'cancel' | 'complete') => {
    monitor.counter('business.appointments.operations', 1, { operation })
  },

  /**
   * Track payment operations
   */
  paymentOperation: (method: string, amount: number) => {
    monitor.counter('business.payments.total', 1, { method })
    monitor.histogram('business.payments.amount', amount, { method })
  },
}

/**
 * System health checks
 */
export const healthChecks = {
  /**
   * Database connectivity check
   */
  database: async () => {
    try {
      // TODO: Implement actual database connectivity check
      // const { data, error } = await supabase.from('clients').select('id').limit(1)
      // if (error) throw error
      
      return { status: 'pass' as const, message: 'Database connection healthy' }
    } catch (error) {
      return { 
        status: 'fail' as const, 
        message: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      }
    }
  },

  /**
   * Memory usage check
   */
  memory: async () => {
    const usage = process.memoryUsage()
    const heapUsedMB = Math.round(usage.heapUsed / 1024 / 1024)
    const heapTotalMB = Math.round(usage.heapTotal / 1024 / 1024)
    
    // Warn if heap usage is over 80%
    const usagePercent = (heapUsedMB / heapTotalMB) * 100
    
    if (usagePercent > 90) {
      return { 
        status: 'fail' as const, 
        message: `High memory usage: ${heapUsedMB}MB (${usagePercent.toFixed(1)}%)` 
      }
    } else if (usagePercent > 80) {
      return { 
        status: 'warn' as const, 
        message: `Elevated memory usage: ${heapUsedMB}MB (${usagePercent.toFixed(1)}%)` 
      }
    }
    
    return { 
      status: 'pass' as const, 
      message: `Memory usage normal: ${heapUsedMB}MB (${usagePercent.toFixed(1)}%)` 
    }
  },

  /**
   * External service check (placeholder)
   */
  externalServices: async () => {
    // TODO: Check external services like Clerk, Supabase, etc.
    return { status: 'pass' as const, message: 'All external services healthy' }
  },
}

// Register default health checks
monitor.registerHealthCheck('database', healthChecks.database)
monitor.registerHealthCheck('memory', healthChecks.memory)
monitor.registerHealthCheck('external_services', healthChecks.externalServices)

/**
 * Performance monitoring decorator
 */
export function monitored(metricName: string, tags?: Record<string, string>) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const timer = monitor.timer(metricName, tags)
      try {
        const result = await method.apply(this, args)
        timer.stop({ success: 'true' })
        return result
      } catch (error) {
        timer.stop({ success: 'false' })
        throw error
      }
    }

    return descriptor
  }
}
