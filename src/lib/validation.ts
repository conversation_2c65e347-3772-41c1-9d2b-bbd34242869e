/**
 * Comprehensive API Validation System
 * Provides type-safe validation for all API endpoints and data structures
 */

import { z } from 'zod';
import { ValidationError } from './errors';

/**
 * Common validation schemas
 */
export const commonSchemas = {
  // UUID validation
  uuid: z.string().uuid('无效的ID格式'),
  
  // Email validation
  email: z.string().email('无效的邮箱格式'),
  
  // Phone number validation (supports various formats)
  phone: z.string().regex(
    /^[\+]?[1-9][\d]{0,15}$/,
    '无效的电话号码格式'
  ),
  
  // Date validation
  date: z.string().regex(
    /^\d{4}-\d{2}-\d{2}$/,
    '日期格式必须为 YYYY-MM-DD'
  ),
  
  // Time validation
  time: z.string().regex(
    /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
    '时间格式必须为 HH:MM'
  ),
  
  // Currency amount validation
  currency: z.number().min(0, '金额不能为负数').max(999999.99, '金额过大'),
  
  // Pagination parameters
  pagination: z.object({
    page: z.number().int().min(1, '页码必须大于0').default(1),
    limit: z.number().int().min(1, '每页数量必须大于0').max(100, '每页数量不能超过100').default(20),
  }),
  
  // Search query
  searchQuery: z.string().min(1, '搜索关键词不能为空').max(100, '搜索关键词过长'),
};

/**
 * Client validation schemas
 */
export const clientSchemas = {
  create: z.object({
    first_name: z.string().min(1, '姓名不能为空').max(50, '姓名过长'),
    last_name: z.string().min(1, '姓名不能为空').max(50, '姓名过长'),
    phone: commonSchemas.phone,
    email: commonSchemas.email.optional(),
    date_of_birth: commonSchemas.date.optional(),
    gender: z.enum(['male', 'female', 'other']).optional(),
    address: z.string().max(200, '地址过长').optional(),
    emergency_contact_name: z.string().max(100, '紧急联系人姓名过长').optional(),
    emergency_contact_phone: commonSchemas.phone.optional(),
    referral_source: z.string().max(100, '推荐来源过长').optional(),
    notes: z.string().max(1000, '备注过长').optional(),
  }),
  
  update: z.object({
    first_name: z.string().min(1, '姓名不能为空').max(50, '姓名过长').optional(),
    last_name: z.string().min(1, '姓名不能为空').max(50, '姓名过长').optional(),
    phone: commonSchemas.phone.optional(),
    email: commonSchemas.email.optional(),
    date_of_birth: commonSchemas.date.optional(),
    gender: z.enum(['male', 'female', 'other']).optional(),
    address: z.string().max(200, '地址过长').optional(),
    emergency_contact_name: z.string().max(100, '紧急联系人姓名过长').optional(),
    emergency_contact_phone: commonSchemas.phone.optional(),
    referral_source: z.string().max(100, '推荐来源过长').optional(),
    notes: z.string().max(1000, '备注过长').optional(),
    status: z.enum(['active', 'inactive', 'archived']).optional(),
  }),
  
  search: z.object({
    q: commonSchemas.searchQuery,
    ...commonSchemas.pagination.shape,
  }),
};

/**
 * Treatment validation schemas
 */
export const treatmentSchemas = {
  create: z.object({
    name_chinese: z.string().min(1, '中文名称不能为空').max(100, '中文名称过长'),
    name_english: z.string().min(1, '英文名称不能为空').max(100, '英文名称过长'),
    category: z.string().min(1, '分类不能为空').max(50, '分类名称过长'),
    price: commonSchemas.currency,
    duration_minutes: z.number().int().min(15, '治疗时长至少15分钟').max(480, '治疗时长不能超过8小时'),
    fixed_deposit_amount: commonSchemas.currency,
    requires_consultation: z.boolean().default(false),
    description: z.string().max(500, '描述过长').optional(),
    is_active: z.boolean().default(true),
  }),
  
  update: z.object({
    name_chinese: z.string().min(1, '中文名称不能为空').max(100, '中文名称过长').optional(),
    name_english: z.string().min(1, '英文名称不能为空').max(100, '英文名称过长').optional(),
    category: z.string().min(1, '分类不能为空').max(50, '分类名称过长').optional(),
    price: commonSchemas.currency.optional(),
    duration_minutes: z.number().int().min(15, '治疗时长至少15分钟').max(480, '治疗时长不能超过8小时').optional(),
    fixed_deposit_amount: commonSchemas.currency.optional(),
    requires_consultation: z.boolean().optional(),
    description: z.string().max(500, '描述过长').optional(),
    is_active: z.boolean().optional(),
  }),
};

/**
 * Appointment validation schemas
 */
export const appointmentSchemas = {
  create: z.object({
    client_id: commonSchemas.uuid,
    treatment_id: commonSchemas.uuid,
    appointment_date: commonSchemas.date,
    start_time: commonSchemas.time,
    end_time: commonSchemas.time,
    custom_price: commonSchemas.currency.optional(),
    notes: z.string().max(500, '备注过长').optional(),
  }).refine(
    (data) => data.start_time < data.end_time,
    {
      message: '结束时间必须晚于开始时间',
      path: ['end_time'],
    }
  ),
  
  update: z.object({
    client_id: commonSchemas.uuid.optional(),
    treatment_id: commonSchemas.uuid.optional(),
    appointment_date: commonSchemas.date.optional(),
    start_time: commonSchemas.time.optional(),
    end_time: commonSchemas.time.optional(),
    status: z.enum(['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show']).optional(),
    custom_price: commonSchemas.currency.optional(),
    notes: z.string().max(500, '备注过长').optional(),
  }).refine(
    (data) => {
      if (data.start_time && data.end_time) {
        return data.start_time < data.end_time;
      }
      return true;
    },
    {
      message: '结束时间必须晚于开始时间',
      path: ['end_time'],
    }
  ),
  
  dateRange: z.object({
    start_date: commonSchemas.date,
    end_date: commonSchemas.date,
  }).refine(
    (data) => data.start_date <= data.end_date,
    {
      message: '结束日期必须晚于或等于开始日期',
      path: ['end_date'],
    }
  ),
  
  conflictCheck: z.object({
    appointment_date: commonSchemas.date,
    start_time: commonSchemas.time,
    end_time: commonSchemas.time,
    exclude_appointment_id: commonSchemas.uuid.optional(),
  }),
};

/**
 * Invoice validation schemas
 */
export const invoiceSchemas = {
  create: z.object({
    client_id: commonSchemas.uuid,
    appointment_ids: z.array(commonSchemas.uuid).min(1, '至少需要一个预约'),
    due_date: commonSchemas.date,
    notes: z.string().max(500, '备注过长').optional(),
  }),
  
  update: z.object({
    status: z.enum(['draft', 'sent', 'paid', 'overdue', 'cancelled']).optional(),
    due_date: commonSchemas.date.optional(),
    notes: z.string().max(500, '备注过长').optional(),
  }),
};

/**
 * Payment validation schemas
 */
export const paymentSchemas = {
  create: z.object({
    invoice_id: commonSchemas.uuid,
    amount: commonSchemas.currency,
    payment_method: z.enum(['cash', 'card', 'bank_transfer', 'alipay', 'wechat_pay']),
    reference_number: z.string().max(100, '参考号过长').optional(),
    notes: z.string().max(500, '备注过长').optional(),
  }),
};

/**
 * Validation middleware for API routes
 */
export function validateRequest<T>(schema: z.ZodSchema<T>) {
  return (data: unknown): T => {
    try {
      return schema.parse(data);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new ValidationError('请求数据验证失败', error);
      }
      throw error;
    }
  };
}

/**
 * Validate query parameters
 */
export function validateQuery<T>(schema: z.ZodSchema<T>, query: Record<string, string | string[]>): T {
  // Convert query parameters to appropriate types
  const processedQuery: Record<string, unknown> = {};
  
  for (const [key, value] of Object.entries(query)) {
    if (Array.isArray(value)) {
      processedQuery[key] = value;
    } else if (value === 'true') {
      processedQuery[key] = true;
    } else if (value === 'false') {
      processedQuery[key] = false;
    } else if (!isNaN(Number(value)) && value !== '') {
      processedQuery[key] = Number(value);
    } else {
      processedQuery[key] = value;
    }
  }
  
  return validateRequest(schema)(processedQuery);
}

/**
 * Sanitize HTML input to prevent XSS
 */
export function sanitizeHtml(input: string): string {
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

/**
 * Validate and sanitize text input
 */
export function sanitizeText(input: string): string {
  return sanitizeHtml(input.trim());
}

/**
 * Type-safe validation result
 */
export type ValidationResult<T> = 
  | { success: true; data: T }
  | { success: false; error: z.ZodError };

/**
 * Safe validation that returns a result instead of throwing
 */
export function safeValidate<T>(schema: z.ZodSchema<T>, data: unknown): ValidationResult<T> {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error };
    }
    throw error;
  }
}
