/**
 * Professional API Route Handler
 * Provides standardized request/response handling with validation, error handling, and logging
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { v4 as uuidv4 } from 'uuid'
import { handleApiError, AppError } from './errors'
import { validateRequest, validateQuery } from './validation'
import { createRequestLogger, createTimer } from './logger'

/**
 * HTTP methods supported by the API handler
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'

/**
 * API route context with request information
 */
export interface ApiContext {
  requestId: string
  method: HttpMethod
  url: string
  userAgent?: string
  ip?: string
  userId?: string
}

/**
 * API route handler configuration
 */
export interface ApiHandlerConfig<TBody = unknown, TQuery = unknown, TParams = unknown> {
  bodySchema?: z.ZodSchema<TBody>
  querySchema?: z.ZodSchema<TQuery>
  paramsSchema?: z.ZodSchema<TParams>
  requireAuth?: boolean
  rateLimit?: {
    requests: number
    windowMs: number
  }
}

/**
 * API route handler function signature
 */
export type ApiHandler<TBody = unknown, TQuery = unknown, TParams = unknown> = (
  request: NextRequest,
  context: ApiContext & {
    body?: TBody
    query?: TQuery
    params?: TParams
  }
) => Promise<NextResponse>

/**
 * Create a professional API route handler with validation, error handling, and logging
 */
export function createApiHandler<TBody = unknown, TQuery = unknown, TParams = unknown>(
  config: ApiHandlerConfig<TBody, TQuery, TParams> = {}
) {
  return (handlers: Partial<Record<HttpMethod, ApiHandler<TBody, TQuery, TParams>>>) => {
    return async (request: NextRequest, routeParams?: { params: Promise<Record<string, string>> }) => {
      const requestId = uuidv4()
      const method = request.method as HttpMethod
      const logger = createRequestLogger(requestId)
      const timer = createTimer(logger, `${method} ${request.url}`)

      try {
        // Create API context
        const context: ApiContext = {
          requestId,
          method,
          url: request.url,
          userAgent: request.headers.get('user-agent') || undefined,
          ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
        }

        logger.info('API request started', {
          method,
          url: request.url,
          userAgent: context.userAgent,
          ip: context.ip,
        })

        // Check if method is supported
        const handler = handlers[method]
        if (!handler) {
          throw new AppError(`Method ${method} not allowed`, 405, 'METHOD_NOT_ALLOWED', true)
        }

        // Validate and parse request body
        let body: TBody | undefined
        if (config.bodySchema && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
          try {
            const rawBody = await request.json()
            body = validateRequest(config.bodySchema)(rawBody)
          } catch (error) {
            if (error instanceof SyntaxError) {
              throw new AppError('Invalid JSON in request body', 400, 'INVALID_JSON', true)
            }
            throw error
          }
        }

        // Validate and parse query parameters
        let query: TQuery | undefined
        if (config.querySchema) {
          const url = new URL(request.url)
          const queryParams: Record<string, string | string[]> = {}
          
          url.searchParams.forEach((value, key) => {
            if (queryParams[key]) {
              if (Array.isArray(queryParams[key])) {
                (queryParams[key] as string[]).push(value)
              } else {
                queryParams[key] = [queryParams[key] as string, value]
              }
            } else {
              queryParams[key] = value
            }
          })
          
          query = validateQuery(config.querySchema, queryParams)
        }

        // Validate and parse route parameters
        let params: TParams | undefined
        if (config.paramsSchema && routeParams) {
          const resolvedParams = await routeParams.params
          params = validateRequest(config.paramsSchema)(resolvedParams)
        }

        // TODO: Implement authentication check
        if (config.requireAuth) {
          // This would integrate with Clerk or your auth system
          // const user = await getAuthenticatedUser(request)
          // context.userId = user.id
        }

        // TODO: Implement rate limiting
        if (config.rateLimit) {
          // This would integrate with Redis or in-memory store
          // await checkRateLimit(context.ip, config.rateLimit)
        }

        // Execute the handler
        const response = await handler(request, {
          ...context,
          body,
          query,
          params,
        })

        // Log successful response
        const status = response.status
        timer.end({ status })

        // Add standard headers
        response.headers.set('X-Request-ID', requestId)
        response.headers.set('X-Content-Type-Options', 'nosniff')
        response.headers.set('X-Frame-Options', 'DENY')
        response.headers.set('X-XSS-Protection', '1; mode=block')

        return response

      } catch (error) {
        // Handle and log errors
        const { status, body } = handleApiError(error)
        timer.error(error as Error, { status })

        const response = NextResponse.json(body, { status })
        response.headers.set('X-Request-ID', requestId)
        
        return response
      }
    }
  }
}

/**
 * Create a standardized success response
 */
export function createSuccessResponse<T>(
  data: T,
  status: number = 200,
  message?: string
): NextResponse {
  return NextResponse.json({
    success: true,
    data,
    message,
    timestamp: new Date().toISOString(),
  }, { status })
}

/**
 * Create a standardized paginated response
 */
export function createPaginatedResponse<T>(
  data: T[],
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  },
  status: number = 200
): NextResponse {
  return NextResponse.json({
    success: true,
    data,
    pagination,
    timestamp: new Date().toISOString(),
  }, { status })
}

/**
 * Utility to extract user ID from request (placeholder)
 */
export async function getUserIdFromRequest(request: NextRequest): Promise<string | null> {
  // TODO: Implement actual user extraction from Clerk or your auth system
  // This is a placeholder implementation
  const authHeader = request.headers.get('authorization')
  if (!authHeader) {
    return null
  }
  
  // Extract user ID from JWT or session
  // This would integrate with your authentication system
  return null
}

/**
 * Middleware to add CORS headers
 */
export function withCors(response: NextResponse): NextResponse {
  response.headers.set('Access-Control-Allow-Origin', '*')
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
  return response
}

/**
 * Handle OPTIONS requests for CORS preflight
 */
export function handleOptions(): NextResponse {
  const response = new NextResponse(null, { status: 200 })
  return withCors(response)
}

/**
 * Custom AppError class for API-specific errors
 */
class ApiError extends AppError {
  constructor(
    message: string,
    public readonly statusCode: number,
    public readonly errorCode: string,
    public readonly isOperational: boolean,
    context?: Record<string, unknown>
  ) {
    super(message, context)
  }
}

/**
 * Common API error creators
 */
export const apiErrors = {
  badRequest: (message: string, context?: Record<string, unknown>) =>
    new ApiError(message, 400, 'BAD_REQUEST', true, context),
  
  unauthorized: (message: string = '未授权访问') =>
    new ApiError(message, 401, 'UNAUTHORIZED', true),
  
  forbidden: (message: string = '禁止访问') =>
    new ApiError(message, 403, 'FORBIDDEN', true),
  
  notFound: (message: string = '资源不存在') =>
    new ApiError(message, 404, 'NOT_FOUND', true),
  
  conflict: (message: string, context?: Record<string, unknown>) =>
    new ApiError(message, 409, 'CONFLICT', true, context),
  
  tooManyRequests: (message: string = '请求过于频繁') =>
    new ApiError(message, 429, 'TOO_MANY_REQUESTS', true),
  
  internalError: (message: string = '服务器内部错误') =>
    new ApiError(message, 500, 'INTERNAL_ERROR', false),
}
