/**
 * Professional Error Handling System
 * Provides structured error handling with proper logging and user-friendly messages
 */

import { z } from 'zod';

/**
 * Base application error class
 * All custom errors should extend this class
 */
export abstract class AppError extends Error {
  abstract readonly statusCode: number;
  abstract readonly errorCode: string;
  abstract readonly isOperational: boolean;
  
  constructor(
    message: string,
    public readonly context?: Record<string, unknown>
  ) {
    super(message);
    this.name = this.constructor.name;
    
    // Maintains proper stack trace for where our error was thrown
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

/**
 * Validation error for invalid input data
 */
export class ValidationError extends AppError {
  readonly statusCode = 400;
  readonly errorCode = 'VALIDATION_ERROR';
  readonly isOperational = true;
  
  constructor(
    message: string,
    public readonly validationErrors?: z.ZodError,
    context?: Record<string, unknown>
  ) {
    super(message, context);
  }
}

/**
 * Authentication error for unauthorized access
 */
export class AuthenticationError extends AppError {
  readonly statusCode = 401;
  readonly errorCode = 'AUTHENTICATION_ERROR';
  readonly isOperational = true;
}

/**
 * Authorization error for forbidden access
 */
export class AuthorizationError extends AppError {
  readonly statusCode = 403;
  readonly errorCode = 'AUTHORIZATION_ERROR';
  readonly isOperational = true;
}

/**
 * Not found error for missing resources
 */
export class NotFoundError extends AppError {
  readonly statusCode = 404;
  readonly errorCode = 'NOT_FOUND_ERROR';
  readonly isOperational = true;
}

/**
 * Conflict error for resource conflicts
 */
export class ConflictError extends AppError {
  readonly statusCode = 409;
  readonly errorCode = 'CONFLICT_ERROR';
  readonly isOperational = true;
}

/**
 * Rate limit error for too many requests
 */
export class RateLimitError extends AppError {
  readonly statusCode = 429;
  readonly errorCode = 'RATE_LIMIT_ERROR';
  readonly isOperational = true;
}

/**
 * Internal server error for unexpected errors
 */
export class InternalServerError extends AppError {
  readonly statusCode = 500;
  readonly errorCode = 'INTERNAL_SERVER_ERROR';
  readonly isOperational = false;
}

/**
 * Database error for database-related issues
 */
export class DatabaseError extends AppError {
  readonly statusCode = 500;
  readonly errorCode = 'DATABASE_ERROR';
  readonly isOperational = false;
}

/**
 * External service error for third-party service failures
 */
export class ExternalServiceError extends AppError {
  readonly statusCode = 502;
  readonly errorCode = 'EXTERNAL_SERVICE_ERROR';
  readonly isOperational = false;
}

/**
 * Error response format for API responses
 */
export interface ErrorResponse {
  error: {
    code: string;
    message: string;
    details?: unknown;
    timestamp: string;
    requestId?: string;
  };
}

/**
 * Create a standardized error response
 */
export function createErrorResponse(
  error: AppError,
  requestId?: string
): ErrorResponse {
  return {
    error: {
      code: error.errorCode,
      message: error.message,
      details: error.context,
      timestamp: new Date().toISOString(),
      requestId,
    },
  };
}

/**
 * Check if an error is an operational error (expected and handled)
 */
export function isOperationalError(error: Error): boolean {
  if (error instanceof AppError) {
    return error.isOperational;
  }
  return false;
}

/**
 * Convert unknown error to AppError
 */
export function normalizeError(error: unknown): AppError {
  if (error instanceof AppError) {
    return error;
  }
  
  if (error instanceof z.ZodError) {
    return new ValidationError('输入数据验证失败', error);
  }
  
  if (error instanceof Error) {
    // Check for specific database errors
    if ('code' in error) {
      const dbError = error as Error & { code: string };
      switch (dbError.code) {
        case '23505': // Unique constraint violation
          return new ConflictError('数据已存在，请检查重复项');
        case '23503': // Foreign key constraint violation
          return new ValidationError('关联数据不存在');
        case '23502': // Not null constraint violation
          return new ValidationError('必填字段不能为空');
        default:
          return new DatabaseError(`数据库操作失败: ${error.message}`);
      }
    }
    
    return new InternalServerError(error.message);
  }
  
  return new InternalServerError('未知错误');
}

/**
 * Error handler for API routes
 */
export function handleApiError(error: unknown): {
  status: number;
  body: ErrorResponse;
} {
  const normalizedError = normalizeError(error);
  
  // Log error for monitoring (in production, this would go to a logging service)
  if (!normalizedError.isOperational) {
    console.error('Unexpected error:', {
      error: normalizedError,
      stack: normalizedError.stack,
      context: normalizedError.context,
    });
  }
  
  return {
    status: normalizedError.statusCode,
    body: createErrorResponse(normalizedError),
  };
}

/**
 * Async error wrapper for API routes
 */
export function asyncHandler<T extends unknown[], R>(
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      throw normalizeError(error);
    }
  };
}

/**
 * Result type for operations that can fail
 */
export type Result<T, E = AppError> = 
  | { success: true; data: T }
  | { success: false; error: E };

/**
 * Create a success result
 */
export function success<T>(data: T): Result<T> {
  return { success: true, data };
}

/**
 * Create an error result
 */
export function failure<E extends AppError>(error: E): Result<never, E> {
  return { success: false, error };
}

/**
 * Wrap a function to return a Result instead of throwing
 */
export async function safeAsync<T>(
  fn: () => Promise<T>
): Promise<Result<T>> {
  try {
    const data = await fn();
    return success(data);
  } catch (error) {
    return failure(normalizeError(error));
  }
}
