// 统一的颜色系统配置
export const statusColors = {
  // 预约状态颜色
  appointment: {
    scheduled: {
      bg: 'bg-blue-100',
      text: 'text-blue-800',
      border: 'border-blue-200',
      hex: '#3b82f6'
    },
    confirmed: {
      bg: 'bg-green-100',
      text: 'text-green-800',
      border: 'border-green-200',
      hex: '#10b981'
    },
    in_progress: {
      bg: 'bg-yellow-100',
      text: 'text-yellow-800',
      border: 'border-yellow-200',
      hex: '#f59e0b'
    },
    completed: {
      bg: 'bg-gray-100',
      text: 'text-gray-800',
      border: 'border-gray-200',
      hex: '#6b7280'
    },
    cancelled: {
      bg: 'bg-red-100',
      text: 'text-red-800',
      border: 'border-red-200',
      hex: '#ef4444'
    },
    no_show: {
      bg: 'bg-red-100',
      text: 'text-red-800',
      border: 'border-red-200',
      hex: '#dc2626'
    },
    consultation: {
      bg: 'bg-purple-100',
      text: 'text-purple-800',
      border: 'border-purple-200',
      hex: '#8b5cf6'
    }
  },
  
  // 客户状态颜色
  client: {
    active: {
      bg: 'bg-green-100',
      text: 'text-green-800',
      border: 'border-green-200'
    },
    inactive: {
      bg: 'bg-yellow-100',
      text: 'text-yellow-800',
      border: 'border-yellow-200'
    },
    archived: {
      bg: 'bg-gray-100',
      text: 'text-gray-800',
      border: 'border-gray-200'
    }
  },
  
  // 账单状态颜色
  invoice: {
    draft: {
      bg: 'bg-gray-100',
      text: 'text-gray-800',
      border: 'border-gray-200'
    },
    sent: {
      bg: 'bg-blue-100',
      text: 'text-blue-800',
      border: 'border-blue-200'
    },
    paid: {
      bg: 'bg-green-100',
      text: 'text-green-800',
      border: 'border-green-200'
    },
    overdue: {
      bg: 'bg-red-100',
      text: 'text-red-800',
      border: 'border-red-200'
    },
    cancelled: {
      bg: 'bg-gray-100',
      text: 'text-gray-800',
      border: 'border-gray-200'
    }
  },
  
  // 付款状态颜色
  payment: {
    pending: {
      bg: 'bg-yellow-100',
      text: 'text-yellow-800',
      border: 'border-yellow-200'
    },
    completed: {
      bg: 'bg-green-100',
      text: 'text-green-800',
      border: 'border-green-200'
    },
    failed: {
      bg: 'bg-red-100',
      text: 'text-red-800',
      border: 'border-red-200'
    },
    refunded: {
      bg: 'bg-purple-100',
      text: 'text-purple-800',
      border: 'border-purple-200'
    }
  }
}

// 获取状态颜色的辅助函数
export function getStatusColor(type: keyof typeof statusColors, status: string) {
  const colorConfig = statusColors[type]
  if (colorConfig && status in colorConfig) {
    return colorConfig[status as keyof typeof colorConfig]
  }
  
  // 默认颜色
  return {
    bg: 'bg-gray-100',
    text: 'text-gray-800',
    border: 'border-gray-200'
  }
}

// 状态文本映射
export const statusText = {
  appointment: {
    scheduled: '已预约',
    confirmed: '已确认',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消',
    no_show: '未到场',
    consultation: '咨询'
  },
  client: {
    active: '活跃',
    inactive: '非活跃',
    archived: '已归档'
  },
  invoice: {
    draft: '草稿',
    sent: '已发送',
    paid: '已支付',
    overdue: '逾期',
    cancelled: '已取消'
  },
  payment: {
    pending: '待处理',
    completed: '已完成',
    failed: '失败',
    refunded: '已退款'
  }
}

// 获取状态文本的辅助函数
export function getStatusText(type: keyof typeof statusText, status: string) {
  const textConfig = statusText[type]
  if (textConfig && status in textConfig) {
    return textConfig[status as keyof typeof textConfig]
  }
  return status
}
