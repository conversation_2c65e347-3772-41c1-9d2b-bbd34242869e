import { z } from 'zod';

/**
 * Environment variable validation schema
 * Ensures type safety and validates all required environment variables at runtime
 */
const envSchema = z.object({
  // Node environment
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  
  // Supabase configuration
  NEXT_PUBLIC_SUPABASE_URL: z.string().url('Invalid Supabase URL'),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1, 'Supabase anon key is required'),
  SUPABASE_SERVICE_ROLE_KEY: z.string().min(1, 'Supabase service role key is required'),
  
  // Clerk authentication
  NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: z.string().min(1, 'Clerk publishable key is required'),
  CLERK_SECRET_KEY: z.string().min(1, 'Clerk secret key is required'),
  
  // Sentry monitoring (optional)
  NEXT_PUBLIC_SENTRY_DSN: z.string().url().optional(),
  NEXT_PUBLIC_SENTRY_ORG: z.string().optional(),
  NEXT_PUBLIC_SENTRY_PROJECT: z.string().optional(),
  NEXT_PUBLIC_SENTRY_DISABLED: z.string().optional(),
  
  // Application configuration
  NEXT_PUBLIC_APP_URL: z.string().url().default('http://localhost:3000'),
  
  // Security
  NEXTAUTH_SECRET: z.string().min(32, 'NextAuth secret must be at least 32 characters').optional(),
  
  // Database encryption key (for sensitive data)
  DATABASE_ENCRYPTION_KEY: z.string().min(32, 'Database encryption key must be at least 32 characters').optional(),
  
  // Rate limiting
  REDIS_URL: z.string().url().optional(),
  
  // Email service (optional)
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.string().transform(Number).optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASSWORD: z.string().optional(),
  
  // File upload
  NEXT_PUBLIC_MAX_FILE_SIZE: z.string().transform(Number).default('5242880'), // 5MB
  
  // Feature flags
  NEXT_PUBLIC_ENABLE_ANALYTICS: z.string().transform(Boolean).default('true'),
  NEXT_PUBLIC_ENABLE_NOTIFICATIONS: z.string().transform(Boolean).default('true'),
});

/**
 * Validated environment variables
 * This ensures type safety throughout the application
 */
export const env = envSchema.parse(process.env);

/**
 * Type-safe environment variables for client-side use
 * Only includes NEXT_PUBLIC_ variables that are safe to expose
 */
export const clientEnv = {
  NODE_ENV: env.NODE_ENV,
  NEXT_PUBLIC_SUPABASE_URL: env.NEXT_PUBLIC_SUPABASE_URL,
  NEXT_PUBLIC_SUPABASE_ANON_KEY: env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY,
  NEXT_PUBLIC_SENTRY_DSN: env.NEXT_PUBLIC_SENTRY_DSN,
  NEXT_PUBLIC_SENTRY_DISABLED: env.NEXT_PUBLIC_SENTRY_DISABLED,
  NEXT_PUBLIC_APP_URL: env.NEXT_PUBLIC_APP_URL,
  NEXT_PUBLIC_MAX_FILE_SIZE: env.NEXT_PUBLIC_MAX_FILE_SIZE,
  NEXT_PUBLIC_ENABLE_ANALYTICS: env.NEXT_PUBLIC_ENABLE_ANALYTICS,
  NEXT_PUBLIC_ENABLE_NOTIFICATIONS: env.NEXT_PUBLIC_ENABLE_NOTIFICATIONS,
} as const;

/**
 * Server-only environment variables
 * These should never be exposed to the client
 */
export const serverEnv = {
  SUPABASE_SERVICE_ROLE_KEY: env.SUPABASE_SERVICE_ROLE_KEY,
  CLERK_SECRET_KEY: env.CLERK_SECRET_KEY,
  NEXTAUTH_SECRET: env.NEXTAUTH_SECRET,
  DATABASE_ENCRYPTION_KEY: env.DATABASE_ENCRYPTION_KEY,
  REDIS_URL: env.REDIS_URL,
  SMTP_HOST: env.SMTP_HOST,
  SMTP_PORT: env.SMTP_PORT,
  SMTP_USER: env.SMTP_USER,
  SMTP_PASSWORD: env.SMTP_PASSWORD,
} as const;

/**
 * Utility function to check if we're in development mode
 */
export const isDevelopment = env.NODE_ENV === 'development';

/**
 * Utility function to check if we're in production mode
 */
export const isProduction = env.NODE_ENV === 'production';

/**
 * Utility function to check if we're in test mode
 */
export const isTest = env.NODE_ENV === 'test';

/**
 * Validate environment variables on module load
 * This will throw an error if any required variables are missing or invalid
 */
try {
  envSchema.parse(process.env);
} catch (error) {
  if (error instanceof z.ZodError) {
    const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
    throw new Error(`❌ Invalid environment variables:\n${missingVars.join('\n')}`);
  }
  throw error;
}

export type Env = z.infer<typeof envSchema>;
