// Google Maps API 集成
// 需要在环境变量中设置 NEXT_PUBLIC_GOOGLE_MAPS_API_KEY

export interface GoogleMapsPlace {
  place_id: string
  formatted_address: string
  address_components: {
    long_name: string
    short_name: string
    types: string[]
  }[]
  geometry: {
    location: {
      lat: number
      lng: number
    }
  }
}

export interface ParsedAddress {
  address_line_1: string
  address_line_2: string
  city: string
  state_province: string
  postal_code: string
  country: string
  latitude: number
  longitude: number
}

// 加载Google Maps JavaScript API
export function loadGoogleMapsAPI(): Promise<any> {
  return new Promise((resolve, reject) => {
    if (typeof window === 'undefined') {
      reject(new Error('Google Maps API can only be loaded in browser'))
      return
    }

    // 检查是否已经加载
    if (window.google && window.google.maps) {
      resolve(window.google)
      return
    }

    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
    if (!apiKey) {
      reject(new Error('Google Maps API key not found'))
      return
    }

    const script = document.createElement('script')
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&language=zh-CN`
    script.async = true
    script.defer = true

    script.onload = () => {
      if (window.google && window.google.maps) {
        resolve(window.google)
      } else {
        reject(new Error('Google Maps API failed to load'))
      }
    }

    script.onerror = () => {
      reject(new Error('Failed to load Google Maps API script'))
    }

    document.head.appendChild(script)
  })
}

// 解析Google Places API返回的地址组件
export function parseGoogleAddress(place: GoogleMapsPlace): ParsedAddress {
  const components = place.address_components
  
  const getComponent = (types: string[]) => {
    const component = components.find(comp => 
      types.some(type => comp.types.includes(type))
    )
    return component?.long_name || ''
  }

  const getShortComponent = (types: string[]) => {
    const component = components.find(comp => 
      types.some(type => comp.types.includes(type))
    )
    return component?.short_name || ''
  }

  // 提取地址组件
  const streetNumber = getComponent(['street_number'])
  const streetName = getComponent(['route'])
  const subpremise = getComponent(['subpremise'])
  const city = getComponent(['locality', 'administrative_area_level_2'])
  const state = getShortComponent(['administrative_area_level_1'])
  const postalCode = getComponent(['postal_code'])
  const country = getComponent(['country'])

  // 构建地址第一行
  let addressLine1 = ''
  if (streetNumber && streetName) {
    addressLine1 = `${streetNumber} ${streetName}`
  } else if (streetName) {
    addressLine1 = streetName
  }

  return {
    address_line_1: addressLine1,
    address_line_2: subpremise,
    city: city,
    state_province: state,
    postal_code: postalCode,
    country: country,
    latitude: place.geometry.location.lat,
    longitude: place.geometry.location.lng
  }
}

// 地址自动完成搜索
export async function searchAddresses(query: string): Promise<GoogleMapsPlace[]> {
  try {
    const google = await loadGoogleMapsAPI()
    
    return new Promise((resolve, reject) => {
      const service = new google.maps.places.AutocompleteService()
      
      service.getPlacePredictions(
        {
          input: query,
          componentRestrictions: { country: 'us' }, // 限制为美国
          types: ['address']
        },
        (predictions: any, status: any) => {
          if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
            // 获取详细地址信息
            const placesService = new google.maps.places.PlacesService(
              document.createElement('div')
            )
            
            const detailPromises = predictions.slice(0, 5).map((prediction: any) =>
              new Promise<GoogleMapsPlace>((resolveDetail, rejectDetail) => {
                placesService.getDetails(
                  {
                    placeId: prediction.place_id,
                    fields: ['place_id', 'formatted_address', 'address_components', 'geometry']
                  },
                  (place: any, detailStatus: any) => {
                    if (detailStatus === google.maps.places.PlacesServiceStatus.OK && place) {
                      resolveDetail(place as GoogleMapsPlace)
                    } else {
                      rejectDetail(new Error('Failed to get place details'))
                    }
                  }
                )
              })
            )
            
            Promise.all(detailPromises)
              .then(resolve)
              .catch(reject)
          } else {
            resolve([])
          }
        }
      )
    })
  } catch (error) {
    console.error('Address search error:', error)
    return []
  }
}

// 地理编码：将地址转换为坐标
export async function geocodeAddress(address: string): Promise<{ lat: number; lng: number } | null> {
  try {
    const google = await loadGoogleMapsAPI()
    
    return new Promise((resolve, reject) => {
      const geocoder = new google.maps.Geocoder()
      
      geocoder.geocode(
        { 
          address: address,
          componentRestrictions: { country: 'US' }
        },
        (results: any, status: any) => {
          if (status === google.maps.GeocoderStatus.OK && results && results[0]) {
            const location = results[0].geometry.location
            resolve({
              lat: location.lat(),
              lng: location.lng()
            })
          } else {
            resolve(null)
          }
        }
      )
    })
  } catch (error) {
    console.error('Geocoding error:', error)
    return null
  }
}

// 类型声明
declare global {
  interface Window {
    google: any
  }

  const google: any
}
