/**
 * Role-Based Access Control (RBAC) System
 * Advanced authentication and authorization with HIPAA compliance
 */

import { logger } from '../logger'
import { monitor } from '../monitoring'
import { AuditLogger } from '../security'
import { HIPAARole, AccessPurpose } from '../compliance/hipaa'

/**
 * System permissions
 */
export enum Permission {
  // Client management
  CLIENT_READ = 'client:read',
  CLIENT_CREATE = 'client:create',
  CLIENT_UPDATE = 'client:update',
  CLIENT_DELETE = 'client:delete',
  CLIENT_EXPORT = 'client:export',
  
  // Treatment management
  TREATMENT_READ = 'treatment:read',
  TREATMENT_CREATE = 'treatment:create',
  TREATMENT_UPDATE = 'treatment:update',
  TREATMENT_DELETE = 'treatment:delete',
  
  // Appointment management
  APPOINTMENT_READ = 'appointment:read',
  APPOINTMENT_CREATE = 'appointment:create',
  APPOINTMENT_UPDATE = 'appointment:update',
  APPOINTMENT_DELETE = 'appointment:delete',
  APPOINTMENT_RESCHEDULE = 'appointment:reschedule',
  
  // Financial management
  INVOICE_READ = 'invoice:read',
  INVOICE_CREATE = 'invoice:create',
  INVOICE_UPDATE = 'invoice:update',
  INVOICE_DELETE = 'invoice:delete',
  PAYMENT_READ = 'payment:read',
  PAYMENT_CREATE = 'payment:create',
  PAYMENT_UPDATE = 'payment:update',
  PAYMENT_DELETE = 'payment:delete',
  
  // Reporting and analytics
  REPORT_VIEW = 'report:view',
  REPORT_EXPORT = 'report:export',
  ANALYTICS_VIEW = 'analytics:view',
  
  // System administration
  USER_READ = 'user:read',
  USER_CREATE = 'user:create',
  USER_UPDATE = 'user:update',
  USER_DELETE = 'user:delete',
  ROLE_MANAGE = 'role:manage',
  SYSTEM_CONFIG = 'system:config',
  
  // Audit and compliance
  AUDIT_READ = 'audit:read',
  AUDIT_EXPORT = 'audit:export',
  COMPLIANCE_VIEW = 'compliance:view',
  SECURITY_MANAGE = 'security:manage',
  
  // PHI specific permissions
  PHI_READ = 'phi:read',
  PHI_EXPORT = 'phi:export',
  PHI_DELETE = 'phi:delete',
}

/**
 * System roles with permissions
 */
export interface Role {
  id: string
  name: string
  description: string
  hipaaRole: HIPAARole
  permissions: Permission[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

/**
 * User with roles and permissions
 */
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  roles: string[] // Role IDs
  isActive: boolean
  lastLogin?: Date
  mfaEnabled: boolean
  hipaaTrainingCompleted: boolean
  hipaaTrainingDate?: Date
  createdAt: Date
  updatedAt: Date
}

/**
 * Session information
 */
export interface Session {
  id: string
  userId: string
  deviceId: string
  ipAddress: string
  userAgent: string
  createdAt: Date
  expiresAt: Date
  lastActivity: Date
  isActive: boolean
}

/**
 * RBAC Manager
 */
export class RBACManager {
  private roles = new Map<string, Role>()
  private users = new Map<string, User>()
  private sessions = new Map<string, Session>()

  constructor() {
    this.initializeDefaultRoles()
  }

  /**
   * Initialize default system roles
   */
  private initializeDefaultRoles(): void {
    const defaultRoles: Omit<Role, 'createdAt' | 'updatedAt'>[] = [
      {
        id: 'admin',
        name: '系统管理员',
        description: '完全系统访问权限',
        hipaaRole: HIPAARole.COVERED_ENTITY_ADMIN,
        permissions: Object.values(Permission),
        isActive: true,
      },
      {
        id: 'privacy_officer',
        name: '隐私官',
        description: 'HIPAA隐私合规管理',
        hipaaRole: HIPAARole.PRIVACY_OFFICER,
        permissions: [
          Permission.CLIENT_READ,
          Permission.AUDIT_READ,
          Permission.AUDIT_EXPORT,
          Permission.COMPLIANCE_VIEW,
          Permission.PHI_READ,
          Permission.PHI_EXPORT,
          Permission.REPORT_VIEW,
          Permission.REPORT_EXPORT,
        ],
        isActive: true,
      },
      {
        id: 'security_officer',
        name: '安全官',
        description: '系统安全管理',
        hipaaRole: HIPAARole.SECURITY_OFFICER,
        permissions: [
          Permission.USER_READ,
          Permission.ROLE_MANAGE,
          Permission.SYSTEM_CONFIG,
          Permission.AUDIT_READ,
          Permission.SECURITY_MANAGE,
          Permission.COMPLIANCE_VIEW,
        ],
        isActive: true,
      },
      {
        id: 'healthcare_provider',
        name: '医疗服务提供者',
        description: '医疗服务和治疗管理',
        hipaaRole: HIPAARole.HEALTHCARE_PROVIDER,
        permissions: [
          Permission.CLIENT_READ,
          Permission.CLIENT_CREATE,
          Permission.CLIENT_UPDATE,
          Permission.TREATMENT_READ,
          Permission.TREATMENT_CREATE,
          Permission.TREATMENT_UPDATE,
          Permission.APPOINTMENT_READ,
          Permission.APPOINTMENT_CREATE,
          Permission.APPOINTMENT_UPDATE,
          Permission.APPOINTMENT_RESCHEDULE,
          Permission.PHI_READ,
        ],
        isActive: true,
      },
      {
        id: 'front_desk',
        name: '前台接待',
        description: '客户接待和预约管理',
        hipaaRole: HIPAARole.ADMINISTRATIVE_STAFF,
        permissions: [
          Permission.CLIENT_READ,
          Permission.CLIENT_CREATE,
          Permission.CLIENT_UPDATE,
          Permission.APPOINTMENT_READ,
          Permission.APPOINTMENT_CREATE,
          Permission.APPOINTMENT_UPDATE,
          Permission.APPOINTMENT_RESCHEDULE,
          Permission.INVOICE_READ,
          Permission.INVOICE_CREATE,
          Permission.PAYMENT_READ,
          Permission.PAYMENT_CREATE,
        ],
        isActive: true,
      },
      {
        id: 'billing_staff',
        name: '财务人员',
        description: '财务和账单管理',
        hipaaRole: HIPAARole.ADMINISTRATIVE_STAFF,
        permissions: [
          Permission.CLIENT_READ,
          Permission.INVOICE_READ,
          Permission.INVOICE_CREATE,
          Permission.INVOICE_UPDATE,
          Permission.PAYMENT_READ,
          Permission.PAYMENT_CREATE,
          Permission.PAYMENT_UPDATE,
          Permission.REPORT_VIEW,
        ],
        isActive: true,
      },
      {
        id: 'auditor',
        name: '审计员',
        description: '系统审计和合规检查',
        hipaaRole: HIPAARole.AUDIT_REVIEWER,
        permissions: [
          Permission.AUDIT_READ,
          Permission.AUDIT_EXPORT,
          Permission.COMPLIANCE_VIEW,
          Permission.REPORT_VIEW,
          Permission.REPORT_EXPORT,
        ],
        isActive: true,
      },
    ]

    for (const roleData of defaultRoles) {
      const role: Role = {
        ...roleData,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      this.roles.set(role.id, role)
    }

    logger.info('Default RBAC roles initialized', {
      rolesCount: this.roles.size,
      roles: Array.from(this.roles.keys()),
    })
  }

  /**
   * Create a new role
   */
  async createRole(roleData: Omit<Role, 'id' | 'createdAt' | 'updatedAt'>): Promise<Role> {
    const id = `role_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const role: Role = {
      ...roleData,
      id,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    this.roles.set(id, role)

    await AuditLogger.logOperation(
      'role_created',
      'role',
      id,
      undefined,
      { roleName: role.name, permissions: role.permissions }
    )

    monitor.counter('rbac.role_created', 1, { roleName: role.name })
    return role
  }

  /**
   * Get role by ID
   */
  getRole(roleId: string): Role | undefined {
    return this.roles.get(roleId)
  }

  /**
   * Get all roles
   */
  getAllRoles(): Role[] {
    return Array.from(this.roles.values()).filter(role => role.isActive)
  }

  /**
   * Update role
   */
  async updateRole(roleId: string, updates: Partial<Role>): Promise<Role | null> {
    const role = this.roles.get(roleId)
    if (!role) {
      return null
    }

    const updatedRole: Role = {
      ...role,
      ...updates,
      id: roleId, // Prevent ID changes
      updatedAt: new Date(),
    }

    this.roles.set(roleId, updatedRole)

    await AuditLogger.logOperation(
      'role_updated',
      'role',
      roleId,
      undefined,
      { updates, previousRole: role }
    )

    monitor.counter('rbac.role_updated', 1, { roleId })
    return updatedRole
  }

  /**
   * Check if user has permission
   */
  async hasPermission(userId: string, permission: Permission): Promise<boolean> {
    const user = this.users.get(userId)
    if (!user || !user.isActive) {
      return false
    }

    // Get all permissions from user's roles
    const userPermissions = new Set<Permission>()
    for (const roleId of user.roles) {
      const role = this.roles.get(roleId)
      if (role && role.isActive) {
        role.permissions.forEach(p => userPermissions.add(p))
      }
    }

    const hasPermission = userPermissions.has(permission)

    // Log permission check for audit
    await AuditLogger.logOperation(
      'permission_check',
      'permission',
      permission,
      userId,
      { hasPermission, requestedPermission: permission }
    )

    monitor.counter('rbac.permission_check', 1, {
      userId,
      permission,
      result: hasPermission.toString(),
    })

    return hasPermission
  }

  /**
   * Check multiple permissions
   */
  async hasPermissions(userId: string, permissions: Permission[]): Promise<{
    hasAll: boolean
    hasAny: boolean
    allowed: Permission[]
    denied: Permission[]
  }> {
    const results = await Promise.all(
      permissions.map(async (permission) => ({
        permission,
        allowed: await this.hasPermission(userId, permission),
      }))
    )

    const allowed = results.filter(r => r.allowed).map(r => r.permission)
    const denied = results.filter(r => !r.allowed).map(r => r.permission)

    return {
      hasAll: denied.length === 0,
      hasAny: allowed.length > 0,
      allowed,
      denied,
    }
  }

  /**
   * Get user permissions
   */
  getUserPermissions(userId: string): Permission[] {
    const user = this.users.get(userId)
    if (!user || !user.isActive) {
      return []
    }

    const permissions = new Set<Permission>()
    for (const roleId of user.roles) {
      const role = this.roles.get(roleId)
      if (role && role.isActive) {
        role.permissions.forEach(p => permissions.add(p))
      }
    }

    return Array.from(permissions)
  }

  /**
   * Get user roles
   */
  getUserRoles(userId: string): Role[] {
    const user = this.users.get(userId)
    if (!user || !user.isActive) {
      return []
    }

    return user.roles
      .map(roleId => this.roles.get(roleId))
      .filter((role): role is Role => role !== undefined && role.isActive)
  }

  /**
   * Assign role to user
   */
  async assignRole(userId: string, roleId: string): Promise<boolean> {
    const user = this.users.get(userId)
    const role = this.roles.get(roleId)

    if (!user || !role || !user.isActive || !role.isActive) {
      return false
    }

    if (!user.roles.includes(roleId)) {
      user.roles.push(roleId)
      user.updatedAt = new Date()

      await AuditLogger.logOperation(
        'role_assigned',
        'user',
        userId,
        undefined,
        { roleId, roleName: role.name }
      )

      monitor.counter('rbac.role_assigned', 1, { userId, roleId })
    }

    return true
  }

  /**
   * Remove role from user
   */
  async removeRole(userId: string, roleId: string): Promise<boolean> {
    const user = this.users.get(userId)
    if (!user) {
      return false
    }

    const roleIndex = user.roles.indexOf(roleId)
    if (roleIndex > -1) {
      user.roles.splice(roleIndex, 1)
      user.updatedAt = new Date()

      await AuditLogger.logOperation(
        'role_removed',
        'user',
        userId,
        undefined,
        { roleId }
      )

      monitor.counter('rbac.role_removed', 1, { userId, roleId })
    }

    return true
  }

  /**
   * Create session
   */
  async createSession(
    userId: string,
    deviceId: string,
    ipAddress: string,
    userAgent: string
  ): Promise<Session> {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const session: Session = {
      id: sessionId,
      userId,
      deviceId,
      ipAddress,
      userAgent,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      lastActivity: new Date(),
      isActive: true,
    }

    this.sessions.set(sessionId, session)

    await AuditLogger.logAuth('login', userId, {
      sessionId,
      deviceId,
      ipAddress,
      userAgent,
    })

    monitor.counter('rbac.session_created', 1, { userId })
    return session
  }

  /**
   * Validate session
   */
  async validateSession(sessionId: string): Promise<Session | null> {
    const session = this.sessions.get(sessionId)
    
    if (!session || !session.isActive || session.expiresAt < new Date()) {
      if (session) {
        session.isActive = false
        monitor.counter('rbac.session_expired', 1, { userId: session.userId })
      }
      return null
    }

    // Update last activity
    session.lastActivity = new Date()
    monitor.counter('rbac.session_validated', 1, { userId: session.userId })
    
    return session
  }

  /**
   * Terminate session
   */
  async terminateSession(sessionId: string): Promise<boolean> {
    const session = this.sessions.get(sessionId)
    if (!session) {
      return false
    }

    session.isActive = false

    await AuditLogger.logAuth('logout', session.userId, {
      sessionId,
      terminatedAt: new Date().toISOString(),
    })

    monitor.counter('rbac.session_terminated', 1, { userId: session.userId })
    return true
  }

  /**
   * Get active sessions for user
   */
  getUserSessions(userId: string): Session[] {
    return Array.from(this.sessions.values())
      .filter(session => session.userId === userId && session.isActive)
  }

  /**
   * Terminate all user sessions
   */
  async terminateAllUserSessions(userId: string): Promise<number> {
    const userSessions = this.getUserSessions(userId)
    
    for (const session of userSessions) {
      await this.terminateSession(session.id)
    }

    monitor.counter('rbac.all_sessions_terminated', 1, { 
      userId, 
      sessionCount: userSessions.length.toString() 
    })

    return userSessions.length
  }
}

/**
 * Global RBAC manager instance
 */
export const rbacManager = new RBACManager()

/**
 * RBAC utilities
 */
export const rbacUtils = {
  /**
   * Check if user has HIPAA training
   */
  hasHIPAATraining: (user: User): boolean => {
    if (!user.hipaaTrainingCompleted || !user.hipaaTrainingDate) {
      return false
    }
    
    // Check if training is within last year
    const oneYearAgo = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)
    return user.hipaaTrainingDate > oneYearAgo
  },

  /**
   * Get required permissions for an operation
   */
  getRequiredPermissions: (operation: string): Permission[] => {
    const operationPermissions: Record<string, Permission[]> = {
      'view_clients': [Permission.CLIENT_READ],
      'create_client': [Permission.CLIENT_CREATE],
      'update_client': [Permission.CLIENT_UPDATE],
      'delete_client': [Permission.CLIENT_DELETE],
      'export_clients': [Permission.CLIENT_EXPORT, Permission.PHI_EXPORT],
      'view_appointments': [Permission.APPOINTMENT_READ],
      'create_appointment': [Permission.APPOINTMENT_CREATE],
      'view_reports': [Permission.REPORT_VIEW],
      'manage_users': [Permission.USER_READ, Permission.USER_CREATE, Permission.USER_UPDATE],
    }
    
    return operationPermissions[operation] || []
  },

  /**
   * Format role for display
   */
  formatRole: (role: Role): string => {
    return `${role.name} (${role.permissions.length} permissions)`
  },
}
