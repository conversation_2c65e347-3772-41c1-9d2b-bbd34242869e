import React from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';

export default function PageContainer({
  children,
  scrollable = true
}: {
  children: React.ReactNode;
  scrollable?: boolean;
}) {
  return (
    <>
      {scrollable ? (
        <ScrollArea className='h-[calc(100vh-4rem)] md:h-[calc(100dvh-4rem)]'>
          <div className='flex flex-1 p-4 sm:p-6 lg:p-8 min-h-full bg-background/50'>{children}</div>
        </ScrollArea>
      ) : (
        <div className='flex flex-1 p-4 sm:p-6 lg:p-8 min-h-[calc(100vh-4rem)] md:min-h-[calc(100dvh-4rem)] bg-background/50'>{children}</div>
      )}
    </>
  );
}
