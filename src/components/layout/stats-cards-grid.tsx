import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';

interface StatsCard {
  title: string;
  value: string | number;
  description?: string;
  icon: LucideIcon;
  trend?: {
    value: string;
    isPositive?: boolean;
  };
  className?: string;
}

interface StatsCardsGridProps {
  cards: StatsCard[];
  loading?: boolean;
  columns?: 2 | 3 | 4;
  className?: string;
}

export function StatsCardSkeleton({ columns = 4 }: { columns?: 2 | 3 | 4 }) {
  const gridCols = {
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  };

  return (
    <div className={`grid gap-4 ${gridCols[columns]}`}>
      {Array.from({ length: columns }).map((_, i) => (
        <Card key={i}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-4" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-8 w-16 mb-2" />
            <Skeleton className="h-3 w-32" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export default function StatsCardsGrid({
  cards,
  loading = false,
  columns = 4,
  className = ''
}: StatsCardsGridProps) {
  const gridCols = {
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  };

  if (loading) {
    return <StatsCardSkeleton columns={columns} />;
  }

  return (
    <div className={`grid gap-4 ${gridCols[columns]} ${className}`}>
      {cards.map((card, index) => (
        <Card key={index} className={`card-hover ${card.className || ''}`}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
            <card.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{card.value}</div>
            {(card.description || card.trend) && (
              <p className="text-xs text-muted-foreground">
                {card.description}
                {card.trend && (
                  <span className={card.trend.isPositive ? 'text-green-600' : 'text-red-600'}>
                    {card.trend.value}
                  </span>
                )}
              </p>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
