'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  Calendar,
  Download,
  FileText,
  CreditCard,
  Users,
  Package
} from 'lucide-react'
import { format, subDays, startOfMonth, endOfMonth, subMonths } from 'date-fns'
import { zhCN } from 'date-fns/locale'

interface FinancialSummary {
  total_revenue: number
  total_deposits: number
  outstanding_balance: number
  completed_treatments: number
  pending_payments: number
  revenue_growth: number
  period_comparison: {
    current_period: number
    previous_period: number
    growth_percentage: number
  }
}

interface RevenueData {
  date: string
  revenue: number
  deposits: number
  treatments: number
}

interface TreatmentRevenueData {
  treatment_name: string
  revenue: number
  count: number
  percentage: number
}

interface PaymentMethodData {
  method: string
  amount: number
  percentage: number
  color: string
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

export default function FinancialReports() {
  const [summary, setSummary] = useState<FinancialSummary | null>(null)
  const [revenueData, setRevenueData] = useState<RevenueData[]>([])
  const [treatmentData, setTreatmentData] = useState<TreatmentRevenueData[]>([])
  const [paymentMethodData, setPaymentMethodData] = useState<PaymentMethodData[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('30days')
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString())

  const periodOptions = [
    { value: '7days', label: '最近7天' },
    { value: '30days', label: '最近30天' },
    { value: 'current_month', label: '本月' },
    { value: 'last_month', label: '上月' },
    { value: 'current_year', label: '今年' }
  ]

  const fetchFinancialData = async () => {
    try {
      setLoading(true)
      
      // Here you would fetch real data from your API
      // const response = await fetch(`/api/reports/financial?period=${selectedPeriod}&year=${selectedYear}`)
      // const data = await response.json()
      
      // Mock data for demonstration
      const mockSummary: FinancialSummary = {
        total_revenue: 125000,
        total_deposits: 35000,
        outstanding_balance: 15000,
        completed_treatments: 156,
        pending_payments: 8,
        revenue_growth: 12.5,
        period_comparison: {
          current_period: 125000,
          previous_period: 110000,
          growth_percentage: 13.6
        }
      }

      const mockRevenueData: RevenueData[] = Array.from({ length: 30 }, (_, i) => ({
        date: format(subDays(new Date(), 29 - i), 'MM/dd'),
        revenue: Math.floor(Math.random() * 5000) + 1000,
        deposits: Math.floor(Math.random() * 2000) + 500,
        treatments: Math.floor(Math.random() * 10) + 2
      }))

      const mockTreatmentData: TreatmentRevenueData[] = [
        { treatment_name: '深层清洁面膜', revenue: 25000, count: 125, percentage: 20 },
        { treatment_name: '补水保湿护理', revenue: 30000, count: 100, percentage: 24 },
        { treatment_name: '抗衰老精华', revenue: 40000, count: 80, percentage: 32 },
        { treatment_name: '激光美白', revenue: 20000, count: 40, percentage: 16 },
        { treatment_name: '微针治疗', revenue: 10000, count: 20, percentage: 8 }
      ]

      const mockPaymentMethodData: PaymentMethodData[] = [
        { method: '现金', amount: 45000, percentage: 36, color: '#0088FE' },
        { method: '银行卡', amount: 50000, percentage: 40, color: '#00C49F' },
        { method: '支付宝', amount: 20000, percentage: 16, color: '#FFBB28' },
        { method: '微信支付', amount: 10000, percentage: 8, color: '#FF8042' }
      ]

      setSummary(mockSummary)
      setRevenueData(mockRevenueData)
      setTreatmentData(mockTreatmentData)
      setPaymentMethodData(mockPaymentMethodData)
    } catch (error) {
      console.error('Error fetching financial data:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchFinancialData()
  }, [selectedPeriod, selectedYear])

  const exportReport = (format: 'pdf' | 'excel') => {
    // Here you would implement report export functionality
    console.log(`Exporting report as ${format}`)
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold">财务报表</h2>
          <p className="text-muted-foreground">收入分析和财务概览</p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {periodOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={() => exportReport('excel')}>
            <Download className="mr-2 h-4 w-4" />
            导出Excel
          </Button>
          <Button variant="outline" onClick={() => exportReport('pdf')}>
            <FileText className="mr-2 h-4 w-4" />
            导出PDF
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">总收入</p>
                  <p className="text-2xl font-bold">¥{summary.total_revenue.toLocaleString()}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-3 w-3 text-green-600" />
                    <span className="text-xs text-green-600">+{summary.revenue_growth}%</span>
                  </div>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">定金收入</p>
                  <p className="text-2xl font-bold">¥{summary.total_deposits.toLocaleString()}</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    占总收入 {Math.round((summary.total_deposits / summary.total_revenue) * 100)}%
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <CreditCard className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">完成治疗</p>
                  <p className="text-2xl font-bold">{summary.completed_treatments}</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    平均单价 ¥{Math.round(summary.total_revenue / summary.completed_treatments)}
                  </p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <Package className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">待收款</p>
                  <p className="text-2xl font-bold">¥{summary.outstanding_balance.toLocaleString()}</p>
                  <Badge variant="outline" className="mt-1">
                    {summary.pending_payments} 笔待付
                  </Badge>
                </div>
                <div className="p-3 bg-orange-100 rounded-full">
                  <Calendar className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Charts */}
      <Tabs defaultValue="revenue" className="space-y-4">
        <TabsList>
          <TabsTrigger value="revenue">收入趋势</TabsTrigger>
          <TabsTrigger value="treatments">治疗项目分析</TabsTrigger>
          <TabsTrigger value="payments">支付方式分析</TabsTrigger>
        </TabsList>

        <TabsContent value="revenue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>收入趋势分析</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={revenueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip 
                    formatter={(value, name) => [
                      `¥${Number(value).toLocaleString()}`, 
                      name === 'revenue' ? '收入' : name === 'deposits' ? '定金' : '治疗次数'
                    ]}
                  />
                  <Line type="monotone" dataKey="revenue" stroke="#8884d8" strokeWidth={2} name="revenue" />
                  <Line type="monotone" dataKey="deposits" stroke="#82ca9d" strokeWidth={2} name="deposits" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="treatments" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>治疗项目收入排行</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={treatmentData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="treatment_name" angle={-45} textAnchor="end" height={80} />
                    <YAxis />
                    <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '收入']} />
                    <Bar dataKey="revenue" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>治疗项目详情</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {treatmentData.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{item.treatment_name}</p>
                        <p className="text-sm text-muted-foreground">{item.count} 次治疗</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">¥{item.revenue.toLocaleString()}</p>
                        <p className="text-sm text-muted-foreground">{item.percentage}%</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="payments" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>支付方式分布</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={paymentMethodData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percentage }) => `${name} ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="amount"
                    >
                      {paymentMethodData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`¥${Number(value).toLocaleString()}`, '金额']} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>支付方式统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {paymentMethodData.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div 
                          className="w-4 h-4 rounded-full" 
                          style={{ backgroundColor: item.color }}
                        />
                        <span className="font-medium">{item.method}</span>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">¥{item.amount.toLocaleString()}</p>
                        <p className="text-sm text-muted-foreground">{item.percentage}%</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
