/**
 * Security Monitoring Dashboard
 * Real-time security monitoring, threat detection, and compliance status
 */

'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Eye,
  Lock,
  Users,
  Activity,
  FileText,
  TrendingUp,
  RefreshCw,
  Bell,
  BellOff,
  Download
} from 'lucide-react'

/**
 * Security metrics interface
 */
interface SecurityMetrics {
  threats: {
    total: number
    critical: number
    high: number
    medium: number
    low: number
    blocked: number
  }
  compliance: {
    hipaa: {
      status: 'compliant' | 'non_compliant' | 'partially_compliant'
      score: number
      lastChecked: string
    }
    gdpr: {
      status: 'compliant' | 'non_compliant' | 'partially_compliant'
      score: number
      lastChecked: string
    }
  }
  access: {
    activeUsers: number
    failedLogins: number
    suspiciousActivity: number
    blockedIPs: number
  }
  audit: {
    totalLogs: number
    phiAccess: number
    dataExports: number
    configChanges: number
  }
}

/**
 * Security alert interface
 */
interface SecurityAlert {
  id: string
  title: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  timestamp: string
  acknowledged: boolean
  resolved: boolean
}

/**
 * Security Dashboard Component
 */
export const SecurityDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<SecurityMetrics | null>(null)
  const [alerts, setAlerts] = useState<SecurityAlert[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())
  const [alertsEnabled, setAlertsEnabled] = useState(true)

  // Fetch security metrics
  const fetchSecurityMetrics = async () => {
    try {
      setIsLoading(true)
      
      // In a real implementation, this would fetch from your security API
      const response = await fetch('/api/admin/security/metrics')
      const data = await response.json()
      
      setMetrics(data.metrics)
      setAlerts(data.alerts)
      setLastUpdated(new Date())
    } catch (error) {
      console.error('Failed to fetch security metrics:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Auto-refresh metrics
  useEffect(() => {
    fetchSecurityMetrics()
    const interval = setInterval(fetchSecurityMetrics, 30000) // Refresh every 30 seconds
    return () => clearInterval(interval)
  }, [])

  // Mock data for demonstration (remove in production)
  useEffect(() => {
    if (!metrics) {
      setMetrics({
        threats: {
          total: 23,
          critical: 1,
          high: 3,
          medium: 8,
          low: 11,
          blocked: 5,
        },
        compliance: {
          hipaa: {
            status: 'compliant',
            score: 94,
            lastChecked: new Date().toISOString(),
          },
          gdpr: {
            status: 'partially_compliant',
            score: 78,
            lastChecked: new Date().toISOString(),
          },
        },
        access: {
          activeUsers: 12,
          failedLogins: 3,
          suspiciousActivity: 1,
          blockedIPs: 2,
        },
        audit: {
          totalLogs: 1247,
          phiAccess: 89,
          dataExports: 5,
          configChanges: 12,
        },
      })

      setAlerts([
        {
          id: '1',
          title: '可疑登录尝试',
          description: '检测到来自异常IP地址的多次登录失败',
          severity: 'high',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          acknowledged: false,
          resolved: false,
        },
        {
          id: '2',
          title: 'PHI批量访问',
          description: '用户在短时间内访问了大量患者数据',
          severity: 'medium',
          timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
          acknowledged: true,
          resolved: false,
        },
      ])
      setIsLoading(false)
    }
  }, [metrics])

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-50'
      case 'high': return 'text-orange-600 bg-orange-50'
      case 'medium': return 'text-yellow-600 bg-yellow-50'
      case 'low': return 'text-blue-600 bg-blue-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getComplianceColor = (status: string) => {
    switch (status) {
      case 'compliant': return 'text-green-600'
      case 'partially_compliant': return 'text-yellow-600'
      case 'non_compliant': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getComplianceIcon = (status: string) => {
    switch (status) {
      case 'compliant': return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'partially_compliant': return <AlertTriangle className="h-5 w-5 text-yellow-600" />
      case 'non_compliant': return <XCircle className="h-5 w-5 text-red-600" />
      default: return <Activity className="h-5 w-5 text-gray-600" />
    }
  }

  const acknowledgeAlert = async (alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ))
    
    // In real implementation, call API to acknowledge alert
    try {
      await fetch(`/api/admin/security/alerts/${alertId}/acknowledge`, {
        method: 'POST',
      })
    } catch (error) {
      console.error('Failed to acknowledge alert:', error)
    }
  }

  const exportSecurityReport = async () => {
    try {
      const response = await fetch('/api/admin/security/export', {
        method: 'POST',
      })
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `security-report-${new Date().toISOString().split('T')[0]}.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Failed to export security report:', error)
    }
  }

  if (isLoading && !metrics) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-lg">加载安全数据...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">安全监控面板</h1>
          <p className="text-gray-600 mt-1">
            实时安全威胁监控、合规状态和审计日志
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <Button
            onClick={() => setAlertsEnabled(!alertsEnabled)}
            variant="outline"
            size="sm"
          >
            {alertsEnabled ? <Bell className="h-4 w-4 mr-2" /> : <BellOff className="h-4 w-4 mr-2" />}
            {alertsEnabled ? '关闭警报' : '启用警报'}
          </Button>
          <Button onClick={exportSecurityReport} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            导出报告
          </Button>
          <Button onClick={fetchSecurityMetrics} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      </div>

      {/* Critical Alerts */}
      {alerts.filter(alert => alert.severity === 'critical' && !alert.resolved).length > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertTitle className="text-red-800">严重安全警报</AlertTitle>
          <AlertDescription className="text-red-700">
            检测到 {alerts.filter(alert => alert.severity === 'critical' && !alert.resolved).length} 个严重安全威胁，需要立即处理。
          </AlertDescription>
        </Alert>
      )}

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">安全威胁</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.threats.total}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <Badge variant="destructive" className="text-xs">
                {metrics?.threats.critical} 严重
              </Badge>
              <Badge variant="secondary" className="text-xs">
                {metrics?.threats.blocked} 已阻止
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">HIPAA合规</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              {getComplianceIcon(metrics?.compliance.hipaa.status || 'unknown')}
              <div className="text-2xl font-bold">{metrics?.compliance.hipaa.score}%</div>
            </div>
            <p className="text-xs text-muted-foreground">
              最后检查: {new Date(metrics?.compliance.hipaa.lastChecked || '').toLocaleString('zh-CN')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.access.activeUsers}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="h-3 w-3 inline mr-1" />
              {metrics?.access.failedLogins} 次登录失败
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">审计日志</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.audit.totalLogs}</div>
            <p className="text-xs text-muted-foreground">
              {metrics?.audit.phiAccess} PHI访问记录
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Security Information */}
      <Tabs defaultValue="threats" className="space-y-4">
        <TabsList>
          <TabsTrigger value="threats">威胁监控</TabsTrigger>
          <TabsTrigger value="compliance">合规状态</TabsTrigger>
          <TabsTrigger value="access">访问控制</TabsTrigger>
          <TabsTrigger value="audit">审计日志</TabsTrigger>
        </TabsList>

        <TabsContent value="threats" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>威胁分布</CardTitle>
                <CardDescription>按严重程度分类的安全威胁</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">严重威胁</span>
                    <Badge variant="destructive">{metrics?.threats.critical}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">高风险威胁</span>
                    <Badge className="bg-orange-100 text-orange-800">{metrics?.threats.high}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">中等威胁</span>
                    <Badge variant="secondary">{metrics?.threats.medium}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">低风险威胁</span>
                    <Badge variant="outline">{metrics?.threats.low}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>安全警报</CardTitle>
                <CardDescription>最近的安全事件和警报</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {alerts.slice(0, 3).map((alert) => (
                  <div key={alert.id} className="flex items-start space-x-3 p-3 rounded-lg border">
                    <div className={`p-1 rounded-full ${getSeverityColor(alert.severity)}`}>
                      <AlertTriangle className="h-3 w-3" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">{alert.title}</p>
                      <p className="text-xs text-gray-500">{alert.description}</p>
                      <p className="text-xs text-gray-400 mt-1">
                        {new Date(alert.timestamp).toLocaleString('zh-CN')}
                      </p>
                    </div>
                    {!alert.acknowledged && (
                      <Button
                        onClick={() => acknowledgeAlert(alert.id)}
                        size="sm"
                        variant="outline"
                      >
                        确认
                      </Button>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>HIPAA合规状态</CardTitle>
                <CardDescription>医疗数据保护合规检查</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">合规评分</span>
                    <span className="text-sm font-bold">{metrics?.compliance.hipaa.score}%</span>
                  </div>
                  <Progress value={metrics?.compliance.hipaa.score} className="w-full" />
                </div>
                <div className="flex items-center space-x-2">
                  {getComplianceIcon(metrics?.compliance.hipaa.status || 'unknown')}
                  <span className={`text-sm font-medium ${getComplianceColor(metrics?.compliance.hipaa.status || 'unknown')}`}>
                    {metrics?.compliance.hipaa.status === 'compliant' && '完全合规'}
                    {metrics?.compliance.hipaa.status === 'partially_compliant' && '部分合规'}
                    {metrics?.compliance.hipaa.status === 'non_compliant' && '不合规'}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>GDPR合规状态</CardTitle>
                <CardDescription>数据保护和隐私合规检查</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">合规评分</span>
                    <span className="text-sm font-bold">{metrics?.compliance.gdpr.score}%</span>
                  </div>
                  <Progress value={metrics?.compliance.gdpr.score} className="w-full" />
                </div>
                <div className="flex items-center space-x-2">
                  {getComplianceIcon(metrics?.compliance.gdpr.status || 'unknown')}
                  <span className={`text-sm font-medium ${getComplianceColor(metrics?.compliance.gdpr.status || 'unknown')}`}>
                    {metrics?.compliance.gdpr.status === 'compliant' && '完全合规'}
                    {metrics?.compliance.gdpr.status === 'partially_compliant' && '部分合规'}
                    {metrics?.compliance.gdpr.status === 'non_compliant' && '不合规'}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="access" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>访问统计</CardTitle>
                <CardDescription>用户访问和认证统计</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">活跃用户</span>
                  <span className="text-sm font-bold">{metrics?.access.activeUsers}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">登录失败</span>
                  <Badge variant={metrics?.access.failedLogins! > 5 ? 'destructive' : 'secondary'}>
                    {metrics?.access.failedLogins}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">可疑活动</span>
                  <Badge variant={metrics?.access.suspiciousActivity! > 0 ? 'destructive' : 'default'}>
                    {metrics?.access.suspiciousActivity}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">被阻止IP</span>
                  <span className="text-sm font-bold">{metrics?.access.blockedIPs}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>访问控制</CardTitle>
                <CardDescription>权限和角色管理状态</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Lock className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-600">基于角色的访问控制已启用</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-600">多因素认证已配置</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Shield className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-600">会话安全已启用</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>审计统计</CardTitle>
                <CardDescription>系统活动和数据访问审计</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">总审计日志</span>
                  <span className="text-sm font-bold">{metrics?.audit.totalLogs}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">PHI访问记录</span>
                  <span className="text-sm font-bold">{metrics?.audit.phiAccess}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">数据导出</span>
                  <Badge variant={metrics?.audit.dataExports! > 10 ? 'destructive' : 'secondary'}>
                    {metrics?.audit.dataExports}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">配置更改</span>
                  <span className="text-sm font-bold">{metrics?.audit.configChanges}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>合规审计</CardTitle>
                <CardDescription>合规性检查和报告状态</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-xs text-muted-foreground">
                  最后更新: {lastUpdated.toLocaleTimeString('zh-CN')}
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-600">审计日志完整性验证通过</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-600">数据访问记录完整</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-600">合规报告自动生成</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
