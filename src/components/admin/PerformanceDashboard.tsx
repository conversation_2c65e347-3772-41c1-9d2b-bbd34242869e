/**
 * Performance Monitoring Dashboard
 * Real-time performance metrics, cache statistics, and system health monitoring
 */

'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { 
  Activity, 
  Database, 
  Zap, 
  Clock, 
  Users, 
  Server,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw
} from 'lucide-react'
import { useQueryPerformance, cacheManager } from '@/lib/react-query'

/**
 * Performance metrics interface
 */
interface PerformanceMetrics {
  api: {
    averageResponseTime: number
    requestsPerSecond: number
    errorRate: number
    activeRequests: number
  }
  database: {
    connectionPool: {
      active: number
      max: number
      utilization: number
    }
    slowQueries: number
    averageQueryTime: number
  }
  cache: {
    hitRatio: number
    totalEntries: number
    memoryUsage: number
    evictions: number
  }
  system: {
    memoryUsage: number
    cpuUsage: number
    uptime: number
    activeUsers: number
  }
}

/**
 * Health status type
 */
type HealthStatus = 'healthy' | 'warning' | 'critical'

/**
 * Performance Dashboard Component
 */
export const PerformanceDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [healthStatus, setHealthStatus] = useState<HealthStatus>('healthy')
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())
  
  const queryStats = useQueryPerformance()

  // Fetch performance metrics
  const fetchMetrics = async () => {
    try {
      setIsLoading(true)
      
      // In a real implementation, this would fetch from your monitoring API
      const response = await fetch('/api/admin/performance')
      const data = await response.json()
      
      setMetrics(data.metrics)
      setHealthStatus(data.healthStatus)
      setLastUpdated(new Date())
    } catch (error) {
      console.error('Failed to fetch performance metrics:', error)
      setHealthStatus('critical')
    } finally {
      setIsLoading(false)
    }
  }

  // Auto-refresh metrics
  useEffect(() => {
    fetchMetrics()
    const interval = setInterval(fetchMetrics, 30000) // Refresh every 30 seconds
    return () => clearInterval(interval)
  }, [])

  // Mock data for demonstration (remove in production)
  useEffect(() => {
    if (!metrics) {
      setMetrics({
        api: {
          averageResponseTime: 145,
          requestsPerSecond: 23,
          errorRate: 0.5,
          activeRequests: 12,
        },
        database: {
          connectionPool: {
            active: 8,
            max: 20,
            utilization: 0.4,
          },
          slowQueries: 2,
          averageQueryTime: 35,
        },
        cache: {
          hitRatio: 0.87,
          totalEntries: 1247,
          memoryUsage: 156,
          evictions: 23,
        },
        system: {
          memoryUsage: 0.65,
          cpuUsage: 0.32,
          uptime: 86400 * 7, // 7 days
          activeUsers: 45,
        },
      })
      setIsLoading(false)
    }
  }, [metrics])

  const getStatusColor = (status: HealthStatus) => {
    switch (status) {
      case 'healthy': return 'text-green-600'
      case 'warning': return 'text-yellow-600'
      case 'critical': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: HealthStatus) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-600" />
      case 'critical': return <XCircle className="h-5 w-5 text-red-600" />
      default: return <Activity className="h-5 w-5 text-gray-600" />
    }
  }

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${days}d ${hours}h ${minutes}m`
  }

  if (isLoading && !metrics) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-lg">加载性能数据...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">性能监控面板</h1>
          <p className="text-gray-600 mt-1">
            实时系统性能指标和健康状态监控
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            {getStatusIcon(healthStatus)}
            <span className={`font-medium ${getStatusColor(healthStatus)}`}>
              {healthStatus === 'healthy' && '系统正常'}
              {healthStatus === 'warning' && '需要关注'}
              {healthStatus === 'critical' && '严重问题'}
            </span>
          </div>
          <Button onClick={fetchMetrics} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均响应时间</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.api.averageResponseTime}ms</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="h-3 w-3 inline mr-1" />
              比昨天快 12%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">每秒请求数</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.api.requestsPerSecond}</div>
            <p className="text-xs text-muted-foreground">
              当前活跃请求: {metrics?.api.activeRequests}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">缓存命中率</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {((metrics?.cache.hitRatio || 0) * 100).toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              缓存条目: {metrics?.cache.totalEntries}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.system.activeUsers}</div>
            <p className="text-xs text-muted-foreground">
              系统运行时间: {formatUptime(metrics?.system.uptime || 0)}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <Tabs defaultValue="api" className="space-y-4">
        <TabsList>
          <TabsTrigger value="api">API 性能</TabsTrigger>
          <TabsTrigger value="database">数据库</TabsTrigger>
          <TabsTrigger value="cache">缓存系统</TabsTrigger>
          <TabsTrigger value="system">系统资源</TabsTrigger>
        </TabsList>

        <TabsContent value="api" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>API 响应时间</CardTitle>
                <CardDescription>平均响应时间和错误率统计</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">平均响应时间</span>
                  <Badge variant={metrics?.api.averageResponseTime! < 200 ? 'default' : 'destructive'}>
                    {metrics?.api.averageResponseTime}ms
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">错误率</span>
                  <Badge variant={metrics?.api.errorRate! < 1 ? 'default' : 'destructive'}>
                    {metrics?.api.errorRate}%
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">每秒请求数</span>
                  <span className="text-sm font-bold">{metrics?.api.requestsPerSecond} RPS</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>React Query 统计</CardTitle>
                <CardDescription>客户端缓存和查询状态</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">总查询数</span>
                  <span className="text-sm font-bold">{queryStats.totalQueries}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">过期查询</span>
                  <span className="text-sm font-bold">{queryStats.staleQueries}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">正在获取</span>
                  <span className="text-sm font-bold">{queryStats.fetchingQueries}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">错误查询</span>
                  <Badge variant={queryStats.errorQueries > 0 ? 'destructive' : 'default'}>
                    {queryStats.errorQueries}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="database" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>连接池状态</CardTitle>
                <CardDescription>数据库连接池使用情况</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">连接池利用率</span>
                    <span className="text-sm font-bold">
                      {((metrics?.database.connectionPool.utilization || 0) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <Progress 
                    value={(metrics?.database.connectionPool.utilization || 0) * 100} 
                    className="w-full"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">活跃连接</span>
                  <span className="text-sm font-bold">
                    {metrics?.database.connectionPool.active} / {metrics?.database.connectionPool.max}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>查询性能</CardTitle>
                <CardDescription>数据库查询执行统计</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">平均查询时间</span>
                  <Badge variant={metrics?.database.averageQueryTime! < 50 ? 'default' : 'destructive'}>
                    {metrics?.database.averageQueryTime}ms
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">慢查询数量</span>
                  <Badge variant={metrics?.database.slowQueries! === 0 ? 'default' : 'destructive'}>
                    {metrics?.database.slowQueries}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="cache" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>缓存效率</CardTitle>
                <CardDescription>缓存命中率和使用统计</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">命中率</span>
                    <span className="text-sm font-bold">
                      {((metrics?.cache.hitRatio || 0) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <Progress 
                    value={(metrics?.cache.hitRatio || 0) * 100} 
                    className="w-full"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">缓存条目</span>
                  <span className="text-sm font-bold">{metrics?.cache.totalEntries}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">内存使用</span>
                  <span className="text-sm font-bold">{metrics?.cache.memoryUsage}MB</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>缓存操作</CardTitle>
                <CardDescription>缓存管理和清理操作</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">缓存驱逐</span>
                  <span className="text-sm font-bold">{metrics?.cache.evictions}</span>
                </div>
                <div className="space-y-2">
                  <Button 
                    onClick={() => cacheManager.invalidate.all()} 
                    variant="outline" 
                    size="sm"
                    className="w-full"
                  >
                    清空所有缓存
                  </Button>
                  <Button 
                    onClick={() => cacheManager.invalidate.clients()} 
                    variant="outline" 
                    size="sm"
                    className="w-full"
                  >
                    清空客户缓存
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>系统资源</CardTitle>
                <CardDescription>CPU 和内存使用情况</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">CPU 使用率</span>
                    <span className="text-sm font-bold">
                      {((metrics?.system.cpuUsage || 0) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <Progress 
                    value={(metrics?.system.cpuUsage || 0) * 100} 
                    className="w-full"
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">内存使用率</span>
                    <span className="text-sm font-bold">
                      {((metrics?.system.memoryUsage || 0) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <Progress 
                    value={(metrics?.system.memoryUsage || 0) * 100} 
                    className="w-full"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>系统状态</CardTitle>
                <CardDescription>运行时间和用户活动</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">系统运行时间</span>
                  <span className="text-sm font-bold">
                    {formatUptime(metrics?.system.uptime || 0)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">活跃用户</span>
                  <span className="text-sm font-bold">{metrics?.system.activeUsers}</span>
                </div>
                <div className="text-xs text-muted-foreground">
                  最后更新: {lastUpdated.toLocaleTimeString('zh-CN')}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
