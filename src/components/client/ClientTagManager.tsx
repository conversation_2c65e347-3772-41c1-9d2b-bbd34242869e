'use client'

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import { Tag, Plus, X, Check } from 'lucide-react'
import { showSuccessToast, showErrorToast } from '@/lib/toast-utils'

interface ClientTag {
  id: string
  name: string
  color: string
  description?: string
}

interface ClientTagManagerProps {
  clientId: string
  clientTags: ClientTag[]
  onTagsUpdate: (tags: ClientTag[]) => void
}

const predefinedTags: Omit<ClientTag, 'id'>[] = [
  { name: 'VIP客户', color: 'bg-purple-100 text-purple-800 border-purple-200', description: '高价值客户' },
  { name: '新客户', color: 'bg-green-100 text-green-800 border-green-200', description: '首次到访客户' },
  { name: '老客户', color: 'bg-blue-100 text-blue-800 border-blue-200', description: '多次到访客户' },
  { name: '推荐客户', color: 'bg-orange-100 text-orange-800 border-orange-200', description: '通过推荐获得的客户' },
  { name: '敏感肌肤', color: 'bg-red-100 text-red-800 border-red-200', description: '需要特殊护理' },
  { name: '定期保养', color: 'bg-teal-100 text-teal-800 border-teal-200', description: '定期进行保养治疗' },
  { name: '特殊需求', color: 'bg-yellow-100 text-yellow-800 border-yellow-200', description: '有特殊治疗需求' },
  { name: '价格敏感', color: 'bg-gray-100 text-gray-800 border-gray-200', description: '对价格比较敏感' },
  { name: '效果导向', color: 'bg-indigo-100 text-indigo-800 border-indigo-200', description: '注重治疗效果' },
  { name: '时间紧张', color: 'bg-pink-100 text-pink-800 border-pink-200', description: '时间安排较紧' }
]

export default function ClientTagManager({ clientId, clientTags, onTagsUpdate }: ClientTagManagerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [availableTags, setAvailableTags] = useState<ClientTag[]>([])
  const [newTagName, setNewTagName] = useState('')
  const [isCreatingTag, setIsCreatingTag] = useState(false)

  useEffect(() => {
    // Initialize available tags with predefined ones
    const tagsWithIds = predefinedTags.map((tag, index) => ({
      ...tag,
      id: `predefined-${index}`
    }))
    setAvailableTags(tagsWithIds)
  }, [])

  const handleAddTag = async (tag: ClientTag) => {
    try {
      // Check if tag is already added
      if (clientTags.some(t => t.id === tag.id)) {
        return
      }

      const updatedTags = [...clientTags, tag]
      onTagsUpdate(updatedTags)
      
      // Here you would typically save to backend
      // await saveClientTags(clientId, updatedTags)
      
      showSuccessToast('标签添加成功', `已为客户添加"${tag.name}"标签`)
    } catch (error) {
      console.error('Error adding tag:', error)
      showErrorToast('添加标签失败', '请稍后重试')
    }
  }

  const handleRemoveTag = async (tagId: string) => {
    try {
      const updatedTags = clientTags.filter(tag => tag.id !== tagId)
      onTagsUpdate(updatedTags)
      
      // Here you would typically save to backend
      // await saveClientTags(clientId, updatedTags)
      
      const removedTag = clientTags.find(t => t.id === tagId)
      showSuccessToast('标签移除成功', `已移除"${removedTag?.name}"标签`)
    } catch (error) {
      console.error('Error removing tag:', error)
      showErrorToast('移除标签失败', '请稍后重试')
    }
  }

  const handleCreateCustomTag = async () => {
    if (!newTagName.trim()) return

    try {
      setIsCreatingTag(true)
      
      const customTag: ClientTag = {
        id: `custom-${Date.now()}`,
        name: newTagName.trim(),
        color: 'bg-slate-100 text-slate-800 border-slate-200',
        description: '自定义标签'
      }

      // Add to available tags
      setAvailableTags(prev => [...prev, customTag])
      
      // Add to client tags
      await handleAddTag(customTag)
      
      setNewTagName('')
      setIsOpen(false)
    } catch (error) {
      console.error('Error creating custom tag:', error)
      showErrorToast('创建标签失败', '请稍后重试')
    } finally {
      setIsCreatingTag(false)
    }
  }

  const unselectedTags = availableTags.filter(
    tag => !clientTags.some(clientTag => clientTag.id === tag.id)
  )

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <Label className="flex items-center gap-2">
          <Tag className="h-4 w-4" />
          客户标签
        </Label>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm">
              <Plus className="h-3 w-3 mr-1" />
              添加标签
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-0" align="end">
            <Command>
              <CommandInput placeholder="搜索标签..." />
              <CommandList>
                <CommandEmpty>
                  <div className="p-4 text-center">
                    <p className="text-sm text-muted-foreground mb-3">未找到匹配的标签</p>
                    <div className="space-y-2">
                      <Input
                        placeholder="输入新标签名称"
                        value={newTagName}
                        onChange={(e) => setNewTagName(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleCreateCustomTag()
                          }
                        }}
                      />
                      <Button
                        size="sm"
                        onClick={handleCreateCustomTag}
                        disabled={!newTagName.trim() || isCreatingTag}
                        className="w-full"
                      >
                        {isCreatingTag ? '创建中...' : '创建自定义标签'}
                      </Button>
                    </div>
                  </div>
                </CommandEmpty>
                
                {unselectedTags.length > 0 && (
                  <CommandGroup heading="可用标签">
                    {unselectedTags.map((tag) => (
                      <CommandItem
                        key={tag.id}
                        onSelect={() => handleAddTag(tag)}
                        className="cursor-pointer"
                      >
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center gap-2">
                            <Badge className={tag.color}>
                              {tag.name}
                            </Badge>
                            {tag.description && (
                              <span className="text-xs text-muted-foreground">
                                {tag.description}
                              </span>
                            )}
                          </div>
                          <Plus className="h-3 w-3" />
                        </div>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                )}
                
                <div className="p-3 border-t">
                  <div className="space-y-2">
                    <Label className="text-xs">创建自定义标签</Label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="标签名称"
                        value={newTagName}
                        onChange={(e) => setNewTagName(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleCreateCustomTag()
                          }
                        }}
                        className="flex-1"
                      />
                      <Button
                        size="sm"
                        onClick={handleCreateCustomTag}
                        disabled={!newTagName.trim() || isCreatingTag}
                      >
                        <Plus className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>

      {/* Current Tags */}
      <div className="flex flex-wrap gap-2">
        {clientTags.length === 0 ? (
          <p className="text-sm text-muted-foreground">暂无标签</p>
        ) : (
          clientTags.map((tag) => (
            <Badge
              key={tag.id}
              className={`${tag.color} group cursor-pointer hover:opacity-80 transition-opacity`}
              onClick={() => handleRemoveTag(tag.id)}
            >
              <span>{tag.name}</span>
              <X className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
            </Badge>
          ))
        )}
      </div>

      {clientTags.length > 0 && (
        <p className="text-xs text-muted-foreground">
          💡 点击标签可以移除
        </p>
      )}
    </div>
  )
}

// Hook for managing client tags
export const useClientTags = (clientId: string) => {
  const [tags, setTags] = useState<ClientTag[]>([])
  const [loading, setLoading] = useState(true)

  const fetchTags = async () => {
    try {
      setLoading(true)
      // Here you would fetch from your API
      // const response = await fetch(`/api/clients/${clientId}/tags`)
      // const data = await response.json()
      // setTags(data.tags || [])
      
      // For now, return empty array
      setTags([])
    } catch (error) {
      console.error('Error fetching client tags:', error)
    } finally {
      setLoading(false)
    }
  }

  const saveTags = async (newTags: ClientTag[]) => {
    try {
      // Here you would save to your API
      // await fetch(`/api/clients/${clientId}/tags`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ tags: newTags })
      // })
      
      setTags(newTags)
    } catch (error) {
      console.error('Error saving client tags:', error)
      throw error
    }
  }

  useEffect(() => {
    if (clientId) {
      fetchTags()
    }
  }, [clientId])

  return {
    tags,
    loading,
    updateTags: saveTags,
    refreshTags: fetchTags
  }
}
