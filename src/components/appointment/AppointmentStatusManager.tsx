'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { 
  MoreHorizontal, 
  Calendar, 
  CheckCircle, 
  Clock, 
  XCircle, 
  AlertCircle,
  Trash2,
  Edit
} from 'lucide-react'
import { showSuccessToast, showErrorToast } from '@/lib/toast-utils'
import { getStatusColor, getStatusText } from '@/lib/colors'

interface AppointmentStatusManagerProps {
  appointment: {
    id: string
    status: string
    client_name: string
    treatment_name: string
    appointment_date: string
    start_time: string
    end_time: string
  }
  onStatusChange: (appointmentId: string, newStatus: string) => void
  onEdit: () => void
  onDelete: () => void
}

const statusOptions = [
  { value: 'scheduled', label: '已预约', icon: Calendar, color: 'blue' },
  { value: 'confirmed', label: '已确认', icon: CheckCircle, color: 'green' },
  { value: 'in_progress', label: '进行中', icon: Clock, color: 'yellow' },
  { value: 'completed', label: '已完成', icon: CheckCircle, color: 'emerald' },
  { value: 'cancelled', label: '已取消', icon: XCircle, color: 'red' },
  { value: 'no_show', label: '未到场', icon: AlertCircle, color: 'orange' }
]

export default function AppointmentStatusManager({
  appointment,
  onStatusChange,
  onEdit,
  onDelete
}: AppointmentStatusManagerProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)

  const currentStatus = statusOptions.find(s => s.value === appointment.status)
  const statusColor = getStatusColor('appointment', appointment.status)

  const handleStatusChange = async (newStatus: string) => {
    if (newStatus === appointment.status) return

    setIsUpdating(true)
    try {
      const response = await fetch(`/api/appointments/${appointment.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...appointment,
          status: newStatus
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || '更新状态失败')
      }

      onStatusChange(appointment.id, newStatus)
      showSuccessToast(
        '状态更新成功',
        `预约状态已更新为：${getStatusText('appointment', newStatus)}`
      )
    } catch (error: any) {
      console.error('Error updating appointment status:', error)
      showErrorToast('更新失败', error.message)
    } finally {
      setIsUpdating(false)
    }
  }

  const handleDelete = async () => {
    try {
      const response = await fetch(`/api/appointments/${appointment.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || '删除预约失败')
      }

      onDelete()
      showSuccessToast('删除成功', '预约已成功删除')
    } catch (error: any) {
      console.error('Error deleting appointment:', error)
      showErrorToast('删除失败', error.message)
    }
    setIsDeleteDialogOpen(false)
  }

  return (
    <>
      <div className="flex items-center gap-2">
        {/* Current Status Badge */}
        <Badge className={`${statusColor.bg} ${statusColor.text} ${statusColor.border} border`}>
          {currentStatus && <currentStatus.icon className="w-3 h-3 mr-1" />}
          {getStatusText('appointment', appointment.status)}
        </Badge>

        {/* Actions Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-8 w-8 p-0"
              disabled={isUpdating}
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            {/* Edit Option */}
            <DropdownMenuItem onClick={onEdit}>
              <Edit className="mr-2 h-4 w-4" />
              编辑预约
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            
            {/* Status Change Options */}
            {statusOptions
              .filter(status => status.value !== appointment.status)
              .map((status) => (
                <DropdownMenuItem
                  key={status.value}
                  onClick={() => handleStatusChange(status.value)}
                  disabled={isUpdating}
                >
                  <status.icon className="mr-2 h-4 w-4" />
                  更改为：{status.label}
                </DropdownMenuItem>
              ))}
            
            <DropdownMenuSeparator />
            
            {/* Delete Option */}
            <DropdownMenuItem
              onClick={() => setIsDeleteDialogOpen(true)}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              删除预约
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除预约</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除以下预约吗？此操作无法撤销。
              <div className="mt-3 p-3 bg-muted rounded-lg">
                <p className="font-medium">{appointment.client_name}</p>
                <p className="text-sm text-muted-foreground">{appointment.treatment_name}</p>
                <p className="text-sm text-muted-foreground">
                  {appointment.appointment_date} {appointment.start_time} - {appointment.end_time}
                </p>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
