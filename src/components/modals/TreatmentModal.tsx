'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Package, Clock, DollarSign, FileText, Languages } from 'lucide-react'
import { EnhancedInput, EnhancedTextarea, EnhancedSelect } from '@/components/ui/enhanced-input'
import { validateForm, validateField, COMMON_RULES, ValidationRule } from '@/lib/form-validation'
import { showSuccessToast, showErrorToast } from '@/lib/toast-utils'

interface Treatment {
  id: string
  name: string
  name_chinese: string
  description: string | null
  description_chinese: string | null
  default_price: number
  fixed_deposit_amount: number
  consultation_fee: number
  duration_minutes: number
  category: string
  is_active: boolean
  requires_consultation: boolean
  created_at: string
  updated_at: string
}

interface TreatmentModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  editingTreatment?: Treatment | null
  mode?: 'view' | 'edit' | 'create'
}

export default function TreatmentModal({
  isOpen,
  onClose,
  onSuccess,
  editingTreatment,
  mode = 'create'
}: TreatmentModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    name_chinese: '',
    description: '',
    description_chinese: '',
    default_price: '',
    fixed_deposit_amount: '',
    consultation_fee: '',
    duration_minutes: '',
    category: '',
    is_active: true,
    requires_consultation: false
  })

  useEffect(() => {
    if (editingTreatment) {
      setFormData({
        name: editingTreatment.name,
        name_chinese: editingTreatment.name_chinese,
        description: editingTreatment.description || '',
        description_chinese: editingTreatment.description_chinese || '',
        default_price: editingTreatment.default_price.toString(),
        fixed_deposit_amount: editingTreatment.fixed_deposit_amount?.toString() || '',
        consultation_fee: editingTreatment.consultation_fee?.toString() || '',
        duration_minutes: editingTreatment.duration_minutes.toString(),
        category: editingTreatment.category,
        is_active: editingTreatment.is_active,
        requires_consultation: editingTreatment.requires_consultation
      })
    } else {
      setFormData({
        name: '',
        name_chinese: '',
        description: '',
        description_chinese: '',
        default_price: '',
        fixed_deposit_amount: '',
        consultation_fee: '',
        duration_minutes: '',
        category: '',
        is_active: true,
        requires_consultation: false
      })
    }
  }, [editingTreatment, isOpen])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (mode === 'view') return

    if (!formData.name || !formData.name_chinese || !formData.default_price || !formData.duration_minutes || !formData.category) {
      showErrorToast('请填写必填字段')
      return
    }

    setLoading(true)
    try {
      const url = editingTreatment 
        ? `/api/treatments/${editingTreatment.id}`
        : '/api/treatments'
      
      const method = editingTreatment ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          default_price: parseFloat(formData.default_price),
          fixed_deposit_amount: formData.fixed_deposit_amount ? parseFloat(formData.fixed_deposit_amount) : 0,
          consultation_fee: formData.consultation_fee ? parseFloat(formData.consultation_fee) : 0,
          duration_minutes: parseInt(formData.duration_minutes)
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || '操作失败')
      }

      showSuccessToast(editingTreatment ? '治疗项目更新成功' : '治疗项目创建成功')
      onSuccess()
      handleClose()
    } catch (error: any) {
      console.error('Error saving treatment:', error)
      showErrorToast(error.message || '操作失败')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    onClose()
    setFormData({
      name: '',
      name_chinese: '',
      description: '',
      description_chinese: '',
      default_price: '',
      fixed_deposit_amount: '',
      consultation_fee: '',
      duration_minutes: '',
      category: '',
      is_active: true,
      requires_consultation: false
    })
  }

  const isReadOnly = mode === 'view'

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {mode === 'view' ? '查看治疗项目' : 
             mode === 'edit' ? '编辑治疗项目' : '新建治疗项目'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Names */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Languages className="h-4 w-4" />
                中文名称 *
              </Label>
              <Input
                placeholder="治疗项目中文名称"
                value={formData.name_chinese}
                onChange={(e) => setFormData(prev => ({ ...prev, name_chinese: e.target.value }))}
                disabled={isReadOnly}
                required
              />
            </div>
            <div className="space-y-2">
              <Label>英文名称 *</Label>
              <Input
                placeholder="Treatment English Name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                disabled={isReadOnly}
                required
              />
            </div>
          </div>

          {/* Price and Duration */}
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                默认价格 *
              </Label>
              <Input
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                value={formData.default_price}
                onChange={(e) => setFormData(prev => ({ ...prev, default_price: e.target.value }))}
                disabled={isReadOnly}
                required
              />
            </div>
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                时长(分钟) *
              </Label>
              <Input
                type="number"
                min="1"
                placeholder="60"
                value={formData.duration_minutes}
                onChange={(e) => setFormData(prev => ({ ...prev, duration_minutes: e.target.value }))}
                disabled={isReadOnly}
                required
              />
            </div>
            <div className="space-y-2">
              <Label>类别 *</Label>
              <Select 
                value={formData.category} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                disabled={isReadOnly}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择类别" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="面部护理">面部护理</SelectItem>
                  <SelectItem value="身体护理">身体护理</SelectItem>
                  <SelectItem value="激光治疗">激光治疗</SelectItem>
                  <SelectItem value="注射治疗">注射治疗</SelectItem>
                  <SelectItem value="皮肤治疗">皮肤治疗</SelectItem>
                  <SelectItem value="美容手术">美容手术</SelectItem>
                  <SelectItem value="咨询服务">咨询服务</SelectItem>
                  <SelectItem value="其他">其他</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Deposit and Consultation Fee */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                固定定金金额
              </Label>
              <Input
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                value={formData.fixed_deposit_amount}
                onChange={(e) => setFormData(prev => ({ ...prev, fixed_deposit_amount: e.target.value }))}
                disabled={isReadOnly}
              />
              <p className="text-xs text-muted-foreground">
                预约此治疗项目需要支付的固定定金金额
              </p>
            </div>
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                咨询费用
              </Label>
              <Input
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                value={formData.consultation_fee}
                onChange={(e) => setFormData(prev => ({ ...prev, consultation_fee: e.target.value }))}
                disabled={isReadOnly || !formData.requires_consultation}
              />
              <p className="text-xs text-muted-foreground">
                {formData.requires_consultation ? '咨询预约的费用（后续治疗时可减免）' : '仅在需要咨询时设置'}
              </p>
            </div>
          </div>

          {/* Descriptions */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                中文描述
              </Label>
              <Textarea
                placeholder="治疗项目的中文详细描述..."
                value={formData.description_chinese}
                onChange={(e) => setFormData(prev => ({ ...prev, description_chinese: e.target.value }))}
                disabled={isReadOnly}
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label>英文描述</Label>
              <Textarea
                placeholder="Treatment description in English..."
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                disabled={isReadOnly}
                rows={3}
              />
            </div>
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>项目状态</Label>
                <p className="text-sm text-muted-foreground">
                  启用后客户可以预约此项目
                </p>
              </div>
              <Switch
                checked={formData.is_active}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                disabled={isReadOnly}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>需要咨询</Label>
                <p className="text-sm text-muted-foreground">
                  启用后预约此项目需要先进行咨询
                </p>
              </div>
              <Switch
                checked={formData.requires_consultation}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, requires_consultation: checked }))}
                disabled={isReadOnly}
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              {mode === 'view' ? '关闭' : '取消'}
            </Button>
            {mode !== 'view' && (
              <Button type="submit" disabled={loading}>
                {loading ? '保存中...' : (editingTreatment ? '更新项目' : '创建项目')}
              </Button>
            )}
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
