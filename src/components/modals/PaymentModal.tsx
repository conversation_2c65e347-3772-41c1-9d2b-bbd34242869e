'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { CreditCard, Receipt, Calendar, DollarSign, FileText, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { EnhancedInput, EnhancedTextarea, EnhancedSelect } from '@/components/ui/enhanced-input'
import { validateForm, validateField, COMMON_RULES, ValidationRule } from '@/lib/form-validation'
import { showSuccessToast, showErrorToast } from '@/lib/toast-utils'

interface Invoice {
  id: string
  invoice_number: string
  client_id: string
  total_amount: number
  deposit_amount: number
  status: string
  clients: {
    first_name: string
    last_name: string
    phone: string
  }
}

interface PaymentModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  preSelectedInvoice?: Invoice
  editingPayment?: any
}

export default function PaymentModal({
  isOpen,
  onClose,
  onSuccess,
  preSelectedInvoice,
  editingPayment
}: PaymentModalProps) {
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})
  const [formData, setFormData] = useState({
    invoice_id: '',
    client_id: '',
    amount: '',
    payment_date: '',
    payment_method: '',
    payment_type: '',
    reference_number: '',
    notes: ''
  })

  // 验证规则
  const validationRules: Record<string, ValidationRule> = {
    invoice_id: { required: true, custom: (value) => !value ? '请选择账单' : null },
    amount: COMMON_RULES.currency,
    payment_date: COMMON_RULES.date,
    payment_method: { required: true },
    payment_type: { required: true },
    reference_number: COMMON_RULES.shortText,
    notes: COMMON_RULES.notes
  }

  // 字段更新函数
  const updateField = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    // 如果字段已被触摸，立即验证
    if (touched[field]) {
      const rule = validationRules[field]
      if (rule) {
        const error = validateField(value, rule)
        setErrors(prev => ({ ...prev, [field]: error || '' }))
      }
    }
  }

  // 字段失焦处理
  const handleFieldBlur = (field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }))

    const rule = validationRules[field]
    if (rule && field in formData) {
      const error = validateField(formData[field as keyof typeof formData], rule)
      setErrors(prev => ({ ...prev, [field]: error || '' }))
    }
  }

  // Initialize form data when modal opens
  useEffect(() => {
    if (isOpen) {
      if (editingPayment) {
        // Editing mode
        setFormData({
          invoice_id: editingPayment.invoice_id || '',
          client_id: editingPayment.client_id || '',
          amount: editingPayment.amount?.toString() || '',
          payment_date: editingPayment.payment_date || '',
          payment_method: editingPayment.payment_method || '',
          payment_type: editingPayment.payment_type || '',
          reference_number: editingPayment.reference_number || '',
          notes: editingPayment.notes || ''
        })
      } else {
        // Creation mode
        setFormData({
          invoice_id: preSelectedInvoice?.id || '',
          client_id: preSelectedInvoice?.client_id || '',
          amount: preSelectedInvoice?.deposit_amount?.toString() || '',
          payment_date: new Date().toISOString().split('T')[0],
          payment_method: '',
          payment_type: 'deposit',
          reference_number: '',
          notes: ''
        })
      }
      
      fetchInvoices()
    }
  }, [isOpen, preSelectedInvoice, editingPayment])

  const fetchInvoices = async () => {
    try {
      const response = await fetch('/api/invoices')
      const data = await response.json()
      // Filter to show only invoices that are not fully paid
      const unpaidInvoices = data.invoices?.filter((inv: Invoice) => 
        inv.status !== 'paid_full'
      ) || []
      setInvoices(unpaidInvoices)
    } catch (error) {
      console.error('Error fetching invoices:', error)
    }
  }

  const validateFormData = () => {
    const result = validateForm(formData, validationRules)
    setErrors(result.errors)
    setTouched(Object.keys(validationRules).reduce((acc, key) => ({ ...acc, [key]: true }), {}))
    return result.isValid
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateFormData()) {
      showErrorToast('请检查表单信息', '请修正标红的字段后重试')
      return
    }

    setLoading(true)

    try {
      const submitData = {
        invoice_id: formData.invoice_id,
        client_id: formData.client_id,
        amount: parseFloat(formData.amount),
        payment_date: formData.payment_date,
        payment_method: formData.payment_method,
        payment_type: formData.payment_type,
        reference_number: formData.reference_number || null,
        notes: formData.notes || null
      }

      const url = editingPayment ? `/api/payments/${editingPayment.id}` : '/api/payments'
      const method = editingPayment ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(submitData)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || '操作失败')
      }

      showSuccessToast(
        editingPayment ? '付款记录更新成功' : '付款记录创建成功',
        editingPayment ? '付款信息已更新' : '新付款记录已创建'
      )
      onSuccess()
      onClose()
      resetForm()
    } catch (error) {
      console.error('Error saving payment:', error)
      showErrorToast(
        '保存失败',
        error instanceof Error ? error.message : '保存付款记录失败'
      )
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setFormData({
      invoice_id: '',
      client_id: '',
      amount: '',
      payment_date: new Date().toISOString().split('T')[0],
      payment_method: '',
      payment_type: 'deposit',
      reference_number: '',
      notes: ''
    })
  }

  const handleClose = () => {
    onClose()
    resetForm()
  }

  const handleInvoiceChange = (invoiceId: string) => {
    const selectedInvoice = invoices.find(inv => inv.id === invoiceId)
    if (selectedInvoice) {
      setFormData(prev => ({
        ...prev,
        invoice_id: invoiceId,
        client_id: selectedInvoice.client_id,
        amount: selectedInvoice.deposit_amount.toString()
      }))
    }
  }

  const selectedInvoice = invoices.find(inv => inv.id === formData.invoice_id)

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            {editingPayment ? '编辑付款记录' : '记录付款'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Invoice Selection */}
          <EnhancedSelect
            label="账单"
            icon={<Receipt className="h-4 w-4" />}
            placeholder="选择账单"
            value={formData.invoice_id}
            onChange={handleInvoiceChange}
            onBlur={() => handleFieldBlur('invoice_id')}
            error={errors.invoice_id}
            touched={touched.invoice_id}
            required
            validation={validationRules.invoice_id}
            options={invoices.map(invoice => ({
              value: invoice.id,
              label: `${invoice.invoice_number} - ${invoice.clients.last_name}${invoice.clients.first_name} - $${invoice.total_amount}`
            }))}
          />
            {selectedInvoice && (
              <div className="text-sm text-muted-foreground bg-gray-50 p-3 rounded">
                <div className="grid grid-cols-2 gap-2">
                  <div>客户: {selectedInvoice.clients.last_name}{selectedInvoice.clients.first_name}</div>
                  <div>电话: {selectedInvoice.clients.phone}</div>
                  <div>总金额: ${selectedInvoice.total_amount}</div>
                  <div>定金: ${selectedInvoice.deposit_amount}</div>
                </div>
              </div>
            )}

          {/* Payment Amount */}
          <EnhancedInput
            label="付款金额"
            icon={<DollarSign className="h-4 w-4" />}
            type="currency"
            placeholder="0.00"
            value={formData.amount}
            onChange={(value) => updateField('amount', value)}
            onBlur={() => handleFieldBlur('amount')}
            error={errors.amount}
            touched={touched.amount}
            required
            validation={validationRules.amount}
            helpText="输入实际付款金额"
          />

          {/* Payment Date */}
          <div className="space-y-2">
            <Label className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              付款日期
              <span className="text-red-500">*</span>
            </Label>
            <Input
              type="date"
              value={formData.payment_date}
              onChange={(e) => updateField('payment_date', e.target.value)}
              onBlur={() => handleFieldBlur('payment_date')}
              className={cn(
                "transition-colors",
                touched.payment_date && errors.payment_date && "border-red-500 focus:border-red-500"
              )}
            />
            {touched.payment_date && errors.payment_date && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.payment_date}
              </p>
            )}
          </div>

          {/* Payment Method and Type */}
          <div className="grid grid-cols-2 gap-4">
            <EnhancedSelect
              label="付款方式"
              placeholder="选择付款方式"
              value={formData.payment_method}
              onChange={(value) => updateField('payment_method', value)}
              onBlur={() => handleFieldBlur('payment_method')}
              error={errors.payment_method}
              touched={touched.payment_method}
              required
              validation={validationRules.payment_method}
              options={[
                { value: 'cash', label: '现金' },
                { value: 'credit_card', label: '信用卡' },
                { value: 'debit_card', label: '借记卡' },
                { value: 'bank_transfer', label: '银行转账' },
                { value: 'check', label: '支票' },
                { value: 'other', label: '其他' }
              ]}
            />
            <EnhancedSelect
              label="付款类型"
              placeholder="选择付款类型"
              value={formData.payment_type}
              onChange={(value) => updateField('payment_type', value)}
              onBlur={() => handleFieldBlur('payment_type')}
              error={errors.payment_type}
              touched={touched.payment_type}
              required
              validation={validationRules.payment_type}
              options={[
                { value: 'deposit', label: '定金' },
                { value: 'partial', label: '部分付款' },
                { value: 'full', label: '全额付款' },
                { value: 'refund', label: '退款' }
              ]}
            />
          </div>

          {/* Reference Number */}
          <EnhancedInput
            label="参考号码"
            placeholder="交易参考号、支票号等"
            value={formData.reference_number}
            onChange={(value) => updateField('reference_number', value)}
            onBlur={() => handleFieldBlur('reference_number')}
            error={errors.reference_number}
            touched={touched.reference_number}
            validation={validationRules.reference_number}
            helpText="可选：输入交易参考号、支票号等"
          />

          {/* Notes */}
          <EnhancedTextarea
            label="备注"
            icon={<FileText className="h-4 w-4" />}
            placeholder="付款相关备注信息..."
            value={formData.notes}
            onChange={(value) => updateField('notes', value)}
            onBlur={() => handleFieldBlur('notes')}
            error={errors.notes}
            touched={touched.notes}
            validation={validationRules.notes}
            rows={3}
            maxLength={1000}
          />

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? '保存中...' : (editingPayment ? '更新记录' : '记录付款')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
