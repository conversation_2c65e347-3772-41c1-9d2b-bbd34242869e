'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Receipt, User, Calendar, DollarSign, FileText, Percent, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { EnhancedInput, EnhancedTextarea, EnhancedSelect } from '@/components/ui/enhanced-input'
import { validateForm, validateField, COMMON_RULES, ValidationRule } from '@/lib/form-validation'
import { showSuccessToast, showErrorToast } from '@/lib/toast-utils'

interface Client {
  id: string
  first_name: string
  last_name: string
  phone: string
}

interface InvoiceModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  editingInvoice?: any
}

export default function InvoiceModal({
  isOpen,
  onClose,
  onSuccess,
  editingInvoice
}: InvoiceModalProps) {
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})
  const [formData, setFormData] = useState({
    client_id: '',
    treatment_date: '',
    total_amount: '',
    deposit_percentage: '50',
    deposit_amount: '',
    consultation_fee_waived: false,
    original_consultation_fee: '',
    due_date: '',
    notes: ''
  })

  // 验证规则
  const validationRules: Record<string, ValidationRule> = {
    client_id: { required: true, custom: (value) => !value ? '请选择客户' : null },
    treatment_date: COMMON_RULES.date,
    total_amount: COMMON_RULES.currency,
    deposit_percentage: COMMON_RULES.percentage,
    deposit_amount: COMMON_RULES.currency,
    original_consultation_fee: { ...COMMON_RULES.currency, required: false },
    due_date: COMMON_RULES.date,
    notes: COMMON_RULES.notes
  }

  // 字段更新函数
  const updateField = (field: string, value: any) => {
    // 特殊处理：当总金额或定金百分比变化时，自动计算定金金额
    if (field === 'total_amount' || field === 'deposit_percentage') {
      const totalAmount = field === 'total_amount' ? parseFloat(value) || 0 : parseFloat(formData.total_amount) || 0
      const percentage = field === 'deposit_percentage' ? parseFloat(value) || 0 : parseFloat(formData.deposit_percentage) || 0
      const depositAmount = (totalAmount * percentage / 100).toFixed(2)
      setFormData(prev => ({
        ...prev,
        [field]: value,
        deposit_amount: depositAmount
      }))
    } else {
      setFormData(prev => ({ ...prev, [field]: value }))
    }

    // 如果字段已被触摸，立即验证
    if (touched[field]) {
      const rule = validationRules[field]
      if (rule) {
        const error = validateField(value, rule)
        setErrors(prev => ({ ...prev, [field]: error || '' }))
      }
    }
  }

  // 字段失焦处理
  const handleFieldBlur = (field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }))

    const rule = validationRules[field]
    if (rule && field in formData) {
      const error = validateField(formData[field as keyof typeof formData], rule)
      setErrors(prev => ({ ...prev, [field]: error || '' }))
    }
  }

  // Initialize form data when modal opens
  useEffect(() => {
    if (isOpen) {
      if (editingInvoice) {
        // Editing mode
        setFormData({
          client_id: editingInvoice.client_id || '',
          treatment_date: editingInvoice.treatment_date || '',
          total_amount: editingInvoice.total_amount?.toString() || '',
          deposit_percentage: editingInvoice.deposit_percentage?.toString() || '50',
          deposit_amount: editingInvoice.deposit_amount?.toString() || '',
          consultation_fee_waived: editingInvoice.consultation_fee_waived || false,
          original_consultation_fee: editingInvoice.original_consultation_fee?.toString() || '',
          due_date: editingInvoice.due_date || '',
          notes: editingInvoice.notes || ''
        })
      } else {
        // Creation mode - set default due date (7 days from now)
        const defaultDueDate = new Date()
        defaultDueDate.setDate(defaultDueDate.getDate() + 7)
        
        setFormData({
          client_id: '',
          treatment_date: new Date().toISOString().split('T')[0],
          total_amount: '',
          deposit_percentage: '50',
          deposit_amount: '',
          consultation_fee_waived: false,
          original_consultation_fee: '',
          due_date: defaultDueDate.toISOString().split('T')[0],
          notes: ''
        })
      }
      
      fetchClients()
    }
  }, [isOpen, editingInvoice])

  // Calculate deposit amount when total amount or percentage changes
  useEffect(() => {
    if (formData.total_amount && formData.deposit_percentage) {
      const totalAmount = parseFloat(formData.total_amount)
      const percentage = parseFloat(formData.deposit_percentage)
      if (!isNaN(totalAmount) && !isNaN(percentage)) {
        const depositAmount = Math.round((totalAmount * percentage) / 100 * 100) / 100
        setFormData(prev => ({ ...prev, deposit_amount: depositAmount.toString() }))
      }
    }
  }, [formData.total_amount, formData.deposit_percentage])

  const fetchClients = async () => {
    try {
      const response = await fetch('/api/clients')
      const data = await response.json()
      setClients(data.clients || [])
    } catch (error) {
      console.error('Error fetching clients:', error)
    }
  }

  const validateFormData = () => {
    const result = validateForm(formData, validationRules)
    setErrors(result.errors)
    setTouched(Object.keys(validationRules).reduce((acc, key) => ({ ...acc, [key]: true }), {}))
    return result.isValid
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateFormData()) {
      showErrorToast('请检查表单信息', '请修正标红的字段后重试')
      return
    }

    setLoading(true)

    try {
      const submitData = {
        client_id: formData.client_id,
        treatment_date: formData.treatment_date,
        total_amount: parseFloat(formData.total_amount),
        deposit_percentage: parseFloat(formData.deposit_percentage),
        deposit_amount: parseFloat(formData.deposit_amount),
        consultation_fee_waived: formData.consultation_fee_waived,
        original_consultation_fee: formData.original_consultation_fee ? parseFloat(formData.original_consultation_fee) : 0,
        due_date: formData.due_date,
        notes: formData.notes || null
      }

      const url = editingInvoice ? `/api/invoices/${editingInvoice.id}` : '/api/invoices'
      const method = editingInvoice ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(submitData)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || '操作失败')
      }

      showSuccessToast(
        editingInvoice ? '账单更新成功' : '账单创建成功',
        editingInvoice ? '账单信息已更新' : '新账单已创建'
      )
      onSuccess()
      onClose()
      resetForm()
    } catch (error) {
      console.error('Error saving invoice:', error)
      showErrorToast(
        '保存失败',
        error instanceof Error ? error.message : '保存账单失败'
      )
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    const defaultDueDate = new Date()
    defaultDueDate.setDate(defaultDueDate.getDate() + 7)
    
    setFormData({
      client_id: '',
      treatment_date: new Date().toISOString().split('T')[0],
      total_amount: '',
      deposit_percentage: '50',
      deposit_amount: '',
      consultation_fee_waived: false,
      original_consultation_fee: '',
      due_date: defaultDueDate.toISOString().split('T')[0],
      notes: ''
    })
  }

  const handleClose = () => {
    onClose()
    resetForm()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Receipt className="h-5 w-5" />
            {editingInvoice ? '编辑账单' : '新建账单'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Client Selection */}
          <EnhancedSelect
            label="客户"
            icon={<User className="h-4 w-4" />}
            placeholder="选择客户"
            value={formData.client_id}
            onChange={(value) => updateField('client_id', value)}
            onBlur={() => handleFieldBlur('client_id')}
            error={errors.client_id}
            touched={touched.client_id}
            required
            validation={validationRules.client_id}
            options={clients.map(client => ({
              value: client.id,
              label: `${client.last_name}${client.first_name} - ${client.phone}`
            }))}
          />

          {/* Treatment Date */}
          <div className="space-y-2">
            <Label className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              治疗日期
              <span className="text-red-500">*</span>
            </Label>
            <Input
              type="date"
              value={formData.treatment_date}
              onChange={(e) => updateField('treatment_date', e.target.value)}
              onBlur={() => handleFieldBlur('treatment_date')}
              className={cn(
                "transition-colors",
                touched.treatment_date && errors.treatment_date && "border-red-500 focus:border-red-500"
              )}
            />
            {touched.treatment_date && errors.treatment_date && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.treatment_date}
              </p>
            )}
          </div>

          {/* Total Amount */}
          <EnhancedInput
            label="总金额"
            icon={<DollarSign className="h-4 w-4" />}
            type="currency"
            placeholder="0.00"
            value={formData.total_amount}
            onChange={(value) => updateField('total_amount', value)}
            onBlur={() => handleFieldBlur('total_amount')}
            error={errors.total_amount}
            touched={touched.total_amount}
            required
            validation={validationRules.total_amount}
            helpText="输入治疗的总费用"
          />

          {/* Deposit Settings */}
          <div className="grid grid-cols-2 gap-4">
            <EnhancedSelect
              label="定金比例 (%) - 仅用于手动创建"
              icon={<Percent className="h-4 w-4" />}
              value={formData.deposit_percentage}
              onChange={(value) => updateField('deposit_percentage', value)}
              onBlur={() => handleFieldBlur('deposit_percentage')}
              error={errors.deposit_percentage}
              touched={touched.deposit_percentage}
              validation={validationRules.deposit_percentage}
              options={[
                { value: '25', label: '25%' },
                { value: '30', label: '30%' },
                { value: '50', label: '50%' },
                { value: '100', label: '100% (全款)' }
              ]}
              helpText="注意：通过预约生成的账单将使用治疗项目的固定定金金额"
            />

            <EnhancedInput
              label="定金金额"
              icon={<DollarSign className="h-4 w-4" />}
              type="currency"
              value={formData.deposit_amount}
              onChange={(value) => updateField('deposit_amount', value)}
              onBlur={() => handleFieldBlur('deposit_amount')}
              error={errors.deposit_amount}
              touched={touched.deposit_amount}
              validation={validationRules.deposit_amount}
              disabled
              className="bg-gray-50"
              helpText="根据总金额和定金比例自动计算"
            />
          </div>

          {/* Consultation Fee Settings */}
          <div className="grid grid-cols-2 gap-4">
            <EnhancedInput
              label="原始咨询费"
              icon={<DollarSign className="h-4 w-4" />}
              type="currency"
              placeholder="0.00"
              value={formData.original_consultation_fee}
              onChange={(value) => updateField('original_consultation_fee', value)}
              onBlur={() => handleFieldBlur('original_consultation_fee')}
              error={errors.original_consultation_fee}
              touched={touched.original_consultation_fee}
              validation={validationRules.original_consultation_fee}
              helpText="如果包含咨询费，请输入原始金额"
            />

            <div className="space-y-2">
              <Label>咨询费减免</Label>
              <div className="flex items-center space-x-2 pt-2">
                <input
                  type="checkbox"
                  id="consultation_fee_waived"
                  checked={formData.consultation_fee_waived}
                  onChange={(e) => updateField('consultation_fee_waived', e.target.checked)}
                  className="rounded"
                />
                <label htmlFor="consultation_fee_waived" className="text-sm">
                  咨询费已减免
                </label>
              </div>
              <p className="text-xs text-muted-foreground">
                如果客户进行治疗，可减免咨询费
              </p>
            </div>
          </div>

          {/* Due Date */}
          <div className="space-y-2">
            <Label className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              到期日期
              <span className="text-red-500">*</span>
            </Label>
            <Input
              type="date"
              value={formData.due_date}
              onChange={(e) => updateField('due_date', e.target.value)}
              onBlur={() => handleFieldBlur('due_date')}
              className={cn(
                "transition-colors",
                touched.due_date && errors.due_date && "border-red-500 focus:border-red-500"
              )}
            />
            {touched.due_date && errors.due_date && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.due_date}
              </p>
            )}
          </div>

          {/* Notes */}
          <EnhancedTextarea
            label="备注"
            icon={<FileText className="h-4 w-4" />}
            placeholder="账单相关备注信息..."
            value={formData.notes}
            onChange={(value) => updateField('notes', value)}
            onBlur={() => handleFieldBlur('notes')}
            error={errors.notes}
            touched={touched.notes}
            validation={validationRules.notes}
            rows={3}
            maxLength={1000}
          />

          {/* Summary */}
          {formData.total_amount && formData.deposit_amount && (
            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <h4 className="font-medium">账单摘要</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">总金额:</span>
                  <span className="ml-2 font-medium">${formData.total_amount}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">定金:</span>
                  <span className="ml-2 font-medium">${formData.deposit_amount}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">余额:</span>
                  <span className="ml-2 font-medium">
                    ${(parseFloat(formData.total_amount) - parseFloat(formData.deposit_amount)).toFixed(2)}
                  </span>
                </div>
                <div>
                  <span className="text-muted-foreground">到期日:</span>
                  <span className="ml-2 font-medium">{formData.due_date}</span>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? '保存中...' : (editingInvoice ? '更新账单' : '创建账单')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
