'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Calendar, DollarSign, FileText, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { ClientSearchSelect } from '@/components/ui/client-search-select'
import { TreatmentSearchSelect } from '@/components/ui/treatment-search-select'
import { AppointmentTypeSelect } from '@/components/ui/appointment-type-select'
import ClientModal from '@/components/modals/ClientModal'
import AppointmentConflictChecker from '@/components/appointment/AppointmentConflictChecker'
import { showSuccessToast, showErrorToast, showLoadingToast } from '@/lib/toast-utils'
import { EnhancedInput, <PERSON>hanced<PERSON>ex<PERSON>ea } from '@/components/ui/enhanced-input'
import { validateF<PERSON>, validateField, COMMON_RULES, ValidationRule } from '@/lib/form-validation'



interface Treatment {
  id: string
  name: string
  name_chinese: string
  default_price: number
  fixed_deposit_amount: number
  consultation_fee: number
  duration_minutes: number
  requires_consultation: boolean
}

interface AppointmentModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  selectedDate?: Date
  selectedTime?: { start: Date; end: Date } | null
  editingAppointment?: any
}

export default function AppointmentModal({
  isOpen,
  onClose,
  onSuccess,
  selectedDate,
  selectedTime,
  editingAppointment
}: AppointmentModalProps) {
  const [selectedTreatment, setSelectedTreatment] = useState<Treatment | null>(null)
  const [loading, setLoading] = useState(false)
  const [isClientModalOpen, setIsClientModalOpen] = useState(false)
  const [clientRefreshTrigger, setClientRefreshTrigger] = useState(0)
  const [hasConflicts, setHasConflicts] = useState(false)
  const [conflictingAppointments, setConflictingAppointments] = useState<any[]>([])
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})
  const [formData, setFormData] = useState({
    client_id: '',
    treatment_id: '',
    appointment_date: '',
    start_time: '',
    end_time: '',
    appointment_type: 'treatment',
    custom_price: '',
    notes: '',
    staff_notes: ''
  })

  // 验证规则
  const validationRules: Record<string, ValidationRule> = {
    client_id: { required: true, custom: (value) => !value ? '请选择客户' : null },
    treatment_id: { required: true, custom: (value) => !value ? '请选择治疗项目' : null },
    appointment_date: COMMON_RULES.date,
    start_time: COMMON_RULES.time,
    end_time: COMMON_RULES.time,
    appointment_type: { required: true },
    custom_price: { ...COMMON_RULES.currency, required: false },
    notes: COMMON_RULES.notes,
    staff_notes: COMMON_RULES.notes
  }

  // 字段更新函数
  const updateField = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    // 如果字段已被触摸，立即验证
    if (touched[field]) {
      const rule = validationRules[field]
      if (rule) {
        const error = validateField(value, rule)
        setErrors(prev => ({ ...prev, [field]: error || '' }))
      }
    }
  }

  // 字段失焦处理
  const handleFieldBlur = (field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }))

    const rule = validationRules[field]
    if (rule && field in formData) {
      const error = validateField(formData[field as keyof typeof formData], rule)
      setErrors(prev => ({ ...prev, [field]: error || '' }))
    }
  }

  // Initialize form data when modal opens
  useEffect(() => {
    if (isOpen) {
      if (editingAppointment) {
        // Editing mode
        setFormData({
          client_id: editingAppointment.client_id || '',
          treatment_id: editingAppointment.treatment_id || '',
          appointment_date: editingAppointment.appointment_date || '',
          start_time: editingAppointment.start_time || '',
          end_time: editingAppointment.end_time || '',
          appointment_type: editingAppointment.appointment_type || 'treatment',
          custom_price: editingAppointment.custom_price?.toString() || '',
          notes: editingAppointment.notes || '',
          staff_notes: editingAppointment.staff_notes || ''
        })
      } else if (selectedDate || selectedTime) {
        // Creation mode with pre-selected time
        const date = selectedDate || selectedTime?.start || new Date()
        const startTime = selectedTime?.start || new Date()
        const endTime = selectedTime?.end || new Date(startTime.getTime() + 60 * 60 * 1000) // Default 1 hour

        setFormData({
          client_id: '',
          treatment_id: '',
          appointment_date: date.toISOString().split('T')[0],
          start_time: startTime.toTimeString().slice(0, 5),
          end_time: endTime.toTimeString().slice(0, 5),
          appointment_type: 'treatment',
          custom_price: '',
          notes: '',
          staff_notes: ''
        })
      }
    }
  }, [isOpen, selectedDate, selectedTime, editingAppointment])



  // Handle new client creation
  const handleNewClient = () => {
    setIsClientModalOpen(true)
  }

  const handleClientSuccess = () => {
    // Client modal will close automatically
    // Trigger refresh of client list
    setClientRefreshTrigger(prev => prev + 1)
  }

  // Handle treatment selection
  const handleTreatmentSelect = (treatment: Treatment) => {
    setSelectedTreatment(treatment)

    // Auto-calculate end time based on treatment duration
    if (formData.start_time && treatment.duration_minutes) {
      const [hours, minutes] = formData.start_time.split(':').map(Number)
      const startTime = new Date()
      startTime.setHours(hours, minutes, 0, 0)

      const endTime = new Date(startTime.getTime() + treatment.duration_minutes * 60 * 1000)
      const endTimeString = endTime.toTimeString().slice(0, 5)

      setFormData(prev => ({
        ...prev,
        end_time: endTimeString,
        custom_price: treatment.default_price.toString()
      }))
    }
  }

  // Handle conflict detection
  const handleConflictDetected = (hasConflict: boolean, conflicts: any[]) => {
    setHasConflicts(hasConflict)
    setConflictingAppointments(conflicts)
  }

  const validateFormData = () => {
    const result = validateForm(formData, validationRules)
    setErrors(result.errors)
    setTouched(Object.keys(validationRules).reduce((acc, key) => ({ ...acc, [key]: true }), {}))
    return result.isValid
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateFormData()) {
      showErrorToast('请检查表单信息', '请修正标红的字段后重试')
      return
    }

    // Check for conflicts before submitting (unless user explicitly wants to proceed)
    if (hasConflicts && conflictingAppointments.length > 0) {
      const proceed = window.confirm(
        `检测到 ${conflictingAppointments.length} 个时间冲突。您确定要继续创建此预约吗？\n\n这可能会导致时间安排冲突。`
      )
      if (!proceed) {
        return
      }
    }

    setLoading(true)
    const toastId = showLoadingToast(editingAppointment ? '正在更新预约...' : '正在创建预约...')

    try {
      const submitData = {
        ...formData,
        custom_price: formData.custom_price ? parseFloat(formData.custom_price) : null
      }

      const url = editingAppointment ? `/api/appointments/${editingAppointment.id}` : '/api/appointments'
      const method = editingAppointment ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(submitData)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || '操作失败')
      }

      showSuccessToast(
        editingAppointment ? '预约更新成功' : '预约创建成功',
        `${formData.appointment_date} ${formData.start_time} 的预约已保存`
      )
      onSuccess()
      onClose()
      resetForm()
    } catch (error) {
      console.error('Error saving appointment:', error)
      showErrorToast(
        editingAppointment ? '更新预约失败' : '创建预约失败',
        error instanceof Error ? error.message : '请稍后重试或联系管理员'
      )
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setFormData({
      client_id: '',
      treatment_id: '',
      appointment_date: '',
      start_time: '',
      end_time: '',
      appointment_type: 'treatment',
      custom_price: '',
      notes: '',
      staff_notes: ''
    })
  }

  const handleClose = () => {
    onClose()
    resetForm()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {editingAppointment ? '编辑预约' : '新建预约'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Client Selection */}
          <ClientSearchSelect
            value={formData.client_id}
            onValueChange={(value) => setFormData(prev => ({ ...prev, client_id: value }))}
            onNewClient={handleNewClient}
            refreshTrigger={clientRefreshTrigger}
          />

          {/* Treatment Selection */}
          <TreatmentSearchSelect
            value={formData.treatment_id}
            onValueChange={(value) => setFormData(prev => ({ ...prev, treatment_id: value }))}
            onTreatmentSelect={handleTreatmentSelect}
          />

          {/* Date and Time */}
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label className="flex items-center gap-1">
                预约日期
                <span className="text-red-500">*</span>
              </Label>
              <Input
                type="date"
                value={formData.appointment_date}
                onChange={(e) => updateField('appointment_date', e.target.value)}
                onBlur={() => handleFieldBlur('appointment_date')}
                className={cn(
                  "transition-colors",
                  touched.appointment_date && errors.appointment_date && "border-red-500 focus:border-red-500"
                )}
              />
              {touched.appointment_date && errors.appointment_date && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.appointment_date}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label className="flex items-center gap-1">
                开始时间
                <span className="text-red-500">*</span>
              </Label>
              <Input
                type="time"
                value={formData.start_time}
                onChange={(e) => updateField('start_time', e.target.value)}
                onBlur={() => handleFieldBlur('start_time')}
                className={cn(
                  "transition-colors",
                  touched.start_time && errors.start_time && "border-red-500 focus:border-red-500"
                )}
              />
              {touched.start_time && errors.start_time && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.start_time}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label className="flex items-center gap-1">
                结束时间
                <span className="text-red-500">*</span>
              </Label>
              <Input
                type="time"
                value={formData.end_time}
                onChange={(e) => updateField('end_time', e.target.value)}
                onBlur={() => handleFieldBlur('end_time')}
                className={cn(
                  "transition-colors",
                  touched.end_time && errors.end_time && "border-red-500 focus:border-red-500"
                )}
              />
              {touched.end_time && errors.end_time && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.end_time}
                </p>
              )}
            </div>
          </div>

          {/* Conflict Detection */}
          {formData.appointment_date && formData.start_time && formData.end_time && (
            <AppointmentConflictChecker
              appointmentDate={formData.appointment_date}
              startTime={formData.start_time}
              endTime={formData.end_time}
              excludeAppointmentId={editingAppointment?.id}
              onConflictDetected={handleConflictDetected}
            />
          )}

          {/* Appointment Type */}
          <AppointmentTypeSelect
            value={formData.appointment_type}
            onValueChange={(value) => setFormData(prev => ({ ...prev, appointment_type: value }))}
          />

          {/* Fee Information */}
          {selectedTreatment && (
            <div className="bg-blue-50 p-4 rounded-lg space-y-2">
              <h4 className="font-medium text-blue-900">费用信息</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">治疗价格:</span>
                  <span className="ml-2 font-medium">¥{selectedTreatment.default_price}</span>
                </div>
                {formData.appointment_type === 'consultation' && selectedTreatment.consultation_fee > 0 && (
                  <div>
                    <span className="text-gray-600">咨询费:</span>
                    <span className="ml-2 font-medium text-orange-600">¥{selectedTreatment.consultation_fee}</span>
                  </div>
                )}
                {formData.appointment_type === 'treatment' && selectedTreatment.fixed_deposit_amount > 0 && (
                  <div>
                    <span className="text-gray-600">需付定金:</span>
                    <span className="ml-2 font-medium text-green-600">¥{selectedTreatment.fixed_deposit_amount}</span>
                  </div>
                )}
              </div>
              {formData.appointment_type === 'consultation' && selectedTreatment.consultation_fee > 0 && (
                <p className="text-xs text-blue-700">
                  💡 如果后续进行治疗，咨询费将自动减免
                </p>
              )}
              {formData.appointment_type === 'treatment' && (
                <p className="text-xs text-blue-700">
                  💡 同一天多项治疗只收取一个定金（取最高金额）
                </p>
              )}
            </div>
          )}

          {/* Custom Price */}
          <EnhancedInput
            label="自定义价格 (可选)"
            icon={<DollarSign className="h-4 w-4" />}
            type="currency"
            placeholder="留空使用默认价格"
            value={formData.custom_price}
            onChange={(value) => updateField('custom_price', value)}
            onBlur={() => handleFieldBlur('custom_price')}
            error={errors.custom_price}
            touched={touched.custom_price}
            validation={validationRules.custom_price}
            helpText="输入自定义价格，留空则使用治疗项目的默认价格"
          />

          {/* Notes */}
          <EnhancedTextarea
            label="客户备注"
            icon={<FileText className="h-4 w-4" />}
            placeholder="客户相关备注信息..."
            value={formData.notes}
            onChange={(value) => updateField('notes', value)}
            onBlur={() => handleFieldBlur('notes')}
            error={errors.notes}
            touched={touched.notes}
            validation={validationRules.notes}
            rows={2}
            maxLength={1000}
          />

          <EnhancedTextarea
            label="内部备注"
            icon={<FileText className="h-4 w-4" />}
            placeholder="内部工作备注..."
            value={formData.staff_notes}
            onChange={(value) => updateField('staff_notes', value)}
            onBlur={() => handleFieldBlur('staff_notes')}
            error={errors.staff_notes}
            touched={touched.staff_notes}
            validation={validationRules.staff_notes}
            rows={2}
            maxLength={1000}
          />

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              取消
            </Button>
            <Button
              type="submit"
              disabled={loading}
              variant={hasConflicts ? "destructive" : "default"}
              className={hasConflicts ? "bg-orange-600 hover:bg-orange-700" : ""}
            >
              {loading ? '保存中...' : (
                hasConflicts ?
                  `⚠️ 强制${editingAppointment ? '更新' : '创建'}预约` :
                  (editingAppointment ? '更新预约' : '创建预约')
              )}
            </Button>
          </div>
        </form>
      </DialogContent>

      {/* Client Modal for creating new clients */}
      <ClientModal
        isOpen={isClientModalOpen}
        onClose={() => setIsClientModalOpen(false)}
        onSuccess={handleClientSuccess}
        mode="create"
      />
    </Dialog>
  )
}
