'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from '@/components/ui/popover'
import { 
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from '@/components/ui/command'
import { Check, ChevronsUpDown, Stethoscope, Clock, DollarSign } from 'lucide-react'
import { cn } from '@/lib/utils'
import { formatCurrency } from '@/lib/currency'

interface Treatment {
  id: string
  name: string
  name_chinese: string
  default_price: number
  fixed_deposit_amount: number
  consultation_fee: number
  duration_minutes: number
  requires_consultation: boolean
  category?: string
}

interface TreatmentSearchSelectProps {
  value?: string
  onValueChange: (value: string) => void
  onTreatmentSelect?: (treatment: Treatment) => void
  placeholder?: string
  disabled?: boolean
}

export function TreatmentSearchSelect({
  value,
  onValueChange,
  onTreatmentSelect,
  placeholder = "搜索或选择治疗项目",
  disabled = false
}: TreatmentSearchSelectProps) {
  const [open, setOpen] = useState(false)
  const [treatments, setTreatments] = useState<Treatment[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  // 获取治疗项目列表
  const fetchTreatments = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/treatments')
      if (!response.ok) throw new Error('Failed to fetch treatments')
      const data = await response.json()
      setTreatments(data.treatments || [])
    } catch (error) {
      console.error('Error fetching treatments:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (open) {
      fetchTreatments()
    }
  }, [open])

  // 过滤治疗项目
  const filteredTreatments = treatments.filter(treatment => {
    if (!searchQuery) return true
    const query = searchQuery.toLowerCase()
    return (
      treatment.name_chinese.toLowerCase().includes(query) ||
      treatment.name.toLowerCase().includes(query) ||
      (treatment.category && treatment.category.toLowerCase().includes(query))
    )
  })

  // 按分类分组
  const groupedTreatments = filteredTreatments.reduce((groups, treatment) => {
    const category = treatment.category || '其他'
    if (!groups[category]) {
      groups[category] = []
    }
    groups[category].push(treatment)
    return groups
  }, {} as Record<string, Treatment[]>)

  // 获取选中治疗项目的显示文本
  const selectedTreatment = treatments.find(treatment => treatment.id === value)
  const displayText = selectedTreatment
    ? `${selectedTreatment.name_chinese} - ${formatCurrency(selectedTreatment.default_price)}`
    : placeholder

  const handleSelect = (treatmentId: string) => {
    const treatment = treatments.find(t => t.id === treatmentId)
    onValueChange(treatmentId === value ? "" : treatmentId)
    if (treatment && onTreatmentSelect) {
      onTreatmentSelect(treatment)
    }
    setOpen(false)
  }

  return (
    <div className="space-y-2">
      <Label className="flex items-center gap-2">
        <Stethoscope className="h-4 w-4" />
        治疗项目 *
      </Label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            disabled={disabled}
          >
            <span className={cn(
              "truncate",
              !selectedTreatment && "text-muted-foreground"
            )}>
              {displayText}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command className="w-full">
            <CommandInput
              placeholder="搜索治疗项目..."
              value={searchQuery}
              onValueChange={setSearchQuery}
            />
            <CommandList>
              <CommandEmpty>
                <div className="text-center py-6">
                  <Stethoscope className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">
                    没有找到匹配的治疗项目
                  </p>
                </div>
              </CommandEmpty>
              
              {Object.entries(groupedTreatments).map(([category, categoryTreatments]) => (
                <CommandGroup key={category} heading={category}>
                  {categoryTreatments.map((treatment) => (
                    <CommandItem
                      key={treatment.id}
                      value={treatment.id}
                      onSelect={() => handleSelect(treatment.id)}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          value === treatment.id ? "opacity-100" : "opacity-0"
                        )}
                      />
                      <div className="flex-1">
                        <div className="font-medium">
                          {treatment.name_chinese}
                        </div>
                        <div className="text-sm text-muted-foreground flex items-center gap-4">
                          <span className="flex items-center gap-1">
                            <DollarSign className="h-3 w-3" />
                            {formatCurrency(treatment.default_price)}
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {treatment.duration_minutes}分钟
                          </span>
                          {treatment.requires_consultation && (
                            <span className="text-orange-600 text-xs">需咨询</span>
                          )}
                        </div>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      
      {selectedTreatment && (
        <div className="bg-blue-50 p-3 rounded-lg text-sm">
          <div className="grid grid-cols-2 gap-2">
            <div>
              <span className="text-gray-600">预计时长:</span>
              <span className="ml-1 font-medium">{selectedTreatment.duration_minutes} 分钟</span>
            </div>
            <div>
              <span className="text-gray-600">默认价格:</span>
              <span className="ml-1 font-medium">{formatCurrency(selectedTreatment.default_price)}</span>
            </div>
            {selectedTreatment.fixed_deposit_amount > 0 && (
              <div>
                <span className="text-gray-600">定金:</span>
                <span className="ml-1 font-medium">{formatCurrency(selectedTreatment.fixed_deposit_amount)}</span>
              </div>
            )}
            {selectedTreatment.consultation_fee > 0 && (
              <div>
                <span className="text-gray-600">咨询费:</span>
                <span className="ml-1 font-medium">{formatCurrency(selectedTreatment.consultation_fee)}</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
