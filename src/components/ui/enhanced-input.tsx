'use client'

import React, { useState, useCallback } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { cn } from '@/lib/utils'
import { validateField, ValidationRule, formatters } from '@/lib/form-validation'
import { AlertCircle, CheckCircle, Eye, EyeOff } from 'lucide-react'

interface BaseInputProps {
  label?: string
  error?: string
  touched?: boolean
  required?: boolean
  icon?: React.ReactNode
  helpText?: string
  className?: string
  containerClassName?: string
}

interface EnhancedInputProps extends BaseInputProps {
  type?: 'text' | 'email' | 'tel' | 'password' | 'number' | 'currency' | 'percentage'
  value: string
  onChange: (value: string) => void
  onBlur?: () => void
  placeholder?: string
  disabled?: boolean
  autoComplete?: string
  maxLength?: number
  validation?: ValidationRule
  formatter?: keyof typeof formatters
}

interface EnhancedTextareaProps extends BaseInputProps {
  value: string
  onChange: (value: string) => void
  onBlur?: () => void
  placeholder?: string
  disabled?: boolean
  rows?: number
  maxLength?: number
  validation?: ValidationRule
}

interface EnhancedSelectProps extends BaseInputProps {
  value: string
  onChange: (value: string) => void
  onBlur?: () => void
  placeholder?: string
  disabled?: boolean
  options: Array<{ value: string; label: string; disabled?: boolean }>
  validation?: ValidationRule
}

/**
 * 增强的输入组件
 */
export function EnhancedInput({
  label,
  error,
  touched,
  required,
  icon,
  helpText,
  className,
  containerClassName,
  type = 'text',
  value,
  onChange,
  onBlur,
  placeholder,
  disabled,
  autoComplete,
  maxLength,
  validation,
  formatter,
  ...props
}: EnhancedInputProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [internalError, setInternalError] = useState<string>('')

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    let newValue = e.target.value

    // 应用格式化器
    if (formatter && formatters[formatter]) {
      newValue = formatters[formatter](newValue)
    }

    // 特殊类型处理
    if (type === 'currency') {
      newValue = formatters.currency(newValue)
    } else if (type === 'percentage') {
      newValue = formatters.percentage(newValue)
    } else if (type === 'tel') {
      newValue = formatters.phone(newValue)
    }

    onChange(newValue)

    // 实时验证
    if (validation && touched) {
      const validationError = validateField(newValue, validation)
      setInternalError(validationError || '')
    }
  }, [formatter, type, onChange, validation, touched])

  const handleBlur = useCallback(() => {
    if (validation) {
      const validationError = validateField(value, validation)
      setInternalError(validationError || '')
    }
    onBlur?.()
  }, [validation, value, onBlur])

  const displayError = error || internalError
  const hasError = touched && displayError
  const isValid = touched && !displayError && value

  const inputType = type === 'password' ? (showPassword ? 'text' : 'password') : 
                   type === 'currency' || type === 'percentage' ? 'text' : type

  return (
    <div className={cn('space-y-2', containerClassName)}>
      {label && (
        <Label className="flex items-center gap-1">
          {icon}
          {label}
          {required && <span className="text-red-500">*</span>}
        </Label>
      )}
      
      <div className="relative">
        <Input
          type={inputType}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          autoComplete={autoComplete}
          maxLength={maxLength}
          className={cn(
            'pr-10',
            hasError && 'border-red-500 focus:border-red-500 focus:ring-red-500',
            isValid && 'border-green-500 focus:border-green-500 focus:ring-green-500',
            className
          )}
          {...props}
        />
        
        {/* 右侧图标 */}
        <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-1">
          {type === 'password' && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="text-gray-400 hover:text-gray-600"
            >
              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
          )}
          
          {hasError && <AlertCircle className="h-4 w-4 text-red-500" />}
          {isValid && <CheckCircle className="h-4 w-4 text-green-500" />}
        </div>
      </div>

      {/* 错误消息 */}
      {hasError && (
        <p className="text-sm text-red-600 flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          {displayError}
        </p>
      )}

      {/* 帮助文本 */}
      {helpText && !hasError && (
        <p className="text-sm text-gray-500">{helpText}</p>
      )}

      {/* 字符计数 */}
      {maxLength && (
        <p className="text-xs text-gray-400 text-right">
          {value.length}/{maxLength}
        </p>
      )}
    </div>
  )
}

/**
 * 增强的文本域组件
 */
export function EnhancedTextarea({
  label,
  error,
  touched,
  required,
  icon,
  helpText,
  className,
  containerClassName,
  value,
  onChange,
  onBlur,
  placeholder,
  disabled,
  rows = 3,
  maxLength,
  validation,
  ...props
}: EnhancedTextareaProps) {
  const [internalError, setInternalError] = useState<string>('')

  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    onChange(newValue)

    // 实时验证
    if (validation && touched) {
      const validationError = validateField(newValue, validation)
      setInternalError(validationError || '')
    }
  }, [onChange, validation, touched])

  const handleBlur = useCallback(() => {
    if (validation) {
      const validationError = validateField(value, validation)
      setInternalError(validationError || '')
    }
    onBlur?.()
  }, [validation, value, onBlur])

  const displayError = error || internalError
  const hasError = touched && displayError
  const isValid = touched && !displayError && value

  return (
    <div className={cn('space-y-2', containerClassName)}>
      {label && (
        <Label className="flex items-center gap-1">
          {icon}
          {label}
          {required && <span className="text-red-500">*</span>}
        </Label>
      )}
      
      <div className="relative">
        <Textarea
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          rows={rows}
          maxLength={maxLength}
          className={cn(
            hasError && 'border-red-500 focus:border-red-500 focus:ring-red-500',
            isValid && 'border-green-500 focus:border-green-500 focus:ring-green-500',
            className
          )}
          {...props}
        />
        
        {/* 状态图标 */}
        <div className="absolute right-3 top-3 flex items-center gap-1">
          {hasError && <AlertCircle className="h-4 w-4 text-red-500" />}
          {isValid && <CheckCircle className="h-4 w-4 text-green-500" />}
        </div>
      </div>

      {/* 错误消息 */}
      {hasError && (
        <p className="text-sm text-red-600 flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          {displayError}
        </p>
      )}

      {/* 帮助文本 */}
      {helpText && !hasError && (
        <p className="text-sm text-gray-500">{helpText}</p>
      )}

      {/* 字符计数 */}
      {maxLength && (
        <div className="flex justify-between text-xs text-gray-400">
          <span></span>
          <span>{value.length}/{maxLength}</span>
        </div>
      )}
    </div>
  )
}

/**
 * 增强的选择组件
 */
export function EnhancedSelect({
  label,
  error,
  touched,
  required,
  icon,
  helpText,
  className,
  containerClassName,
  value,
  onChange,
  onBlur,
  placeholder,
  disabled,
  options,
  validation,
  ...props
}: EnhancedSelectProps) {
  const [internalError, setInternalError] = useState<string>('')

  const handleValueChange = useCallback((newValue: string) => {
    onChange(newValue)

    // 实时验证
    if (validation && touched) {
      const validationError = validateField(newValue, validation)
      setInternalError(validationError || '')
    }
  }, [onChange, validation, touched])

  const displayError = error || internalError
  const hasError = touched && displayError
  const isValid = touched && !displayError && value

  return (
    <div className={cn('space-y-2', containerClassName)}>
      {label && (
        <Label className="flex items-center gap-1">
          {icon}
          {label}
          {required && <span className="text-red-500">*</span>}
        </Label>
      )}
      
      <div className="relative">
        <Select
          value={value}
          onValueChange={handleValueChange}
          disabled={disabled}
          {...props}
        >
          <SelectTrigger 
            className={cn(
              hasError && 'border-red-500 focus:border-red-500 focus:ring-red-500',
              isValid && 'border-green-500 focus:border-green-500 focus:ring-green-500',
              className
            )}
            onBlur={onBlur}
          >
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent>
            {options.map((option) => (
              <SelectItem 
                key={option.value} 
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        {/* 状态图标 */}
        <div className="absolute right-10 top-1/2 -translate-y-1/2 flex items-center gap-1">
          {hasError && <AlertCircle className="h-4 w-4 text-red-500" />}
          {isValid && <CheckCircle className="h-4 w-4 text-green-500" />}
        </div>
      </div>

      {/* 错误消息 */}
      {hasError && (
        <p className="text-sm text-red-600 flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          {displayError}
        </p>
      )}

      {/* 帮助文本 */}
      {helpText && !hasError && (
        <p className="text-sm text-gray-500">{helpText}</p>
      )}
    </div>
  )
}
