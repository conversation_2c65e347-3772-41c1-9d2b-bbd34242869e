'use client'

import { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from '@/components/ui/popover'
import { 
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator
} from '@/components/ui/command'
import { Check, ChevronsUpDown, Plus, User } from 'lucide-react'
import { cn } from '@/lib/utils'

interface Client {
  id: string
  first_name: string
  last_name: string
  phone: string
  email?: string
}

interface ClientSearchSelectProps {
  value?: string
  onValueChange: (value: string) => void
  onNewClient: () => void
  placeholder?: string
  disabled?: boolean
  refreshTrigger?: number // 用于触发刷新的计数器
}

export function ClientSearchSelect({
  value,
  onValueChange,
  onNewClient,
  placeholder = "搜索或选择客户",
  disabled = false,
  refreshTrigger = 0
}: ClientSearchSelectProps) {
  const [open, setOpen] = useState(false)
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  // 获取客户列表
  const fetchClients = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/clients')
      if (!response.ok) throw new Error('Failed to fetch clients')
      const data = await response.json()

      // 去重处理，基于客户ID
      const uniqueClients = (data.clients || []).filter((client: Client, index: number, self: Client[]) =>
        index === self.findIndex(c => c.id === client.id)
      )

      setClients(uniqueClients)
    } catch (error) {
      console.error('Error fetching clients:', error)
      setClients([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (open) {
      fetchClients()
    }
  }, [open])

  // 当refreshTrigger变化时刷新客户列表
  useEffect(() => {
    if (refreshTrigger > 0) {
      fetchClients()
    }
  }, [refreshTrigger])

  // 过滤客户
  const filteredClients = clients.filter(client => {
    if (!searchQuery) return true
    const query = searchQuery.toLowerCase()
    const fullName = `${client.last_name}${client.first_name}`.toLowerCase()
    return (
      fullName.includes(query) ||
      client.phone.includes(query) ||
      (client.email && client.email.toLowerCase().includes(query))
    )
  })

  // 获取选中客户的显示文本
  const selectedClient = clients.find(client => client.id === value)
  const displayText = selectedClient 
    ? `${selectedClient.last_name}${selectedClient.first_name} - ${selectedClient.phone}`
    : placeholder

  return (
    <div className="space-y-2">
      <Label className="flex items-center gap-2">
        <User className="h-4 w-4" />
        客户 *
      </Label>
      <div className="flex gap-2">
        <div className="flex-1">
        <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="flex-1 justify-between"
            disabled={disabled}
          >
            <span className={cn(
              "truncate",
              !selectedClient && "text-muted-foreground"
            )}>
              {displayText}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command className="w-full">
            <CommandInput
              placeholder="搜索客户姓名、电话或邮箱..."
              value={searchQuery}
              onValueChange={setSearchQuery}
              className="h-9"
            />
            <CommandList>
              <CommandEmpty>
                <div className="text-center py-6">
                  <User className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground mb-3">
                    {searchQuery ? '没有找到匹配的客户' : '没有客户记录'}
                  </p>
                  <Button 
                    size="sm" 
                    onClick={() => {
                      setOpen(false)
                      onNewClient()
                    }}
                    className="w-full"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    新建客户
                  </Button>
                </div>
              </CommandEmpty>
              
              {filteredClients.length > 0 && (
                <>
                  <CommandGroup heading={`找到 ${filteredClients.length} 位客户`}>
                    {filteredClients.map((client) => (
                      <CommandItem
                        key={client.id}
                        value={client.id}
                        onSelect={(currentValue) => {
                          onValueChange(currentValue === value ? "" : currentValue)
                          setOpen(false)
                          setSearchQuery('')
                        }}
                        className="cursor-pointer"
                      >
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4 text-primary",
                            value === client.id ? "opacity-100" : "opacity-0"
                          )}
                        />
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-sm truncate">
                            {client.last_name}{client.first_name}
                          </div>
                          <div className="text-xs text-muted-foreground truncate">
                            {client.phone}
                            {client.email && ` • ${client.email}`}
                          </div>
                        </div>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                  <CommandSeparator />
                </>
              )}
              
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      </div>
      <Button
        variant="outline"
        size="icon"
        onClick={onNewClient}
        disabled={disabled}
        className="shrink-0"
        title="新建客户"
      >
        <Plus className="h-4 w-4" />
      </Button>
      </div>
    </div>
  )
}
