'use client';

import * as React from 'react';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { cn } from '@/lib/utils';

interface DataTableProps extends React.ComponentProps<'div'> {
  children: React.ReactNode;
  className?: string;
}

export function DataTable({ children, className, ...props }: DataTableProps) {
  return (
    <div className={cn('data-table-container', className)} {...props}>
      <ScrollArea className="h-[600px] md:h-[600px]">
        <div className="relative">
          {children}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  );
}

interface SimpleDataTableProps {
  headers: string[];
  data: any[];
  renderRow: (item: any, index: number) => React.ReactNode;
  className?: string;
}

export function SimpleDataTable({ headers, data, renderRow, className }: SimpleDataTableProps) {
  return (
    <DataTable className={className}>
      <Table>
        <TableHeader>
          <TableRow>
            {headers.map((header, index) => (
              <TableHead key={index}>{header}</TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.length > 0 ? (
            data.map((item, index) => renderRow(item, index))
          ) : (
            <TableRow>
              <TableCell colSpan={headers.length} className="h-24 text-center">
                暂无数据
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </DataTable>
  );
}
