'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from '@/components/ui/popover'
import { 
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator
} from '@/components/ui/command'
import { Check, ChevronsUpDown, Plus, Calendar } from 'lucide-react'
import { cn } from '@/lib/utils'

interface AppointmentType {
  id: string
  name: string
  color?: string
  description?: string
}

interface AppointmentTypeSelectProps {
  value?: string
  onValueChange: (value: string) => void
  placeholder?: string
  disabled?: boolean
}

// 默认预约类型
const DEFAULT_TYPES: AppointmentType[] = [
  { id: 'consultation', name: '咨询', color: '#8b5cf6', description: '初次咨询或问诊' },
  { id: 'treatment', name: '治疗', color: '#10b981', description: '正式治疗项目' },
  { id: 'follow_up', name: '复诊', color: '#f59e0b', description: '治疗后复查' },
  { id: 'maintenance', name: '维护', color: '#6b7280', description: '定期维护保养' }
]

export function AppointmentTypeSelect({
  value,
  onValueChange,
  placeholder = "选择预约类型",
  disabled = false
}: AppointmentTypeSelectProps) {
  const [open, setOpen] = useState(false)
  const [types, setTypes] = useState<AppointmentType[]>(DEFAULT_TYPES)

  // 从localStorage加载自定义类型
  useEffect(() => {
    const savedTypes = localStorage.getItem('appointment_types')
    if (savedTypes) {
      try {
        const customTypes = JSON.parse(savedTypes)
        setTypes([...DEFAULT_TYPES, ...customTypes])
      } catch (error) {
        console.error('Error loading custom appointment types:', error)
      }
    }
  }, [])

  // 保存自定义类型到localStorage
  const saveCustomTypes = (customTypes: AppointmentType[]) => {
    try {
      localStorage.setItem('appointment_types', JSON.stringify(customTypes))
    } catch (error) {
      console.error('Error saving custom appointment types:', error)
    }
  }



  // 获取选中类型的显示文本
  const selectedType = types.find(type => type.id === value)
  const displayText = selectedType ? selectedType.name : placeholder

  return (
    <div className="space-y-2">
      <Label className="flex items-center gap-2">
        <Calendar className="h-4 w-4" />
        预约类型
      </Label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            disabled={disabled}
          >
            <span className={cn(
              "truncate flex items-center gap-2",
              !selectedType && "text-muted-foreground"
            )}>
              {selectedType && (
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: selectedType.color }}
                />
              )}
              {displayText}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command className="w-full">
            <CommandInput placeholder="搜索预约类型..." />
            <CommandList>
              <CommandEmpty>没有找到匹配的预约类型</CommandEmpty>
              
              <CommandGroup heading="预约类型">
                {types.map((type) => (
                  <CommandItem
                    key={type.id}
                    value={type.id}
                    onSelect={(currentValue) => {
                      onValueChange(currentValue === value ? "" : currentValue)
                      setOpen(false)
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === type.id ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <div 
                      className="w-3 h-3 rounded-full mr-2" 
                      style={{ backgroundColor: type.color }}
                    />
                    <div className="flex-1">
                      <div className="font-medium">{type.name}</div>
                      {type.description && (
                        <div className="text-sm text-muted-foreground">
                          {type.description}
                        </div>
                      )}
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
              
              <CommandSeparator />

              <CommandGroup>
                <CommandItem onSelect={() => setOpen(false)}>
                  <Plus className="mr-2 h-4 w-4" />
                  管理预约类型
                </CommandItem>
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
