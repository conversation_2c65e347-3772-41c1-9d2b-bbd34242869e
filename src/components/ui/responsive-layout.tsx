'use client'

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import { Menu, X } from 'lucide-react'

/**
 * 响应式断点
 */
export const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
} as const

/**
 * 获取当前屏幕尺寸
 */
export function useScreenSize() {
  const [screenSize, setScreenSize] = useState<keyof typeof breakpoints>('lg')

  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth
      if (width >= breakpoints['2xl']) {
        setScreenSize('2xl')
      } else if (width >= breakpoints.xl) {
        setScreenSize('xl')
      } else if (width >= breakpoints.lg) {
        setScreenSize('lg')
      } else if (width >= breakpoints.md) {
        setScreenSize('md')
      } else {
        setScreenSize('sm')
      }
    }

    updateScreenSize()
    window.addEventListener('resize', updateScreenSize)
    return () => window.removeEventListener('resize', updateScreenSize)
  }, [])

  return screenSize
}

/**
 * 响应式容器组件
 */
interface ResponsiveContainerProps {
  children: React.ReactNode
  className?: string
  maxWidth?: keyof typeof breakpoints | 'full'
  padding?: 'none' | 'sm' | 'md' | 'lg'
}

export function ResponsiveContainer({
  children,
  className,
  maxWidth = 'xl',
  padding = 'md'
}: ResponsiveContainerProps) {
  const paddingClasses = {
    none: '',
    sm: 'px-4 py-2',
    md: 'px-6 py-4',
    lg: 'px-8 py-6'
  }

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full'
  }

  return (
    <div className={cn(
      'mx-auto w-full',
      maxWidth !== 'full' && maxWidthClasses[maxWidth],
      paddingClasses[padding],
      className
    )}>
      {children}
    </div>
  )
}

/**
 * 响应式网格组件
 */
interface ResponsiveGridProps {
  children: React.ReactNode
  className?: string
  cols?: {
    sm?: number
    md?: number
    lg?: number
    xl?: number
    '2xl'?: number
  }
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
}

export function ResponsiveGrid({
  children,
  className,
  cols = { sm: 1, md: 2, lg: 3, xl: 4 },
  gap = 'md'
}: ResponsiveGridProps) {
  const gapClasses = {
    none: 'gap-0',
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8'
  }

  const colClasses = Object.entries(cols).map(([breakpoint, colCount]) => {
    if (breakpoint === 'sm') {
      return `grid-cols-${colCount}`
    }
    return `${breakpoint}:grid-cols-${colCount}`
  }).join(' ')

  return (
    <div className={cn(
      'grid',
      colClasses,
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  )
}

/**
 * 响应式侧边栏组件
 */
interface ResponsiveSidebarProps {
  children: React.ReactNode
  trigger?: React.ReactNode
  title?: string
  className?: string
  side?: 'left' | 'right'
  width?: 'sm' | 'md' | 'lg' | 'xl'
}

export function ResponsiveSidebar({
  children,
  trigger,
  title,
  className,
  side = 'left',
  width = 'md'
}: ResponsiveSidebarProps) {
  const screenSize = useScreenSize()
  const isMobile = screenSize === 'sm' || screenSize === 'md'

  const widthClasses = {
    sm: 'w-64',
    md: 'w-80',
    lg: 'w-96',
    xl: 'w-[28rem]'
  }

  if (isMobile) {
    return (
      <Sheet>
        <SheetTrigger asChild>
          {trigger || (
            <Button variant="outline" size="sm">
              <Menu className="h-4 w-4" />
            </Button>
          )}
        </SheetTrigger>
        <SheetContent side={side} className={cn(widthClasses[width], className)}>
          {title && (
            <div className="pb-4 border-b">
              <h2 className="text-lg font-semibold">{title}</h2>
            </div>
          )}
          <div className="pt-4">
            {children}
          </div>
        </SheetContent>
      </Sheet>
    )
  }

  return (
    <div className={cn(
      'hidden lg:block',
      widthClasses[width],
      className
    )}>
      {title && (
        <div className="pb-4 border-b mb-4">
          <h2 className="text-lg font-semibold">{title}</h2>
        </div>
      )}
      {children}
    </div>
  )
}

/**
 * 响应式卡片组件
 */
interface ResponsiveCardProps {
  children: React.ReactNode
  className?: string
  padding?: 'none' | 'sm' | 'md' | 'lg'
  shadow?: 'none' | 'sm' | 'md' | 'lg'
  rounded?: 'none' | 'sm' | 'md' | 'lg'
  border?: boolean
}

export function ResponsiveCard({
  children,
  className,
  padding = 'md',
  shadow = 'sm',
  rounded = 'md',
  border = true
}: ResponsiveCardProps) {
  const paddingClasses = {
    none: '',
    sm: 'p-3',
    md: 'p-4 sm:p-6',
    lg: 'p-6 sm:p-8'
  }

  const shadowClasses = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg'
  }

  const roundedClasses = {
    none: '',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg'
  }

  return (
    <div className={cn(
      'bg-white',
      border && 'border border-gray-200',
      paddingClasses[padding],
      shadowClasses[shadow],
      roundedClasses[rounded],
      className
    )}>
      {children}
    </div>
  )
}

/**
 * 响应式堆栈组件
 */
interface ResponsiveStackProps {
  children: React.ReactNode
  className?: string
  direction?: {
    sm?: 'row' | 'col'
    md?: 'row' | 'col'
    lg?: 'row' | 'col'
    xl?: 'row' | 'col'
  }
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  align?: 'start' | 'center' | 'end' | 'stretch'
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly'
}

export function ResponsiveStack({
  children,
  className,
  direction = { sm: 'col', lg: 'row' },
  gap = 'md',
  align = 'start',
  justify = 'start'
}: ResponsiveStackProps) {
  const gapClasses = {
    none: 'gap-0',
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8'
  }

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch'
  }

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly'
  }

  const directionClasses = Object.entries(direction).map(([breakpoint, dir]) => {
    const dirClass = dir === 'row' ? 'flex-row' : 'flex-col'
    if (breakpoint === 'sm') {
      return dirClass
    }
    return `${breakpoint}:${dirClass}`
  }).join(' ')

  return (
    <div className={cn(
      'flex',
      directionClasses,
      gapClasses[gap],
      alignClasses[align],
      justifyClasses[justify],
      className
    )}>
      {children}
    </div>
  )
}

/**
 * 响应式文本组件
 */
interface ResponsiveTextProps {
  children: React.ReactNode
  className?: string
  size?: {
    sm?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl'
    md?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl'
    lg?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl'
    xl?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl'
  }
  weight?: 'normal' | 'medium' | 'semibold' | 'bold'
  color?: 'default' | 'muted' | 'primary' | 'secondary' | 'destructive'
}

export function ResponsiveText({
  children,
  className,
  size = { sm: 'sm', lg: 'base' },
  weight = 'normal',
  color = 'default'
}: ResponsiveTextProps) {
  const weightClasses = {
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold'
  }

  const colorClasses = {
    default: 'text-foreground',
    muted: 'text-muted-foreground',
    primary: 'text-primary',
    secondary: 'text-secondary',
    destructive: 'text-destructive'
  }

  const sizeClasses = Object.entries(size).map(([breakpoint, textSize]) => {
    const sizeClass = `text-${textSize}`
    if (breakpoint === 'sm') {
      return sizeClass
    }
    return `${breakpoint}:${sizeClass}`
  }).join(' ')

  return (
    <span className={cn(
      sizeClasses,
      weightClasses[weight],
      colorClasses[color],
      className
    )}>
      {children}
    </span>
  )
}
