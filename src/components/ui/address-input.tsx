'use client'

import { useState, useEffect, useRef } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { MapPin, Search, X, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import { searchAddresses, parseGoogleAddress, type GoogleMapsPlace } from '@/lib/google-maps'

export interface AddressData {
  address_line_1: string
  address_line_2: string
  city: string
  state_province: string
  postal_code: string
  country: string
  latitude?: number
  longitude?: number
}

interface AddressInputProps {
  value: AddressData
  onChange: (address: AddressData) => void
  disabled?: boolean
  required?: boolean
  className?: string
  showMap?: boolean
}

const defaultAddress: AddressData = {
  address_line_1: '',
  address_line_2: '',
  city: '',
  state_province: '',
  postal_code: '',
  country: '美国'
}

export default function AddressInput({
  value,
  onChange,
  disabled = false,
  required = false,
  className,
  showMap = false
}: AddressInputProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<GoogleMapsPlace[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 检查是否有地址数据来决定是否展开
  useEffect(() => {
    const hasAddressData = !!(value.address_line_1 || value.city || value.state_province)
    setIsExpanded(hasAddressData)
  }, [value])

  const handleFieldChange = (field: keyof AddressData, fieldValue: string) => {
    onChange({
      ...value,
      [field]: fieldValue
    })
  }

  const handleClear = () => {
    onChange(defaultAddress)
    setIsExpanded(false)
    setSearchQuery('')
  }

  const formatDisplayAddress = () => {
    const parts = [
      value.address_line_1,
      value.address_line_2,
      value.city,
      value.state_province,
      value.postal_code
    ].filter(Boolean)
    
    return parts.length > 0 ? parts.join(', ') : ''
  }

  // Google Maps地址搜索
  const handleAddressSearch = async () => {
    if (!searchQuery.trim()) return

    setIsSearching(true)
    setShowResults(false)

    try {
      const results = await searchAddresses(searchQuery)
      setSearchResults(results)
      setShowResults(results.length > 0)
    } catch (error) {
      console.error('地址搜索失败:', error)
      // 降级到简单解析
      const parts = searchQuery.split(',').map(part => part.trim())
      if (parts.length >= 1) {
        handleFieldChange('address_line_1', parts[0])
      }
      if (parts.length >= 2) {
        handleFieldChange('city', parts[1])
      }
      if (parts.length >= 3) {
        handleFieldChange('state_province', parts[2])
      }
      setIsExpanded(true)
    } finally {
      setIsSearching(false)
    }
  }

  // 处理搜索输入变化
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setSearchQuery(query)

    // 清除之前的搜索定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current)
    }

    // 设置新的搜索定时器
    if (query.trim().length > 2) {
      searchTimeoutRef.current = setTimeout(async () => {
        setIsSearching(true)
        try {
          const results = await searchAddresses(query)
          setSearchResults(results)
          setShowResults(results.length > 0)
        } catch (error) {
          console.error('自动搜索失败:', error)
        } finally {
          setIsSearching(false)
        }
      }, 500)
    } else {
      setShowResults(false)
      setSearchResults([])
    }
  }

  // 选择搜索结果
  const handleSelectAddress = (place: GoogleMapsPlace) => {
    const parsedAddress = parseGoogleAddress(place)
    onChange(parsedAddress)
    setSearchQuery(place.formatted_address)
    setShowResults(false)
    setIsExpanded(true)
  }

  return (
    <div className={cn('space-y-4', className)}>
      <div className="space-y-2">
        <Label className="flex items-center gap-2">
          <MapPin className="h-4 w-4" />
          地址 {required && <span className="text-red-500">*</span>}
        </Label>
        
        {!isExpanded ? (
          <div className="relative">
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Input
                  placeholder="搜索地址... (如: 123 Main St, New York, NY)"
                  value={searchQuery}
                  onChange={handleSearchInputChange}
                  disabled={disabled}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault()
                      handleAddressSearch()
                    }
                    if (e.key === 'Escape') {
                      setShowResults(false)
                    }
                  }}
                  onFocus={() => {
                    if (searchResults.length > 0) {
                      setShowResults(true)
                    }
                  }}
                />
                {isSearching && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                  </div>
                )}
              </div>
              <Button
                type="button"
                variant="outline"
                onClick={handleAddressSearch}
                disabled={disabled || !searchQuery.trim() || isSearching}
              >
                <Search className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsExpanded(true)}
                disabled={disabled}
              >
                手动输入
              </Button>
            </div>

            {/* 搜索结果下拉列表 */}
            {showResults && searchResults.length > 0 && (
              <Card className="absolute top-full left-0 right-0 z-50 mt-1 max-h-60 overflow-y-auto">
                <CardContent className="p-2">
                  {searchResults.map((place) => (
                    <button
                      key={place.place_id}
                      type="button"
                      className="w-full text-left p-2 hover:bg-muted rounded-md transition-colors"
                      onClick={() => handleSelectAddress(place)}
                    >
                      <div className="flex items-start gap-2">
                        <MapPin className="h-4 w-4 mt-0.5 text-muted-foreground flex-shrink-0" />
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium truncate">
                            {place.formatted_address}
                          </div>
                        </div>
                      </div>
                    </button>
                  ))}
                </CardContent>
              </Card>
            )}
          </div>
        ) : (
          <Card>
            <CardContent className="p-4 space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">地址详情</h4>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleClear}
                  disabled={disabled}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <Label htmlFor="address_line_1">街道地址 *</Label>
                  <Input
                    id="address_line_1"
                    placeholder="如：123 Main Street"
                    value={value.address_line_1}
                    onChange={(e) => handleFieldChange('address_line_1', e.target.value)}
                    disabled={disabled}
                    required={required}
                  />
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="address_line_2">地址补充</Label>
                  <Input
                    id="address_line_2"
                    placeholder="如：Apt 5B, Suite 200等"
                    value={value.address_line_2}
                    onChange={(e) => handleFieldChange('address_line_2', e.target.value)}
                    disabled={disabled}
                  />
                </div>

                <div>
                  <Label htmlFor="city">城市 *</Label>
                  <Input
                    id="city"
                    placeholder="如：New York"
                    value={value.city}
                    onChange={(e) => handleFieldChange('city', e.target.value)}
                    disabled={disabled}
                    required={required}
                  />
                </div>

                <div>
                  <Label htmlFor="state_province">州 *</Label>
                  <Input
                    id="state_province"
                    placeholder="如：NY"
                    value={value.state_province}
                    onChange={(e) => handleFieldChange('state_province', e.target.value)}
                    disabled={disabled}
                    required={required}
                  />
                </div>

                <div>
                  <Label htmlFor="postal_code">邮政编码 *</Label>
                  <Input
                    id="postal_code"
                    placeholder="如：10001"
                    value={value.postal_code}
                    onChange={(e) => handleFieldChange('postal_code', e.target.value)}
                    disabled={disabled}
                    required={required}
                  />
                </div>

                <div>
                  <Label htmlFor="country">国家</Label>
                  <Input
                    id="country"
                    value={value.country}
                    onChange={(e) => handleFieldChange('country', e.target.value)}
                    disabled={disabled}
                  />
                </div>
              </div>
              
              {formatDisplayAddress() && (
                <div className="pt-2 border-t">
                  <Label className="text-sm text-muted-foreground">完整地址预览：</Label>
                  <p className="text-sm mt-1">{formatDisplayAddress()}</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

// 辅助函数：格式化地址显示
export function formatAddress(address: Partial<AddressData>): string {
  // 美国地址格式：Street Address, City, State ZIP
  const addressLine = [address.address_line_1, address.address_line_2].filter(Boolean).join(', ')
  const cityStateZip = [
    address.city,
    address.state_province && address.postal_code
      ? `${address.state_province} ${address.postal_code}`
      : address.state_province || address.postal_code
  ].filter(Boolean).join(', ')

  const parts = [addressLine, cityStateZip].filter(Boolean)

  if (address.country && address.country !== '美国') {
    parts.push(address.country)
  }

  return parts.join(', ')
}

// 辅助函数：检查地址是否为空
export function isAddressEmpty(address: AddressData): boolean {
  return !address.address_line_1 && !address.city && !address.state_province
}
