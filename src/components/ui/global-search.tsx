'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator
} from '@/components/ui/command'
import { 
  Search, 
  User, 
  Calendar, 
  Package, 
  DollarSign, 
  Settings,
  Clock,
  FileText,
  ChevronRight,
  Sparkles
} from 'lucide-react'

interface SearchResult {
  id: string
  type: 'client' | 'appointment' | 'treatment' | 'invoice' | 'setting' | 'page'
  title: string
  subtitle?: string
  url: string
  icon: React.ElementType
  badge?: {
    text: string
    variant: 'default' | 'secondary' | 'destructive' | 'outline'
  }
}

export function GlobalSearch() {
  const [open, setOpen] = useState(false)
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [recentSearches, setRecentSearches] = useState<SearchResult[]>([])
  const [quickActions, setQuickActions] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if ((e.key === 'k' && (e.metaKey || e.ctrlKey)) || e.key === '/') {
        e.preventDefault()
        setOpen((open) => !open)
      }
    }

    document.addEventListener('keydown', down)
    return () => document.removeEventListener('keydown', down)
  }, [])

  useEffect(() => {
    // Load recent searches from localStorage
    const savedSearches = localStorage.getItem('recentSearches')
    if (savedSearches) {
      try {
        setRecentSearches(JSON.parse(savedSearches))
      } catch (error) {
        console.error('Error parsing recent searches:', error)
      }
    }

    // Set up quick actions
    setQuickActions([
      {
        id: 'new-client',
        type: 'page',
        title: '新建客户',
        url: '/dashboard/clients/new',
        icon: User
      },
      {
        id: 'new-appointment',
        type: 'page',
        title: '新建预约',
        url: '/dashboard/calendar?action=new',
        icon: Calendar
      },
      {
        id: 'new-treatment',
        type: 'page',
        title: '新建治疗项目',
        url: '/dashboard/treatments/new',
        icon: Package
      },
      {
        id: 'new-invoice',
        type: 'page',
        title: '新建账单',
        url: '/dashboard/invoices/new',
        icon: DollarSign
      },
      {
        id: 'today-appointments',
        type: 'page',
        title: '今日预约',
        url: '/dashboard/calendar?date=today',
        icon: Clock
      },
      {
        id: 'analytics',
        type: 'page',
        title: '业务分析',
        url: '/dashboard/analytics',
        icon: Sparkles
      }
    ])
  }, [])

  const searchItems = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([])
      return
    }

    setLoading(true)
    try {
      // Here you would fetch search results from your API
      // const response = await fetch(`/api/search?q=${encodeURIComponent(searchQuery)}`)
      // const data = await response.json()
      // setResults(data.results)
      
      // Mock search results for demonstration
      const mockResults: SearchResult[] = [
        {
          id: 'client-1',
          type: 'client',
          title: '张三',
          subtitle: '13800138000',
          url: '/dashboard/clients/1',
          icon: User,
          badge: {
            text: 'VIP',
            variant: 'default'
          }
        },
        {
          id: 'client-2',
          type: 'client',
          title: '李四',
          subtitle: '13900139000',
          url: '/dashboard/clients/2',
          icon: User
        },
        {
          id: 'appointment-1',
          type: 'appointment',
          title: '张三 - 深层清洁面膜',
          subtitle: '今天 14:00-15:00',
          url: '/dashboard/calendar?appointment=1',
          icon: Calendar,
          badge: {
            text: '已确认',
            variant: 'secondary'
          }
        },
        {
          id: 'treatment-1',
          type: 'treatment',
          title: '深层清洁面膜',
          subtitle: '¥200',
          url: '/dashboard/treatments?id=1',
          icon: Package
        },
        {
          id: 'invoice-1',
          type: 'invoice',
          title: '账单 #INV-2023-001',
          subtitle: '张三 - ¥500',
          url: '/dashboard/invoices/1',
          icon: DollarSign,
          badge: {
            text: '已支付',
            variant: 'default'
          }
        }
      ]
      
      // Filter mock results based on query
      const filteredResults = mockResults.filter(
        result => 
          result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (result.subtitle && result.subtitle.toLowerCase().includes(searchQuery.toLowerCase()))
      )
      
      setResults(filteredResults)
    } catch (error) {
      console.error('Error searching:', error)
      setResults([])
    } finally {
      setLoading(false)
    }
  }

  const handleSelect = (item: SearchResult) => {
    // Add to recent searches
    const newRecentSearches = [
      item,
      ...recentSearches.filter(search => search.id !== item.id).slice(0, 4)
    ]
    setRecentSearches(newRecentSearches)
    localStorage.setItem('recentSearches', JSON.stringify(newRecentSearches))
    
    // Navigate to the URL
    router.push(item.url)
    setOpen(false)
  }

  const handleQueryChange = (value: string) => {
    setQuery(value)
    searchItems(value)
  }

  return (
    <>
      <Button
        variant="outline"
        className="relative h-9 w-9 p-0 xl:h-10 xl:w-60 xl:justify-start xl:px-3 xl:py-2"
        onClick={() => setOpen(true)}
      >
        <Search className="h-4 w-4 xl:mr-2" />
        <span className="hidden xl:inline-flex">搜索...</span>
        <kbd className="pointer-events-none absolute right-1.5 top-1.5 hidden h-6 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-xs font-medium opacity-100 xl:flex">
          <span className="text-xs">⌘</span>K
        </kbd>
      </Button>
      <CommandDialog open={open} onOpenChange={setOpen}>
        <CommandInput
          ref={inputRef}
          placeholder="搜索客户、预约、治疗项目..."
          value={query}
          onValueChange={handleQueryChange}
        />
        <CommandList>
          {loading && (
            <div className="flex items-center justify-center p-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              <span className="ml-2 text-sm text-muted-foreground">搜索中...</span>
            </div>
          )}
          
          {!query && (
            <>
              <CommandGroup heading="快捷操作">
                <div className="grid grid-cols-2 gap-2 p-2">
                  {quickActions.map((action) => {
                    const Icon = action.icon
                    return (
                      <CommandItem
                        key={action.id}
                        value={action.title}
                        onSelect={() => handleSelect(action)}
                        className="flex items-center gap-2 p-2 cursor-pointer"
                      >
                        <div className="flex items-center gap-2 flex-1">
                          <div className="p-1 rounded-md bg-muted">
                            <Icon className="h-4 w-4" />
                          </div>
                          <span>{action.title}</span>
                        </div>
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      </CommandItem>
                    )
                  })}
                </div>
              </CommandGroup>
              
              {recentSearches.length > 0 && (
                <>
                  <CommandSeparator />
                  <CommandGroup heading="最近搜索">
                    {recentSearches.map((item) => {
                      const Icon = item.icon
                      return (
                        <CommandItem
                          key={item.id}
                          value={item.title}
                          onSelect={() => handleSelect(item)}
                        >
                          <div className="flex items-center gap-2 flex-1">
                            <Icon className="h-4 w-4 text-muted-foreground" />
                            <span>{item.title}</span>
                            {item.subtitle && (
                              <span className="text-xs text-muted-foreground">
                                {item.subtitle}
                              </span>
                            )}
                          </div>
                          {item.badge && (
                            <Badge variant={item.badge.variant}>
                              {item.badge.text}
                            </Badge>
                          )}
                        </CommandItem>
                      )
                    })}
                  </CommandGroup>
                </>
              )}
            </>
          )}
          
          {query && results.length > 0 && (
            <>
              <CommandGroup heading="客户">
                {results
                  .filter(result => result.type === 'client')
                  .map((result) => {
                    const Icon = result.icon
                    return (
                      <CommandItem
                        key={result.id}
                        value={result.title}
                        onSelect={() => handleSelect(result)}
                      >
                        <Icon className="mr-2 h-4 w-4 text-muted-foreground" />
                        <div className="flex-1">
                          <span>{result.title}</span>
                          {result.subtitle && (
                            <span className="ml-2 text-xs text-muted-foreground">
                              {result.subtitle}
                            </span>
                          )}
                        </div>
                        {result.badge && (
                          <Badge variant={result.badge.variant}>
                            {result.badge.text}
                          </Badge>
                        )}
                      </CommandItem>
                    )
                  })}
              </CommandGroup>
              
              <CommandGroup heading="预约">
                {results
                  .filter(result => result.type === 'appointment')
                  .map((result) => {
                    const Icon = result.icon
                    return (
                      <CommandItem
                        key={result.id}
                        value={result.title}
                        onSelect={() => handleSelect(result)}
                      >
                        <Icon className="mr-2 h-4 w-4 text-muted-foreground" />
                        <div className="flex-1">
                          <span>{result.title}</span>
                          {result.subtitle && (
                            <span className="ml-2 text-xs text-muted-foreground">
                              {result.subtitle}
                            </span>
                          )}
                        </div>
                        {result.badge && (
                          <Badge variant={result.badge.variant}>
                            {result.badge.text}
                          </Badge>
                        )}
                      </CommandItem>
                    )
                  })}
              </CommandGroup>
              
              <CommandGroup heading="治疗项目">
                {results
                  .filter(result => result.type === 'treatment')
                  .map((result) => {
                    const Icon = result.icon
                    return (
                      <CommandItem
                        key={result.id}
                        value={result.title}
                        onSelect={() => handleSelect(result)}
                      >
                        <Icon className="mr-2 h-4 w-4 text-muted-foreground" />
                        <div className="flex-1">
                          <span>{result.title}</span>
                          {result.subtitle && (
                            <span className="ml-2 text-xs text-muted-foreground">
                              {result.subtitle}
                            </span>
                          )}
                        </div>
                      </CommandItem>
                    )
                  })}
              </CommandGroup>
              
              <CommandGroup heading="账单">
                {results
                  .filter(result => result.type === 'invoice')
                  .map((result) => {
                    const Icon = result.icon
                    return (
                      <CommandItem
                        key={result.id}
                        value={result.title}
                        onSelect={() => handleSelect(result)}
                      >
                        <Icon className="mr-2 h-4 w-4 text-muted-foreground" />
                        <div className="flex-1">
                          <span>{result.title}</span>
                          {result.subtitle && (
                            <span className="ml-2 text-xs text-muted-foreground">
                              {result.subtitle}
                            </span>
                          )}
                        </div>
                        {result.badge && (
                          <Badge variant={result.badge.variant}>
                            {result.badge.text}
                          </Badge>
                        )}
                      </CommandItem>
                    )
                  })}
              </CommandGroup>
            </>
          )}
          
          {query && results.length === 0 && !loading && (
            <CommandEmpty>
              <div className="flex flex-col items-center justify-center py-6">
                <FileText className="h-10 w-10 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">未找到匹配的结果</p>
                <p className="text-xs text-muted-foreground mt-1">
                  尝试使用不同的关键词或检查拼写
                </p>
              </div>
            </CommandEmpty>
          )}
        </CommandList>
      </CommandDialog>
    </>
  )
}
