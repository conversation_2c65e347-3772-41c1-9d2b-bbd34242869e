'use client'

import React, { useState, useCallback, useMemo } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'
import { cn } from '@/lib/utils'
import { Search, Filter, X, Calendar as CalendarIcon, SortAsc, SortDesc } from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'

export interface FilterOption {
  key: string
  label: string
  type: 'select' | 'date' | 'dateRange' | 'number' | 'text'
  options?: Array<{ value: string; label: string }>
  placeholder?: string
}

export interface SortOption {
  key: string
  label: string
  direction?: 'asc' | 'desc'
}

export interface SearchFilters {
  search: string
  filters: Record<string, any>
  sort?: SortOption
  dateRange?: {
    from?: Date
    to?: Date
  }
}

interface EnhancedSearchProps {
  placeholder?: string
  filterOptions?: FilterOption[]
  sortOptions?: SortOption[]
  onFiltersChange: (filters: SearchFilters) => void
  showDateRange?: boolean
  className?: string
}

/**
 * 增强的搜索和筛选组件
 */
export function EnhancedSearch({
  placeholder = '搜索...',
  filterOptions = [],
  sortOptions = [],
  onFiltersChange,
  showDateRange = false,
  className
}: EnhancedSearchProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState<Record<string, any>>({})
  const [sort, setSort] = useState<SortOption | undefined>()
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({})
  const [showFilters, setShowFilters] = useState(false)

  // 计算活跃的筛选器数量
  const activeFiltersCount = useMemo(() => {
    let count = 0
    Object.values(filters).forEach(value => {
      if (value && value !== '' && value !== 'all') count++
    })
    if (dateRange.from || dateRange.to) count++
    return count
  }, [filters, dateRange])

  // 更新搜索词
  const handleSearchChange = useCallback((value: string) => {
    setSearchTerm(value)
    updateFilters({ search: value })
  }, [])

  // 更新筛选器
  const handleFilterChange = useCallback((key: string, value: any) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    updateFilters({ filters: newFilters })
  }, [filters])

  // 更新排序
  const handleSortChange = useCallback((newSort: SortOption) => {
    setSort(newSort)
    updateFilters({ sort: newSort })
  }, [])

  // 更新日期范围
  const handleDateRangeChange = useCallback((range: { from?: Date; to?: Date }) => {
    setDateRange(range)
    updateFilters({ dateRange: range })
  }, [])

  // 统一更新函数
  const updateFilters = useCallback((updates: Partial<SearchFilters>) => {
    const currentFilters: SearchFilters = {
      search: searchTerm,
      filters,
      sort,
      dateRange,
      ...updates
    }
    onFiltersChange(currentFilters)
  }, [searchTerm, filters, sort, dateRange, onFiltersChange])

  // 清除所有筛选器
  const clearAllFilters = useCallback(() => {
    setSearchTerm('')
    setFilters({})
    setSort(undefined)
    setDateRange({})
    onFiltersChange({
      search: '',
      filters: {},
      sort: undefined,
      dateRange: {}
    })
  }, [onFiltersChange])

  // 清除单个筛选器
  const clearFilter = useCallback((key: string) => {
    if (key === 'search') {
      handleSearchChange('')
    } else if (key === 'dateRange') {
      handleDateRangeChange({})
    } else if (key === 'sort') {
      setSort(undefined)
      updateFilters({ sort: undefined })
    } else {
      handleFilterChange(key, '')
    }
  }, [handleSearchChange, handleDateRangeChange, handleFilterChange])

  return (
    <div className={cn('space-y-4', className)}>
      {/* 搜索栏和主要控制 */}
      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder={placeholder}
            value={searchTerm}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* 筛选器按钮 */}
        {filterOptions.length > 0 && (
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="relative"
          >
            <Filter className="h-4 w-4 mr-2" />
            筛选
            {activeFiltersCount > 0 && (
              <Badge variant="destructive" className="ml-2 h-5 w-5 p-0 text-xs">
                {activeFiltersCount}
              </Badge>
            )}
          </Button>
        )}

        {/* 排序选择 */}
        {sortOptions.length > 0 && (
          <Select
            value={sort ? `${sort.key}-${sort.direction}` : ''}
            onValueChange={(value) => {
              if (!value) {
                handleSortChange(undefined as any)
                return
              }
              const [key, direction] = value.split('-')
              const option = sortOptions.find(opt => opt.key === key)
              if (option) {
                handleSortChange({ ...option, direction: direction as 'asc' | 'desc' })
              }
            }}
          >
            <SelectTrigger className="w-40">
              <SelectValue placeholder="排序" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">默认排序</SelectItem>
              {sortOptions.map((option) => (
                <React.Fragment key={option.key}>
                  <SelectItem value={`${option.key}-asc`}>
                    <div className="flex items-center gap-2">
                      <SortAsc className="h-4 w-4" />
                      {option.label} ↑
                    </div>
                  </SelectItem>
                  <SelectItem value={`${option.key}-desc`}>
                    <div className="flex items-center gap-2">
                      <SortDesc className="h-4 w-4" />
                      {option.label} ↓
                    </div>
                  </SelectItem>
                </React.Fragment>
              ))}
            </SelectContent>
          </Select>
        )}

        {/* 日期范围选择 */}
        {showDateRange && (
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-60 justify-start text-left font-normal">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, 'yyyy-MM-dd', { locale: zhCN })} -{' '}
                      {format(dateRange.to, 'yyyy-MM-dd', { locale: zhCN })}
                    </>
                  ) : (
                    format(dateRange.from, 'yyyy-MM-dd', { locale: zhCN })
                  )
                ) : (
                  '选择日期范围'
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange.from}
                selected={{ from: dateRange.from, to: dateRange.to }}
                onSelect={(range) => handleDateRangeChange(range || {})}
                numberOfMonths={2}
                locale={zhCN}
              />
            </PopoverContent>
          </Popover>
        )}

        {/* 清除所有筛选器 */}
        {activeFiltersCount > 0 && (
          <Button variant="ghost" size="sm" onClick={clearAllFilters}>
            <X className="h-4 w-4 mr-1" />
            清除
          </Button>
        )}
      </div>

      {/* 筛选器面板 */}
      {showFilters && filterOptions.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg">
          {filterOptions.map((option) => (
            <div key={option.key} className="space-y-2">
              <label className="text-sm font-medium">{option.label}</label>
              {option.type === 'select' && option.options && (
                <Select
                  value={filters[option.key] || ''}
                  onValueChange={(value) => handleFilterChange(option.key, value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={option.placeholder || '全部'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部</SelectItem>
                    {option.options.map((opt) => (
                      <SelectItem key={opt.value} value={opt.value}>
                        {opt.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
              {option.type === 'text' && (
                <Input
                  placeholder={option.placeholder}
                  value={filters[option.key] || ''}
                  onChange={(e) => handleFilterChange(option.key, e.target.value)}
                />
              )}
              {option.type === 'number' && (
                <Input
                  type="number"
                  placeholder={option.placeholder}
                  value={filters[option.key] || ''}
                  onChange={(e) => handleFilterChange(option.key, e.target.value)}
                />
              )}
            </div>
          ))}
        </div>
      )}

      {/* 活跃筛选器标签 */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {searchTerm && (
            <Badge variant="secondary" className="gap-1">
              搜索: {searchTerm}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => clearFilter('search')}
              />
            </Badge>
          )}
          {Object.entries(filters).map(([key, value]) => {
            if (!value || value === '' || value === 'all') return null
            const option = filterOptions.find(opt => opt.key === key)
            const displayValue = option?.options?.find(opt => opt.value === value)?.label || value
            return (
              <Badge key={key} variant="secondary" className="gap-1">
                {option?.label}: {displayValue}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => clearFilter(key)}
                />
              </Badge>
            )
          })}
          {(dateRange.from || dateRange.to) && (
            <Badge variant="secondary" className="gap-1">
              日期: {dateRange.from && format(dateRange.from, 'yyyy-MM-dd', { locale: zhCN })}
              {dateRange.to && ` - ${format(dateRange.to, 'yyyy-MM-dd', { locale: zhCN })}`}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => clearFilter('dateRange')}
              />
            </Badge>
          )}
          {sort && (
            <Badge variant="secondary" className="gap-1">
              排序: {sort.label} {sort.direction === 'asc' ? '↑' : '↓'}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => clearFilter('sort')}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  )
}
