'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Plus, 
  Edit, 
  Trash2, 
  MoreHorizontal, 
  Package2, 
  DollarSign,
  Clock,
  Per<PERSON>,
  <PERSON>
} from 'lucide-react'
import { showSuccessToast, showErrorToast } from '@/lib/toast-utils'

interface Treatment {
  id: string
  name_chinese: string
  default_price: number
  duration_minutes: number
}

interface TreatmentPackageItem {
  treatment_id: string
  treatment_name: string
  quantity: number
  unit_price: number
  total_price: number
}

interface TreatmentPackage {
  id: string
  name: string
  name_chinese: string
  description?: string
  items: TreatmentPackageItem[]
  original_price: number
  package_price: number
  discount_percentage: number
  validity_days: number
  max_uses: number
  is_active: boolean
  is_featured: boolean
  created_at: string
  updated_at: string
}

interface TreatmentPackageManagerProps {
  onPackageSelect?: (packageItem: TreatmentPackage) => void
}

export default function TreatmentPackageManager({ onPackageSelect }: TreatmentPackageManagerProps) {
  const [packages, setPackages] = useState<TreatmentPackage[]>([])
  const [treatments, setTreatments] = useState<Treatment[]>([])
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingPackage, setEditingPackage] = useState<TreatmentPackage | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    name_chinese: '',
    description: '',
    items: [] as TreatmentPackageItem[],
    package_price: 0,
    validity_days: 365,
    max_uses: 1,
    is_active: true,
    is_featured: false
  })

  const fetchData = async () => {
    try {
      setLoading(true)
      
      // Fetch treatments for package creation
      // const treatmentsResponse = await fetch('/api/treatments')
      // const treatmentsData = await treatmentsResponse.json()
      // setTreatments(treatmentsData.treatments || [])
      
      // Fetch packages
      // const packagesResponse = await fetch('/api/treatment-packages')
      // const packagesData = await packagesResponse.json()
      // setPackages(packagesData.packages || [])
      
      // Mock data for demonstration
      const mockTreatments: Treatment[] = [
        { id: '1', name_chinese: '深层清洁面膜', default_price: 200, duration_minutes: 60 },
        { id: '2', name_chinese: '补水保湿护理', default_price: 300, duration_minutes: 90 },
        { id: '3', name_chinese: '抗衰老精华', default_price: 500, duration_minutes: 120 }
      ]
      
      const mockPackages: TreatmentPackage[] = [
        {
          id: '1',
          name: 'facial_basic',
          name_chinese: '基础面部护理套餐',
          description: '适合初次体验的基础护理套餐',
          items: [
            { treatment_id: '1', treatment_name: '深层清洁面膜', quantity: 1, unit_price: 200, total_price: 200 },
            { treatment_id: '2', treatment_name: '补水保湿护理', quantity: 1, unit_price: 300, total_price: 300 }
          ],
          original_price: 500,
          package_price: 400,
          discount_percentage: 20,
          validity_days: 180,
          max_uses: 2,
          is_active: true,
          is_featured: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ]
      
      setTreatments(mockTreatments)
      setPackages(mockPackages)
    } catch (error) {
      console.error('Error fetching data:', error)
      showErrorToast('加载失败', '无法加载套餐数据')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  const calculateOriginalPrice = () => {
    return formData.items.reduce((sum, item) => sum + item.total_price, 0)
  }

  const calculateDiscountPercentage = () => {
    const originalPrice = calculateOriginalPrice()
    if (originalPrice === 0) return 0
    return Math.round(((originalPrice - formData.package_price) / originalPrice) * 100)
  }

  const addTreatmentToPackage = (treatmentId: string) => {
    const treatment = treatments.find(t => t.id === treatmentId)
    if (!treatment) return

    const existingItem = formData.items.find(item => item.treatment_id === treatmentId)
    if (existingItem) {
      // Increase quantity
      setFormData(prev => ({
        ...prev,
        items: prev.items.map(item =>
          item.treatment_id === treatmentId
            ? { ...item, quantity: item.quantity + 1, total_price: (item.quantity + 1) * item.unit_price }
            : item
        )
      }))
    } else {
      // Add new item
      const newItem: TreatmentPackageItem = {
        treatment_id: treatmentId,
        treatment_name: treatment.name_chinese,
        quantity: 1,
        unit_price: treatment.default_price,
        total_price: treatment.default_price
      }
      setFormData(prev => ({
        ...prev,
        items: [...prev.items, newItem]
      }))
    }
  }

  const removeTreatmentFromPackage = (treatmentId: string) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter(item => item.treatment_id !== treatmentId)
    }))
  }

  const updateItemQuantity = (treatmentId: string, quantity: number) => {
    if (quantity <= 0) {
      removeTreatmentFromPackage(treatmentId)
      return
    }

    setFormData(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.treatment_id === treatmentId
          ? { ...item, quantity, total_price: quantity * item.unit_price }
          : item
      )
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (formData.items.length === 0) {
      showErrorToast('请添加治疗项目', '套餐至少需要包含一个治疗项目')
      return
    }

    try {
      const packageData = {
        ...formData,
        original_price: calculateOriginalPrice(),
        discount_percentage: calculateDiscountPercentage()
      }

      if (editingPackage) {
        // Update existing package
        showSuccessToast('套餐更新成功', `"${formData.name_chinese}"套餐已更新`)
      } else {
        // Create new package
        showSuccessToast('套餐创建成功', `"${formData.name_chinese}"套餐已创建`)
      }

      setIsDialogOpen(false)
      setEditingPackage(null)
      resetForm()
      fetchData()
    } catch (error: any) {
      console.error('Error saving package:', error)
      showErrorToast('保存失败', error.message)
    }
  }

  const handleEdit = (packageItem: TreatmentPackage) => {
    setEditingPackage(packageItem)
    setFormData({
      name: packageItem.name,
      name_chinese: packageItem.name_chinese,
      description: packageItem.description || '',
      items: packageItem.items,
      package_price: packageItem.package_price,
      validity_days: packageItem.validity_days,
      max_uses: packageItem.max_uses,
      is_active: packageItem.is_active,
      is_featured: packageItem.is_featured
    })
    setIsDialogOpen(true)
  }

  const handleDelete = async (packageId: string) => {
    if (!confirm('确定要删除这个套餐吗？此操作无法撤销。')) {
      return
    }

    try {
      showSuccessToast('套餐删除成功', '套餐已成功删除')
      fetchData()
    } catch (error: any) {
      console.error('Error deleting package:', error)
      showErrorToast('删除失败', error.message)
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      name_chinese: '',
      description: '',
      items: [],
      package_price: 0,
      validity_days: 365,
      max_uses: 1,
      is_active: true,
      is_featured: false
    })
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Package2 className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">加载套餐...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Package2 className="h-5 w-5" />
            治疗套餐管理
          </CardTitle>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => { resetForm(); setEditingPackage(null) }}>
                <Plus className="mr-2 h-4 w-4" />
                新建套餐
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>
                  {editingPackage ? '编辑套餐' : '新建套餐'}
                </DialogTitle>
                <DialogDescription>
                  {editingPackage ? '修改治疗套餐信息' : '创建新的治疗套餐'}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit}>
                <div className="grid gap-4 py-4">
                  {/* Basic Information */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name_chinese">套餐名称</Label>
                      <Input
                        id="name_chinese"
                        value={formData.name_chinese}
                        onChange={(e) => setFormData(prev => ({ ...prev, name_chinese: e.target.value }))}
                        placeholder="基础面部护理套餐"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="name">英文标识</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="facial_basic"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="description">套餐描述</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="套餐详细描述..."
                      rows={3}
                    />
                  </div>

                  {/* Treatment Items */}
                  <div>
                    <Label>包含的治疗项目</Label>
                    <div className="mt-2 space-y-2">
                      {formData.items.map((item) => (
                        <div key={item.treatment_id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <p className="font-medium">{item.treatment_name}</p>
                            <p className="text-sm text-muted-foreground">
                              ¥{item.unit_price} × {item.quantity} = ¥{item.total_price}
                            </p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Input
                              type="number"
                              min="1"
                              value={item.quantity}
                              onChange={(e) => updateItemQuantity(item.treatment_id, parseInt(e.target.value))}
                              className="w-20"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => removeTreatmentFromPackage(item.treatment_id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                      
                      <Select onValueChange={addTreatmentToPackage}>
                        <SelectTrigger>
                          <SelectValue placeholder="添加治疗项目" />
                        </SelectTrigger>
                        <SelectContent>
                          {treatments
                            .filter(t => !formData.items.some(item => item.treatment_id === t.id))
                            .map((treatment) => (
                              <SelectItem key={treatment.id} value={treatment.id}>
                                {treatment.name_chinese} - ¥{treatment.default_price}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Pricing */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>原价总计</Label>
                      <div className="mt-1 p-2 bg-muted rounded-md">
                        ¥{calculateOriginalPrice()}
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="package_price">套餐价格</Label>
                      <Input
                        id="package_price"
                        type="number"
                        min="0"
                        value={formData.package_price}
                        onChange={(e) => setFormData(prev => ({ ...prev, package_price: parseFloat(e.target.value) }))}
                        required
                      />
                    </div>
                  </div>

                  <div className="p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Percent className="h-4 w-4 text-green-600" />
                      <span className="text-green-800 font-medium">
                        优惠 {calculateDiscountPercentage()}% 
                        (节省 ¥{calculateOriginalPrice() - formData.package_price})
                      </span>
                    </div>
                  </div>

                  {/* Package Settings */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="validity_days">有效期(天)</Label>
                      <Input
                        id="validity_days"
                        type="number"
                        min="1"
                        value={formData.validity_days}
                        onChange={(e) => setFormData(prev => ({ ...prev, validity_days: parseInt(e.target.value) }))}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="max_uses">最大使用次数</Label>
                      <Input
                        id="max_uses"
                        type="number"
                        min="1"
                        value={formData.max_uses}
                        onChange={(e) => setFormData(prev => ({ ...prev, max_uses: parseInt(e.target.value) }))}
                        required
                      />
                    </div>
                  </div>

                  {/* Switches */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="is_active">启用套餐</Label>
                      <Switch
                        id="is_active"
                        checked={formData.is_active}
                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="is_featured">推荐套餐</Label>
                      <Switch
                        id="is_featured"
                        checked={formData.is_featured}
                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_featured: checked }))}
                      />
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit">
                    {editingPackage ? '更新套餐' : '创建套餐'}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {packages.map((packageItem) => (
            <div
              key={packageItem.id}
              className="p-4 border rounded-lg hover:bg-muted/30 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-medium">{packageItem.name_chinese}</h4>
                    {packageItem.is_featured && (
                      <Badge className="bg-yellow-100 text-yellow-800">
                        <Star className="w-3 h-3 mr-1" />
                        推荐
                      </Badge>
                    )}
                    {!packageItem.is_active && (
                      <Badge variant="outline" className="text-muted-foreground">
                        已禁用
                      </Badge>
                    )}
                  </div>
                  
                  <p className="text-sm text-muted-foreground mb-3">
                    {packageItem.description || '暂无描述'}
                  </p>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div className="flex items-center gap-1">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <span>¥{packageItem.package_price}</span>
                      <span className="text-muted-foreground line-through">¥{packageItem.original_price}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Percent className="h-4 w-4 text-green-600" />
                      <span className="text-green-600">优惠{packageItem.discount_percentage}%</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>{packageItem.validity_days}天有效</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Package2 className="h-4 w-4 text-muted-foreground" />
                      <span>{packageItem.items.length}个项目</span>
                    </div>
                  </div>
                  
                  <div className="mt-3">
                    <p className="text-xs text-muted-foreground mb-1">包含项目:</p>
                    <div className="flex flex-wrap gap-1">
                      {packageItem.items.map((item) => (
                        <Badge key={item.treatment_id} variant="outline" className="text-xs">
                          {item.treatment_name} ×{item.quantity}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleEdit(packageItem)}>
                      <Edit className="mr-2 h-4 w-4" />
                      编辑
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => handleDelete(packageItem.id)}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      删除
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          ))}
          
          {packages.length === 0 && (
            <div className="text-center py-8">
              <Package2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">暂无治疗套餐</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
