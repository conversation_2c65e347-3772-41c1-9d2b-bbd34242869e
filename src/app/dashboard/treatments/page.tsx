'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { Plus, Package, Clock, DollarSign, Edit, Eye } from 'lucide-react'
import TreatmentModal from '@/components/modals/TreatmentModal'
import { ResponsiveTable, MobileTableField } from '@/components/ui/mobile-table'
import PageContainer from '@/components/layout/page-container'
import PageHeader from '@/components/layout/page-header'
import StatsCardsGrid from '@/components/layout/stats-cards-grid'

// Treatment type based on our database schema
interface Treatment {
  id: string
  name: string
  name_chinese: string
  description: string | null
  description_chinese: string | null
  default_price: number
  fixed_deposit_amount: number
  consultation_fee: number
  duration_minutes: number
  category: string
  is_active: boolean
  requires_consultation: boolean
  created_at: string
  updated_at: string
}

export default function TreatmentsPage() {
  const [treatments, setTreatments] = useState<Treatment[]>([])
  const [loading, setLoading] = useState(true)
  const [isTreatmentModalOpen, setIsTreatmentModalOpen] = useState(false)
  const [editingTreatment, setEditingTreatment] = useState<Treatment | null>(null)
  const [modalMode, setModalMode] = useState<'view' | 'edit' | 'create'>('create')

  // Fetch treatments from API
  const fetchTreatments = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/treatments')

      if (!response.ok) throw new Error('Failed to fetch treatments')

      const data = await response.json()
      setTreatments(data.treatments)
    } catch (error) {
      console.error('Error fetching treatments:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleNewTreatment = () => {
    setEditingTreatment(null)
    setModalMode('create')
    setIsTreatmentModalOpen(true)
  }

  const handleViewTreatment = (treatment: Treatment) => {
    setEditingTreatment(treatment)
    setModalMode('view')
    setIsTreatmentModalOpen(true)
  }

  const handleEditTreatment = (treatment: Treatment) => {
    setEditingTreatment(treatment)
    setModalMode('edit')
    setIsTreatmentModalOpen(true)
  }

  const handleTreatmentSuccess = () => {
    fetchTreatments()
  }

  useEffect(() => {
    fetchTreatments()
  }, [])

  const formatPrice = (price: number) => {
    return `$${price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  }

  const formatDuration = (minutes: number) => {
    if (minutes >= 60) {
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
    }
    return `${minutes}分钟`
  }

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge className="bg-green-100 text-green-800">启用</Badge>
    ) : (
      <Badge className="bg-gray-100 text-gray-800">停用</Badge>
    )
  }

  const getConsultationBadge = (requiresConsultation: boolean) => {
    return requiresConsultation ? (
      <Badge variant="outline" className="text-blue-600 border-blue-200">需要咨询</Badge>
    ) : null
  }

  // Group treatments by category
  const treatmentsByCategory = treatments.reduce((acc, treatment) => {
    if (!acc[treatment.category]) {
      acc[treatment.category] = []
    }
    acc[treatment.category].push(treatment)
    return acc
  }, {} as Record<string, Treatment[]>)

  const categories = Object.keys(treatmentsByCategory)

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-4'>
        {/* Header */}
        <PageHeader
          title="治疗项目"
          description="管理诊所提供的治疗服务和定价"
          action={{
            label: "新建治疗项目",
            onClick: handleNewTreatment,
            icon: Plus
          }}
        />

      {/* Stats Cards */}
      <StatsCardsGrid
        cards={[
          {
            title: "总项目数",
            value: treatments.length,
            description: `启用项目 ${treatments.filter(t => t.is_active).length} 个`,
            icon: Package
          },
          {
            title: "平均价格",
            value: formatPrice(treatments.reduce((sum, t) => sum + t.default_price, 0) / treatments.length || 0),
            description: treatments.length > 0 ? `价格区间 ${formatPrice(Math.min(...treatments.map(t => t.default_price)))} - ${formatPrice(Math.max(...treatments.map(t => t.default_price)))}` : '暂无数据',
            icon: DollarSign
          },
          {
            title: "平均时长",
            value: formatDuration(Math.round(treatments.reduce((sum, t) => sum + t.duration_minutes, 0) / treatments.length || 0)),
            description: treatments.length > 0 ? `时长区间 ${formatDuration(Math.min(...treatments.map(t => t.duration_minutes)))} - ${formatDuration(Math.max(...treatments.map(t => t.duration_minutes)))}` : '暂无数据',
            icon: Clock
          },
          {
            title: "治疗类别",
            value: categories.length,
            description: `主要类别：${categories[0] || '暂无'}`,
            icon: Package
          }
        ]}
      />

      {/* Treatments List */}
      <Card>
        <CardHeader>
          <CardTitle>治疗项目列表</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-muted-foreground">加载中...</div>
            </div>
          ) : (
            <ResponsiveTable
              data={treatments}
              desktopTable={
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>治疗名称</TableHead>
                        <TableHead>类别</TableHead>
                        <TableHead>价格</TableHead>
                        <TableHead>时长</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>特殊要求</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {treatments.map((treatment) => (
                        <TableRow key={treatment.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{treatment.name_chinese}</div>
                              <div className="text-sm text-muted-foreground">{treatment.name}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{treatment.category}</Badge>
                          </TableCell>
                          <TableCell className="font-medium">
                            {formatPrice(treatment.default_price)}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                              {formatDuration(treatment.duration_minutes)}
                            </div>
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(treatment.is_active)}
                          </TableCell>
                          <TableCell>
                            {getConsultationBadge(treatment.requires_consultation)}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end space-x-2">
                              <Button variant="ghost" size="sm" onClick={() => handleViewTreatment(treatment)}>
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleEditTreatment(treatment)}>
                                <Edit className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              }
              renderMobileCard={(treatment) => (
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-medium text-base">{treatment.name_chinese}</h3>
                      <p className="text-sm text-muted-foreground">{treatment.name}</p>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <Button variant="ghost" size="sm" onClick={() => handleViewTreatment(treatment)}>
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleEditTreatment(treatment)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <MobileTableField
                      label="类别"
                      value={<Badge variant="outline" className="text-xs">{treatment.category}</Badge>}
                    />
                    <MobileTableField
                      label="价格"
                      value={<span className="font-medium">{formatPrice(treatment.default_price)}</span>}
                    />
                    <MobileTableField
                      label="时长"
                      value={
                        <div className="flex items-center">
                          <Clock className="mr-1 h-3 w-3 text-muted-foreground" />
                          {formatDuration(treatment.duration_minutes)}
                        </div>
                      }
                    />
                    <MobileTableField
                      label="状态"
                      value={getStatusBadge(treatment.is_active)}
                    />
                  </div>

                  {treatment.requires_consultation && (
                    <div className="pt-2 border-t">
                      <MobileTableField
                        label="特殊要求"
                        value={getConsultationBadge(treatment.requires_consultation)}
                      />
                    </div>
                  )}
                </div>
              )}
            />
          )}

          {!loading && treatments.length === 0 && (
            <div className="text-center py-8">
              <Package className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold">没有治疗项目</h3>
              <p className="text-muted-foreground">开始添加您的第一个治疗项目</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Treatment Modal */}
      <TreatmentModal
        isOpen={isTreatmentModalOpen}
        onClose={() => setIsTreatmentModalOpen(false)}
        onSuccess={handleTreatmentSuccess}
        editingTreatment={editingTreatment}
        mode={modalMode}
      />
      </div>
    </PageContainer>
  )
}
