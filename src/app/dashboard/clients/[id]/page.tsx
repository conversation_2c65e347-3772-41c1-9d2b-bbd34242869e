'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs'
import { 
  ArrowLeft, 
  Edit, 
  Phone, 
  Mail, 
  MapPin, 
  Calendar,
  User,
  Clock,
  DollarSign,
  FileText,
  Heart,
  AlertCircle,
  Plus
} from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import ClientModal from '@/components/modals/ClientModal'
import AppointmentModal from '@/components/modals/AppointmentModal'
import { getStatusColor, getStatusText } from '@/lib/colors'
import { showSuccessToast, showErrorToast } from '@/lib/toast-utils'
import <PERSON><PERSON>ontainer from '@/components/layout/page-container'

interface Client {
  id: string
  first_name: string
  last_name: string
  phone: string
  email: string | null
  date_of_birth: string | null
  address_line_1: string | null
  address_line_2: string | null
  city: string | null
  state_province: string | null
  postal_code: string | null
  country: string | null
  latitude: number | null
  longitude: number | null
  emergency_contact_name: string | null
  emergency_contact_phone: string | null
  notes: string | null
  status: 'active' | 'inactive' | 'archived'
  referral_source: string | null
  preferred_language: string
  created_at: string
  updated_at: string
}

interface Appointment {
  id: string
  appointment_date: string
  start_time: string
  end_time: string
  status: string
  appointment_type: string
  notes: string | null
  treatments: {
    name_chinese: string
    default_price: number
  }
}

interface Invoice {
  id: string
  invoice_number: string
  treatment_date: string
  total_amount: number
  deposit_amount: number
  status: string
  due_date: string | null
}

export default function ClientDetailPage() {
  const params = useParams()
  const router = useRouter()
  const clientId = params.id as string

  const [client, setClient] = useState<Client | null>(null)
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [loading, setLoading] = useState(true)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isAppointmentModalOpen, setIsAppointmentModalOpen] = useState(false)

  const fetchClientData = async () => {
    try {
      setLoading(true)
      
      // Fetch client details
      const clientResponse = await fetch(`/api/clients/${clientId}`)
      if (!clientResponse.ok) throw new Error('客户不存在')
      const clientData = await clientResponse.json()
      setClient(clientData.client)

      // Fetch appointments
      const appointmentsResponse = await fetch(`/api/appointments?client_id=${clientId}`)
      if (appointmentsResponse.ok) {
        const appointmentsData = await appointmentsResponse.json()
        setAppointments(appointmentsData.appointments || [])
      }

      // Fetch invoices
      const invoicesResponse = await fetch(`/api/invoices?client_id=${clientId}`)
      if (invoicesResponse.ok) {
        const invoicesData = await invoicesResponse.json()
        setInvoices(invoicesData.invoices || [])
      }
    } catch (error: any) {
      console.error('Error fetching client data:', error)
      showErrorToast('加载失败', error.message)
      router.push('/dashboard/clients')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (clientId) {
      fetchClientData()
    }
  }, [clientId])

  const handleEditSuccess = () => {
    fetchClientData()
    setIsEditModalOpen(false)
  }

  const handleAppointmentSuccess = () => {
    fetchClientData()
    setIsAppointmentModalOpen(false)
  }

  const getAppointmentStatusColor = (status: string) => {
    const colors = getStatusColor('appointment', status)
    return `${colors.bg} ${colors.text} ${colors.border} border`
  }

  const getInvoiceStatusColor = (status: string) => {
    const colors = getStatusColor('invoice', status)
    return `${colors.bg} ${colors.text} ${colors.border} border`
  }

  if (loading) {
    return (
      <PageContainer>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Clock className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">加载客户信息...</p>
          </div>
        </div>
      </PageContainer>
    )
  }

  if (!client) {
    return (
      <PageContainer>
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-xl font-semibold mb-2">客户不存在</h2>
          <p className="text-muted-foreground mb-4">请检查客户ID是否正确</p>
          <Button onClick={() => router.push('/dashboard/clients')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回客户列表
          </Button>
        </div>
      </PageContainer>
    )
  }

  return (
    <PageContainer>
      <div className="flex flex-1 flex-col space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/dashboard/clients')}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回
            </Button>
            <div>
              <h1 className="text-2xl font-bold">
                {client.last_name}{client.first_name}
              </h1>
              <p className="text-muted-foreground">客户详情</p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setIsEditModalOpen(true)}
            >
              <Edit className="mr-2 h-4 w-4" />
              编辑信息
            </Button>
            <Button onClick={() => setIsAppointmentModalOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              新建预约
            </Button>
          </div>
        </div>

        {/* Client Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-blue-100 rounded-full">
                  <Calendar className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">总预约次数</p>
                  <p className="text-2xl font-bold">{appointments.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-green-100 rounded-full">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">总消费金额</p>
                  <p className="text-2xl font-bold">
                    ¥{invoices.reduce((sum, inv) => sum + inv.total_amount, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-purple-100 rounded-full">
                  <Heart className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">客户状态</p>
                  <Badge className={getStatusColor('client', client.status).bg + ' ' + getStatusColor('client', client.status).text}>
                    {getStatusText('client', client.status)}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Information Tabs */}
        <Tabs defaultValue="info" className="space-y-4">
          <TabsList>
            <TabsTrigger value="info">基本信息</TabsTrigger>
            <TabsTrigger value="appointments">预约历史</TabsTrigger>
            <TabsTrigger value="invoices">账单记录</TabsTrigger>
            <TabsTrigger value="notes">备注记录</TabsTrigger>
          </TabsList>

          <TabsContent value="info" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Personal Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    个人信息
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">姓名</p>
                      <p className="font-medium">{client.last_name}{client.first_name}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">出生日期</p>
                      <p>{client.date_of_birth ? format(new Date(client.date_of_birth), 'yyyy年MM月dd日', { locale: zhCN }) : '未填写'}</p>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{client.phone}</span>
                    </div>
                    {client.email && (
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span>{client.email}</span>
                      </div>
                    )}
                    {(client.address_line_1 || client.city) && (
                      <div className="flex items-start gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                        <div>
                          {client.address_line_1 && <p>{client.address_line_1}</p>}
                          {client.city && <p>{client.city}, {client.state_province} {client.country}</p>}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Additional Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    其他信息
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">推荐来源</p>
                    <p>{client.referral_source || '未填写'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">首选语言</p>
                    <p>{client.preferred_language === 'zh-CN' ? '中文简体' : client.preferred_language}</p>
                  </div>
                  {client.emergency_contact_name && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">紧急联系人</p>
                      <p>{client.emergency_contact_name}</p>
                      {client.emergency_contact_phone && (
                        <p className="text-sm text-muted-foreground">{client.emergency_contact_phone}</p>
                      )}
                    </div>
                  )}
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">注册时间</p>
                    <p>{format(new Date(client.created_at), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="appointments" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>预约历史</CardTitle>
              </CardHeader>
              <CardContent>
                {appointments.length === 0 ? (
                  <div className="text-center py-8">
                    <Calendar className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                    <p className="text-muted-foreground">暂无预约记录</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {appointments.map((appointment) => (
                      <div key={appointment.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Badge className={getAppointmentStatusColor(appointment.status)}>
                              {getStatusText('appointment', appointment.status)}
                            </Badge>
                            <Badge variant="outline">
                              {appointment.appointment_type === 'consultation' ? '咨询' : '治疗'}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {appointment.appointment_date} {appointment.start_time}-{appointment.end_time}
                          </p>
                        </div>
                        <p className="font-medium">{appointment.treatments.name_chinese}</p>
                        <p className="text-sm text-muted-foreground">¥{appointment.treatments.default_price}</p>
                        {appointment.notes && (
                          <p className="text-sm text-muted-foreground mt-2">{appointment.notes}</p>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="invoices" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>账单记录</CardTitle>
              </CardHeader>
              <CardContent>
                {invoices.length === 0 ? (
                  <div className="text-center py-8">
                    <DollarSign className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                    <p className="text-muted-foreground">暂无账单记录</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {invoices.map((invoice) => (
                      <div key={invoice.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{invoice.invoice_number}</span>
                            <Badge className={getInvoiceStatusColor(invoice.status)}>
                              {getStatusText('invoice', invoice.status)}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {invoice.treatment_date}
                          </p>
                        </div>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-muted-foreground">总金额</p>
                            <p className="font-medium">¥{invoice.total_amount.toLocaleString()}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">定金</p>
                            <p className="font-medium">¥{invoice.deposit_amount.toLocaleString()}</p>
                          </div>
                        </div>
                        {invoice.due_date && (
                          <p className="text-sm text-muted-foreground mt-2">
                            到期日期: {invoice.due_date}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notes" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>客户备注</CardTitle>
              </CardHeader>
              <CardContent>
                {client.notes ? (
                  <div className="p-4 bg-muted/30 rounded-lg">
                    <p className="whitespace-pre-wrap">{client.notes}</p>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                    <p className="text-muted-foreground">暂无备注信息</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Modals */}
      <ClientModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSuccess={handleEditSuccess}
        editingClient={client}
        mode="edit"
      />

      <AppointmentModal
        isOpen={isAppointmentModalOpen}
        onClose={() => setIsAppointmentModalOpen(false)}
        onSuccess={handleAppointmentSuccess}
      />
    </PageContainer>
  )
}
