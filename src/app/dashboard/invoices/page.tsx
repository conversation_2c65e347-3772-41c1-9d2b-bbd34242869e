'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { IconSearch, IconReceipt, IconCreditCard, IconClock, IconEdit, IconTrash } from '@tabler/icons-react'
import { Plus } from 'lucide-react'
import PageContainer from '@/components/layout/page-container'
import PageHeader from '@/components/layout/page-header'

import InvoiceModal from '@/components/modals/InvoiceModal'
import { ResponsiveTable, MobileTableField } from '@/components/ui/mobile-table'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import { showSuccessToast, showErrorToast } from '@/lib/toast-utils'


interface Invoice {
  id: string
  invoice_number: string
  invoice_date: string
  treatment_date: string
  total_amount: number
  deposit_amount: number
  status: 'draft' | 'deposit_pending' | 'deposit_paid' | 'paid_in_full' | 'overdue' | 'cancelled'
  due_date: string
  clients: {
    first_name: string
    last_name: string
    phone: string
  }
  payments: Array<{
    amount: number
    payment_date: string
    payment_type: string
  }>
}

const statusConfig = {
  draft: { label: '草稿', color: 'bg-gray-100 text-gray-800' },
  deposit_pending: { label: '待付定金', color: 'bg-yellow-100 text-yellow-800' },
  deposit_paid: { label: '已付定金', color: 'bg-blue-100 text-blue-800' },
  paid_in_full: { label: '已付全款', color: 'bg-green-100 text-green-800' },
  overdue: { label: '逾期', color: 'bg-red-100 text-red-800' },
  cancelled: { label: '已取消', color: 'bg-gray-100 text-gray-800' }
}

export default function InvoicesPage() {
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  // Modal states
  const [isInvoiceModalOpen, setIsInvoiceModalOpen] = useState(false)
  const [editingInvoice, setEditingInvoice] = useState<any>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [invoiceToDelete, setInvoiceToDelete] = useState<Invoice | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  useEffect(() => {
    fetchInvoices()
  }, [])

  const fetchInvoices = async () => {
    try {
      const response = await fetch('/api/invoices')
      const data = await response.json()
      setInvoices(data.invoices || [])
    } catch (error) {
      console.error('Error fetching invoices:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleNewInvoice = () => {
    setEditingInvoice(null)
    setIsInvoiceModalOpen(true)
  }

  const handleInvoiceSuccess = () => {
    fetchInvoices()
  }

  const handleEditInvoice = (invoice: Invoice) => {
    setEditingInvoice(invoice)
    setIsInvoiceModalOpen(true)
  }

  const handleDeleteInvoice = (invoice: Invoice) => {
    setInvoiceToDelete(invoice)
    setDeleteDialogOpen(true)
  }

  const confirmDeleteInvoice = async () => {
    if (!invoiceToDelete) return

    setIsDeleting(true)
    try {
      const response = await fetch(`/api/invoices/${invoiceToDelete.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || '删除失败')
      }

      showSuccessToast('删除成功', `账单 ${invoiceToDelete.invoice_number} 已删除`)
      fetchInvoices()
      setDeleteDialogOpen(false)
      setInvoiceToDelete(null)
    } catch (error) {
      console.error('Error deleting invoice:', error)
      showErrorToast('删除失败', error instanceof Error ? error.message : '删除账单失败')
    } finally {
      setIsDeleting(false)
    }
  }

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = 
      invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      `${invoice.clients.last_name}${invoice.clients.first_name}`.includes(searchTerm) ||
      invoice.clients.phone.includes(searchTerm)
    
    const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  const getTotalPaid = (payments: Invoice['payments']) => {
    return payments.reduce((sum, payment) => sum + payment.amount, 0)
  }

  const getStats = () => {
    const total = invoices.length
    const pending = invoices.filter(inv => inv.status === 'deposit_pending').length
    const paid = invoices.filter(inv => inv.status === 'paid_in_full').length
    const totalAmount = invoices.reduce((sum, inv) => sum + inv.total_amount, 0)
    
    return { total, pending, paid, totalAmount }
  }

  const stats = getStats()

  if (loading) {
    return (
      <PageContainer>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <IconClock className="mx-auto h-8 w-8 text-gray-400 animate-spin" />
            <p className="mt-2 text-gray-500">加载中...</p>
          </div>
        </div>
      </PageContainer>
    )
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-4'>
        {/* Header */}
        <PageHeader
          title="账单管理"
          description="管理客户账单和付款状态"
          action={{
            label: "新建账单",
            onClick: handleNewInvoice,
            icon: Plus
          }}
        />

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总账单数</CardTitle>
              <IconReceipt className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">待付款</CardTitle>
              <IconClock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.pending}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">已付清</CardTitle>
              <IconCreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.paid}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总金额</CardTitle>
              <IconCreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.totalAmount)}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>账单列表</CardTitle>
            <CardDescription>查看和管理所有客户账单</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4 mb-4">
              <div className="relative flex-1">
                <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="搜索账单号、客户姓名或电话..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="筛选状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="draft">草稿</SelectItem>
                  <SelectItem value="deposit_pending">待付定金</SelectItem>
                  <SelectItem value="deposit_paid">已付定金</SelectItem>
                  <SelectItem value="paid_in_full">已付全款</SelectItem>
                  <SelectItem value="overdue">逾期</SelectItem>
                  <SelectItem value="cancelled">已取消</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Table */}
            <ResponsiveTable
              data={filteredInvoices}
              desktopTable={
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>账单号</TableHead>
                        <TableHead>客户</TableHead>
                        <TableHead>治疗日期</TableHead>
                        <TableHead>总金额</TableHead>
                        <TableHead>已付金额</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>到期日期</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredInvoices.map((invoice) => {
                        const totalPaid = getTotalPaid(invoice.payments)
                        const statusInfo = statusConfig[invoice.status]

                        return (
                          <TableRow key={invoice.id}>
                            <TableCell className="font-medium">
                              {invoice.invoice_number}
                            </TableCell>
                            <TableCell>
                              <div>
                                <div className="font-medium">
                                  {invoice.clients.last_name}{invoice.clients.first_name}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {invoice.clients.phone}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>{formatDate(invoice.treatment_date)}</TableCell>
                            <TableCell>{formatCurrency(invoice.total_amount)}</TableCell>
                            <TableCell>{formatCurrency(totalPaid)}</TableCell>
                            <TableCell>
                              <Badge className={statusInfo.color}>
                                {statusInfo.label}
                              </Badge>
                            </TableCell>
                            <TableCell>{formatDate(invoice.due_date)}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEditInvoice(invoice)}
                                  className="h-8 w-8 p-0"
                                >
                                  <IconEdit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteInvoice(invoice)}
                                  className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                >
                                  <IconTrash className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        )
                      })}
                    </TableBody>
                  </Table>
                </div>
              }
              renderMobileCard={(invoice) => {
                const totalPaid = getTotalPaid(invoice.payments)
                const statusInfo = statusConfig[invoice.status as keyof typeof statusConfig]

                return (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-lg">{invoice.invoice_number}</h3>
                      <Badge className={statusInfo.color}>
                        {statusInfo.label}
                      </Badge>
                    </div>

                    <div className="space-y-2">
                      <MobileTableField
                        label="客户"
                        value={
                          <div>
                            <div className="font-medium">
                              {invoice.clients.last_name}{invoice.clients.first_name}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {invoice.clients.phone}
                            </div>
                          </div>
                        }
                      />

                      <MobileTableField
                        label="治疗日期"
                        value={formatDate(invoice.treatment_date)}
                      />

                      <MobileTableField
                        label="总金额"
                        value={<span className="font-medium">{formatCurrency(invoice.total_amount)}</span>}
                      />

                      <MobileTableField
                        label="已付金额"
                        value={<span className="font-medium">{formatCurrency(totalPaid)}</span>}
                      />

                      <MobileTableField
                        label="到期日期"
                        value={formatDate(invoice.due_date)}
                      />

                      <MobileTableField
                        label="操作"
                        value={
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditInvoice(invoice)}
                              className="h-8"
                            >
                              <IconEdit className="h-4 w-4 mr-1" />
                              编辑
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteInvoice(invoice)}
                              className="h-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <IconTrash className="h-4 w-4 mr-1" />
                              删除
                            </Button>
                          </div>
                        }
                      />
                    </div>
                  </div>
                )
              }}
            />

            {filteredInvoices.length === 0 && (
              <div className="text-center py-8">
                <IconReceipt className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-semibold text-gray-900">暂无账单</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm || statusFilter !== 'all' ? '没有找到匹配的账单' : '开始创建第一个账单'}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Invoice Modal */}
      <InvoiceModal
        isOpen={isInvoiceModalOpen}
        onClose={() => setIsInvoiceModalOpen(false)}
        onSuccess={handleInvoiceSuccess}
        editingInvoice={editingInvoice}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除账单</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除账单 <strong>{invoiceToDelete?.invoice_number}</strong> 吗？
              <br />
              <br />
              此操作将永久删除该账单及其相关数据，无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteInvoice}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? '删除中...' : '确认删除'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </PageContainer>
  )
}
