import PageContainer from '@/components/layout/page-container';
import PageHeader from '@/components/layout/page-header';
import CRMSummaryCard from '@/components/dashboard/CRMSummaryCard';
import QuickActionsToolbar from '@/components/dashboard/QuickActionsToolbar';
import React from 'react';

export default function OverViewLayout({
  sales,
  pie_stats,
  bar_stats,
  area_stats
}: {
  sales: React.ReactNode;
  pie_stats: React.ReactNode;
  bar_stats: React.ReactNode;
  area_stats: React.ReactNode;
}) {
  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        <PageHeader
          title="医美诊所管理系统"
          emoji="👋"
          description="欢迎使用医美诊所管理系统，管理您的客户、预约和治疗项目"
        />

        {/* CRM Summary Dashboard */}
        <CRMSummaryCard />

        {/* Quick Actions Toolbar */}
        <QuickActionsToolbar />

        {/* Additional Charts and Analytics */}
        <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-7'>
          <div className='col-span-4'>{bar_stats}</div>
          <div className='col-span-4 md:col-span-3'>
            {/* sales parallel routes */}
            {sales}
          </div>
          <div className='col-span-4'>{area_stats}</div>
          <div className='col-span-4 md:col-span-3'>{pie_stats}</div>
        </div>
      </div>
    </PageContainer>
  );
}
