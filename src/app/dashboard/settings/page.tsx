'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { EnhancedInput, EnhancedTextarea, EnhancedSelect } from '@/components/ui/enhanced-input'
import { 
  Settings, 
  Building, 
  Bell, 
  Shield, 
  Database,
  Palette,
  Clock,
  DollarSign,
  Mail,
  Phone,
  MapPin,
  Save,
  RotateCcw,
  Download,
  Upload
} from 'lucide-react'
import { showSuccessToast, showErrorToast } from '@/lib/toast-utils'
import <PERSON><PERSON>ontainer from '@/components/layout/page-container'
import PageHeader from '@/components/layout/page-header'

interface SystemSettings {
  // Clinic Information
  clinic_name: string
  clinic_address: string
  clinic_phone: string
  clinic_email: string
  clinic_website: string
  clinic_description: string
  
  // Business Settings
  business_hours: {
    monday: { open: string; close: string; closed: boolean }
    tuesday: { open: string; close: string; closed: boolean }
    wednesday: { open: string; close: string; closed: boolean }
    thursday: { open: string; close: string; closed: boolean }
    friday: { open: string; close: string; closed: boolean }
    saturday: { open: string; close: string; closed: boolean }
    sunday: { open: string; close: string; closed: boolean }
  }
  appointment_duration_default: number
  appointment_buffer_time: number
  advance_booking_days: number
  
  // Financial Settings
  default_currency: string
  tax_rate: number
  deposit_percentage: number
  payment_terms_days: number
  
  // Notification Settings
  email_notifications: boolean
  sms_notifications: boolean
  appointment_reminders: boolean
  reminder_hours_before: number
  
  // System Settings
  timezone: string
  date_format: string
  time_format: string
  language: string
  
  // Security Settings
  session_timeout_minutes: number
  password_min_length: number
  require_2fa: boolean
  auto_backup_enabled: boolean
  backup_frequency_days: number
}

const defaultSettings: SystemSettings = {
  clinic_name: '美丽诊所',
  clinic_address: '',
  clinic_phone: '',
  clinic_email: '',
  clinic_website: '',
  clinic_description: '',
  business_hours: {
    monday: { open: '09:00', close: '18:00', closed: false },
    tuesday: { open: '09:00', close: '18:00', closed: false },
    wednesday: { open: '09:00', close: '18:00', closed: false },
    thursday: { open: '09:00', close: '18:00', closed: false },
    friday: { open: '09:00', close: '18:00', closed: false },
    saturday: { open: '09:00', close: '17:00', closed: false },
    sunday: { open: '10:00', close: '16:00', closed: true }
  },
  appointment_duration_default: 60,
  appointment_buffer_time: 15,
  advance_booking_days: 30,
  default_currency: 'CNY',
  tax_rate: 0,
  deposit_percentage: 30,
  payment_terms_days: 30,
  email_notifications: true,
  sms_notifications: true,
  appointment_reminders: true,
  reminder_hours_before: 24,
  timezone: 'Asia/Shanghai',
  date_format: 'YYYY-MM-DD',
  time_format: '24h',
  language: 'zh-CN',
  session_timeout_minutes: 480,
  password_min_length: 8,
  require_2fa: false,
  auto_backup_enabled: true,
  backup_frequency_days: 7
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<SystemSettings>(defaultSettings)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  const fetchSettings = async () => {
    try {
      setLoading(true)
      // Here you would fetch settings from your API
      // const response = await fetch('/api/settings')
      // const data = await response.json()
      // setSettings(data.settings)
      
      // For now, use default settings
      setSettings(defaultSettings)
    } catch (error) {
      console.error('Error fetching settings:', error)
      showErrorToast('加载失败', '无法加载系统设置')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchSettings()
  }, [])

  const handleSave = async () => {
    try {
      setSaving(true)
      
      // Here you would save settings to your API
      // const response = await fetch('/api/settings', {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(settings)
      // })
      
      setHasChanges(false)
      showSuccessToast('设置已保存', '系统设置已成功更新')
    } catch (error: any) {
      console.error('Error saving settings:', error)
      showErrorToast('保存失败', error.message)
    } finally {
      setSaving(false)
    }
  }

  const handleReset = () => {
    setSettings(defaultSettings)
    setHasChanges(false)
  }

  const updateSetting = (path: string, value: any) => {
    setSettings(prev => {
      const keys = path.split('.')
      const newSettings = { ...prev }
      let current: any = newSettings
      
      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] }
        current = current[keys[i]]
      }
      
      current[keys[keys.length - 1]] = value
      return newSettings
    })
    setHasChanges(true)
  }

  const exportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'clinic-settings.json'
    link.click()
    URL.revokeObjectURL(url)
  }

  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const importedSettings = JSON.parse(e.target?.result as string)
        setSettings({ ...defaultSettings, ...importedSettings })
        setHasChanges(true)
        showSuccessToast('导入成功', '设置已从文件导入')
      } catch (error) {
        showErrorToast('导入失败', '文件格式不正确')
      }
    }
    reader.readAsText(file)
  }

  if (loading) {
    return (
      <PageContainer>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Settings className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">加载系统设置...</p>
          </div>
        </div>
      </PageContainer>
    )
  }

  return (
    <PageContainer>
      <div className="flex flex-1 flex-col space-y-6">
        <PageHeader
          title="系统设置"
          description="配置诊所信息、业务规则和系统参数"
          emoji="⚙️"
        >
          <div className="flex gap-2">
            <Button variant="outline" onClick={exportSettings}>
              <Download className="mr-2 h-4 w-4" />
              导出设置
            </Button>
            <Button variant="outline" onClick={() => document.getElementById('import-settings')?.click()}>
              <Upload className="mr-2 h-4 w-4" />
              导入设置
            </Button>
            <input
              id="import-settings"
              type="file"
              accept=".json"
              onChange={importSettings}
              className="hidden"
            />
            <Button
              variant="outline"
              onClick={handleReset}
              disabled={!hasChanges}
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              重置
            </Button>
            <Button
              onClick={handleSave}
              disabled={!hasChanges || saving}
            >
              <Save className="mr-2 h-4 w-4" />
              {saving ? '保存中...' : '保存设置'}
            </Button>
          </div>
        </PageHeader>

        <Tabs defaultValue="clinic" className="space-y-4">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="clinic">诊所信息</TabsTrigger>
            <TabsTrigger value="business">业务设置</TabsTrigger>
            <TabsTrigger value="financial">财务设置</TabsTrigger>
            <TabsTrigger value="notifications">通知设置</TabsTrigger>
            <TabsTrigger value="system">系统设置</TabsTrigger>
            <TabsTrigger value="security">安全设置</TabsTrigger>
          </TabsList>

          <TabsContent value="clinic" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  诊所基本信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="clinic_name">诊所名称</Label>
                    <Input
                      id="clinic_name"
                      value={settings.clinic_name}
                      onChange={(e) => updateSetting('clinic_name', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="clinic_phone">联系电话</Label>
                    <Input
                      id="clinic_phone"
                      value={settings.clinic_phone}
                      onChange={(e) => updateSetting('clinic_phone', e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="clinic_email">邮箱地址</Label>
                    <Input
                      id="clinic_email"
                      type="email"
                      value={settings.clinic_email}
                      onChange={(e) => updateSetting('clinic_email', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="clinic_website">网站地址</Label>
                    <Input
                      id="clinic_website"
                      value={settings.clinic_website}
                      onChange={(e) => updateSetting('clinic_website', e.target.value)}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="clinic_address">诊所地址</Label>
                  <Input
                    id="clinic_address"
                    value={settings.clinic_address}
                    onChange={(e) => updateSetting('clinic_address', e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="clinic_description">诊所简介</Label>
                  <Textarea
                    id="clinic_description"
                    value={settings.clinic_description}
                    onChange={(e) => updateSetting('clinic_description', e.target.value)}
                    rows={4}
                    placeholder="请输入诊所简介..."
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  营业时间
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(settings.business_hours).map(([day, hours]) => {
                    const dayNames: Record<string, string> = {
                      monday: '周一',
                      tuesday: '周二',
                      wednesday: '周三',
                      thursday: '周四',
                      friday: '周五',
                      saturday: '周六',
                      sunday: '周日'
                    }

                    return (
                      <div key={day} className="flex items-center gap-4">
                        <div className="w-16">
                          <Label>{dayNames[day]}</Label>
                        </div>
                        <Switch
                          checked={!hours.closed}
                          onCheckedChange={(checked) => 
                            updateSetting(`business_hours.${day}.closed`, !checked)
                          }
                        />
                        {!hours.closed && (
                          <>
                            <Input
                              type="time"
                              value={hours.open}
                              onChange={(e) => 
                                updateSetting(`business_hours.${day}.open`, e.target.value)
                              }
                              className="w-32"
                            />
                            <span>-</span>
                            <Input
                              type="time"
                              value={hours.close}
                              onChange={(e) => 
                                updateSetting(`business_hours.${day}.close`, e.target.value)
                              }
                              className="w-32"
                            />
                          </>
                        )}
                        {hours.closed && (
                          <span className="text-muted-foreground">休息</span>
                        )}
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="business" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>预约设置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="appointment_duration">默认预约时长(分钟)</Label>
                    <Input
                      id="appointment_duration"
                      type="number"
                      value={settings.appointment_duration_default}
                      onChange={(e) => updateSetting('appointment_duration_default', parseInt(e.target.value))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="buffer_time">预约间隔时间(分钟)</Label>
                    <Input
                      id="buffer_time"
                      type="number"
                      value={settings.appointment_buffer_time}
                      onChange={(e) => updateSetting('appointment_buffer_time', parseInt(e.target.value))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="advance_booking">提前预约天数</Label>
                    <Input
                      id="advance_booking"
                      type="number"
                      value={settings.advance_booking_days}
                      onChange={(e) => updateSetting('advance_booking_days', parseInt(e.target.value))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="financial" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  财务设置
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="currency">默认货币</Label>
                    <Select
                      value={settings.default_currency}
                      onValueChange={(value) => updateSetting('default_currency', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="CNY">人民币 (CNY)</SelectItem>
                        <SelectItem value="USD">美元 (USD)</SelectItem>
                        <SelectItem value="EUR">欧元 (EUR)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="tax_rate">税率 (%)</Label>
                    <Input
                      id="tax_rate"
                      type="number"
                      step="0.01"
                      value={settings.tax_rate}
                      onChange={(e) => updateSetting('tax_rate', parseFloat(e.target.value))}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="deposit_percentage">默认定金比例 (%)</Label>
                    <Input
                      id="deposit_percentage"
                      type="number"
                      value={settings.deposit_percentage}
                      onChange={(e) => updateSetting('deposit_percentage', parseInt(e.target.value))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="payment_terms">付款期限(天)</Label>
                    <Input
                      id="payment_terms"
                      type="number"
                      value={settings.payment_terms_days}
                      onChange={(e) => updateSetting('payment_terms_days', parseInt(e.target.value))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notifications" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  通知设置
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>邮件通知</Label>
                      <p className="text-sm text-muted-foreground">接收系统邮件通知</p>
                    </div>
                    <Switch
                      checked={settings.email_notifications}
                      onCheckedChange={(checked) => updateSetting('email_notifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>短信通知</Label>
                      <p className="text-sm text-muted-foreground">接收系统短信通知</p>
                    </div>
                    <Switch
                      checked={settings.sms_notifications}
                      onCheckedChange={(checked) => updateSetting('sms_notifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>预约提醒</Label>
                      <p className="text-sm text-muted-foreground">自动发送预约提醒</p>
                    </div>
                    <Switch
                      checked={settings.appointment_reminders}
                      onCheckedChange={(checked) => updateSetting('appointment_reminders', checked)}
                    />
                  </div>
                </div>

                {settings.appointment_reminders && (
                  <div>
                    <Label htmlFor="reminder_hours">提前提醒时间(小时)</Label>
                    <Input
                      id="reminder_hours"
                      type="number"
                      value={settings.reminder_hours_before}
                      onChange={(e) => updateSetting('reminder_hours_before', parseInt(e.target.value))}
                      className="w-32"
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="system" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>系统配置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="timezone">时区</Label>
                    <Select
                      value={settings.timezone}
                      onValueChange={(value) => updateSetting('timezone', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Asia/Shanghai">中国标准时间</SelectItem>
                        <SelectItem value="Asia/Hong_Kong">香港时间</SelectItem>
                        <SelectItem value="Asia/Taipei">台北时间</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="language">系统语言</Label>
                    <Select
                      value={settings.language}
                      onValueChange={(value) => updateSetting('language', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="zh-CN">中文简体</SelectItem>
                        <SelectItem value="zh-TW">中文繁体</SelectItem>
                        <SelectItem value="en-US">English</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="date_format">日期格式</Label>
                    <Select
                      value={settings.date_format}
                      onValueChange={(value) => updateSetting('date_format', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="YYYY-MM-DD">2023-12-31</SelectItem>
                        <SelectItem value="DD/MM/YYYY">31/12/2023</SelectItem>
                        <SelectItem value="MM/DD/YYYY">12/31/2023</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="time_format">时间格式</Label>
                    <Select
                      value={settings.time_format}
                      onValueChange={(value) => updateSetting('time_format', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="24h">24小时制</SelectItem>
                        <SelectItem value="12h">12小时制</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  安全设置
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="session_timeout">会话超时(分钟)</Label>
                    <Input
                      id="session_timeout"
                      type="number"
                      value={settings.session_timeout_minutes}
                      onChange={(e) => updateSetting('session_timeout_minutes', parseInt(e.target.value))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="password_length">密码最小长度</Label>
                    <Input
                      id="password_length"
                      type="number"
                      value={settings.password_min_length}
                      onChange={(e) => updateSetting('password_min_length', parseInt(e.target.value))}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>启用双因素认证</Label>
                      <p className="text-sm text-muted-foreground">增强账户安全性</p>
                    </div>
                    <Switch
                      checked={settings.require_2fa}
                      onCheckedChange={(checked) => updateSetting('require_2fa', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>自动备份</Label>
                      <p className="text-sm text-muted-foreground">定期自动备份数据</p>
                    </div>
                    <Switch
                      checked={settings.auto_backup_enabled}
                      onCheckedChange={(checked) => updateSetting('auto_backup_enabled', checked)}
                    />
                  </div>
                </div>

                {settings.auto_backup_enabled && (
                  <div>
                    <Label htmlFor="backup_frequency">备份频率(天)</Label>
                    <Input
                      id="backup_frequency"
                      type="number"
                      value={settings.backup_frequency_days}
                      onChange={(e) => updateSetting('backup_frequency_days', parseInt(e.target.value))}
                      className="w-32"
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageContainer>
  )
}
