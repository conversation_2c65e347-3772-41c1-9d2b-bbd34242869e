import { NextRequest, NextResponse } from 'next/server'
import { clientQueries } from '@/lib/supabase/queries'
import { createApiHandler, createSuccessResponse, apiErrors } from '@/lib/api-handler'
import { clientSchemas, commonSchemas } from '@/lib/validation'
import { z } from 'zod'

// Define query schema for GET requests
const getQuerySchema = z.object({
  q: z.string().optional(),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
})

// Enhanced client creation schema with address fields
const createClientSchema = z.object({
  first_name: z.string().min(1, '姓名不能为空').max(50, '姓名过长'),
  last_name: z.string().min(1, '姓名不能为空').max(50, '姓名过长'),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/, '无效的电话号码格式'),
  email: z.string().email('无效的邮箱格式').optional(),
  date_of_birth: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, '日期格式必须为 YYYY-MM-DD').optional(),
  address_line_1: z.string().max(200, '地址过长').optional(),
  address_line_2: z.string().max(200, '地址过长').optional(),
  city: z.string().max(100, '城市名过长').optional(),
  state_province: z.string().max(100, '省份名过长').optional(),
  postal_code: z.string().max(20, '邮编过长').optional(),
  country: z.string().max(100, '国家名过长').optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  emergency_contact_name: z.string().max(100, '紧急联系人姓名过长').optional(),
  emergency_contact_phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/, '无效的电话号码格式').optional(),
  referral_source: z.string().max(100, '推荐来源过长').optional(),
  notes: z.string().max(1000, '备注过长').optional(),
  preferred_language: z.string().max(10, '语言代码过长').optional(),
})

// Create API handler with validation
const handler = createApiHandler({
  querySchema: getQuerySchema,
  bodySchema: createClientSchema,
})({
  GET: async (request, { query }) => {
    const { q, page, limit } = query!

    let clients
    if (q) {
      clients = await clientQueries.search(q)
    } else {
      clients = await clientQueries.getAll()
    }

    // TODO: Implement proper pagination at database level
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedClients = clients.slice(startIndex, endIndex)

    return createSuccessResponse({
      clients: paginatedClients,
      pagination: {
        page,
        limit,
        total: clients.length,
        totalPages: Math.ceil(clients.length / limit),
      },
    })
  },

  POST: async (request, { body }) => {
    const clientData = body!

    const client = await clientQueries.create({
      first_name: clientData.first_name,
      last_name: clientData.last_name,
      phone: clientData.phone,
      email: clientData.email || null,
      date_of_birth: clientData.date_of_birth || null,
      address_line_1: clientData.address_line_1 || null,
      address_line_2: clientData.address_line_2 || null,
      city: clientData.city || null,
      state_province: clientData.state_province || null,
      postal_code: clientData.postal_code || null,
      country: clientData.country || '美国',
      latitude: clientData.latitude || null,
      longitude: clientData.longitude || null,
      emergency_contact_name: clientData.emergency_contact_name || null,
      emergency_contact_phone: clientData.emergency_contact_phone || null,
      notes: clientData.notes || null,
      referral_source: clientData.referral_source || null,
      preferred_language: clientData.preferred_language || 'zh-CN'
    })

    return createSuccessResponse({ client }, 201, '客户创建成功')
  },
})

// Export the handler functions
export const GET = handler
export const POST = handler
