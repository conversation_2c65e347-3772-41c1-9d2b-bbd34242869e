import { NextRequest, NextResponse } from 'next/server'
import { clientQueries } from '@/lib/supabase/queries'

// GET /api/clients/[id] - Get client by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const client = await clientQueries.getById(id)
    return NextResponse.json({ client })
  } catch (error) {
    console.error('Error fetching client:', error)
    return NextResponse.json(
      { error: '客户不存在' },
      { status: 404 }
    )
  }
}

// PUT /api/clients/[id] - Update client
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const body = await request.json()

    const client = await clientQueries.update(id, {
      first_name: body.first_name,
      last_name: body.last_name,
      phone: body.phone,
      email: body.email || null,
      date_of_birth: body.date_of_birth || null,
      address_line_1: body.address_line_1 || null,
      address_line_2: body.address_line_2 || null,
      city: body.city || null,
      state_province: body.state_province || null,
      postal_code: body.postal_code || null,
      country: body.country || '美国',
      latitude: body.latitude || null,
      longitude: body.longitude || null,
      emergency_contact_name: body.emergency_contact_name || null,
      emergency_contact_phone: body.emergency_contact_phone || null,
      notes: body.notes || null,
      status: body.status,
      referral_source: body.referral_source || null,
      preferred_language: body.preferred_language || 'zh-CN'
    })

    return NextResponse.json({ client })
  } catch (error: any) {
    console.error('Error updating client:', error)
    
    // Handle unique constraint violation (duplicate phone)
    if (error.code === '23505' && error.constraint === 'clients_phone_key') {
      return NextResponse.json(
        { error: '该电话号码已存在' },
        { status: 409 }
      )
    }

    return NextResponse.json(
      { error: '更新客户信息失败' },
      { status: 500 }
    )
  }
}

// DELETE /api/clients/[id] - Delete client
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    await clientQueries.delete(id)
    return NextResponse.json({ message: '客户删除成功' })
  } catch (error) {
    console.error('Error deleting client:', error)
    return NextResponse.json(
      { error: '删除客户失败' },
      { status: 500 }
    )
  }
}
