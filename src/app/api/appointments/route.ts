import { NextRequest, NextResponse } from 'next/server'
import { appointmentQueries, businessLogic } from '@/lib/supabase/queries'

// GET /api/appointments - Get appointments by date range
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')
    const clientId = searchParams.get('client_id')

    if (clientId) {
      const appointments = await appointmentQueries.getByClientId(clientId)
      return NextResponse.json({ appointments })
    }

    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: '需要提供开始日期和结束日期' },
        { status: 400 }
      )
    }

    const appointments = await appointmentQueries.getByDateRange(startDate, endDate)
    return NextResponse.json({ appointments })
  } catch (error) {
    console.error('Error fetching appointments:', error)
    return NextResponse.json(
      { error: '获取预约信息失败' },
      { status: 500 }
    )
  }
}

// POST /api/appointments - Create new appointment
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.client_id || !body.treatment_id || !body.appointment_date || !body.start_time || !body.end_time) {
      return NextResponse.json(
        { error: '客户、治疗项目、日期和时间为必填项' },
        { status: 400 }
      )
    }

    // Check for appointment conflicts
    const hasConflict = await businessLogic.checkAppointmentConflict(
      body.appointment_date,
      body.start_time,
      body.end_time
    )

    if (hasConflict) {
      return NextResponse.json(
        { error: '该时间段已有预约，请选择其他时间' },
        { status: 409 }
      )
    }

    const appointment = await appointmentQueries.create({
      client_id: body.client_id,
      treatment_id: body.treatment_id,
      appointment_date: body.appointment_date,
      start_time: body.start_time,
      end_time: body.end_time,
      appointment_type: body.appointment_type || 'treatment',
      notes: body.notes || null,
      staff_notes: body.staff_notes || null,
      custom_price: body.custom_price || null
    })

    // If this is a consultation appointment, create consultation invoice
    if (body.appointment_type === 'consultation') {
      try {
        await businessLogic.createConsultationInvoice(appointment)
      } catch (error) {
        console.error('Error creating consultation invoice:', error)
        // Don't fail the appointment creation if invoice creation fails
      }
    }

    // If this is a treatment appointment, check for consultation fee waiver
    if (body.appointment_type === 'treatment') {
      try {
        await businessLogic.processConsultationFeeWaiver(body.client_id, body.appointment_date)
      } catch (error) {
        console.error('Error processing consultation fee waiver:', error)
        // Don't fail the appointment creation if waiver processing fails
      }
    }

    return NextResponse.json({ appointment }, { status: 201 })
  } catch (error) {
    console.error('Error creating appointment:', error)
    return NextResponse.json(
      { error: '创建预约失败' },
      { status: 500 }
    )
  }
}
