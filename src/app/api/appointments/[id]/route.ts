import { NextRequest, NextResponse } from 'next/server'
import { appointmentQueries, businessLogic } from '@/lib/supabase/queries'

// GET /api/appointments/[id] - Get appointment by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const appointment = await appointmentQueries.getById(id)
    return NextResponse.json({ appointment })
  } catch (error) {
    console.error('Error fetching appointment:', error)
    return NextResponse.json(
      { error: '预约不存在' },
      { status: 404 }
    )
  }
}

// PUT /api/appointments/[id] - Update appointment
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.client_id || !body.treatment_id || !body.appointment_date || !body.start_time || !body.end_time) {
      return NextResponse.json(
        { error: '客户、治疗项目、日期和时间为必填项' },
        { status: 400 }
      )
    }

    // Check for appointment conflicts (excluding current appointment)
    const hasConflict = await businessLogic.checkAppointmentConflict(
      body.appointment_date,
      body.start_time,
      body.end_time,
      id // Exclude current appointment from conflict check
    )

    if (hasConflict) {
      return NextResponse.json(
        { error: '该时间段已有预约，请选择其他时间' },
        { status: 409 }
      )
    }

    // Prepare update data
    const updateData: any = {}
    if (body.client_id) updateData.client_id = body.client_id
    if (body.treatment_id) updateData.treatment_id = body.treatment_id
    if (body.appointment_date) updateData.appointment_date = body.appointment_date
    if (body.start_time) updateData.start_time = body.start_time
    if (body.end_time) updateData.end_time = body.end_time
    if (body.appointment_type) updateData.appointment_type = body.appointment_type
    if (body.status) updateData.status = body.status
    if (body.notes !== undefined) updateData.notes = body.notes
    if (body.staff_notes !== undefined) updateData.staff_notes = body.staff_notes
    if (body.custom_price !== undefined) updateData.custom_price = body.custom_price

    // Get the original appointment to check for type changes
    const originalAppointment = await appointmentQueries.getById(id)

    const appointment = await appointmentQueries.update(id, updateData)

    // Handle consultation fee logic if appointment type changed
    if (body.appointment_type && originalAppointment.appointment_type !== body.appointment_type) {
      if (body.appointment_type === 'consultation') {
        // Create consultation invoice for new consultation appointment
        try {
          await businessLogic.createConsultationInvoice(appointment)
        } catch (error) {
          console.error('Error creating consultation invoice:', error)
        }
      } else if (body.appointment_type === 'treatment' && originalAppointment.appointment_type === 'consultation') {
        // Process consultation fee waiver when changing from consultation to treatment
        try {
          await businessLogic.processConsultationFeeWaiver(appointment.client_id, appointment.appointment_date)
        } catch (error) {
          console.error('Error processing consultation fee waiver:', error)
        }
      }
    }

    return NextResponse.json({ appointment })
  } catch (error) {
    console.error('Error updating appointment:', error)
    return NextResponse.json(
      { error: '更新预约失败' },
      { status: 500 }
    )
  }
}

// DELETE /api/appointments/[id] - Delete appointment
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    await appointmentQueries.delete(id)
    return NextResponse.json({ message: '预约已删除' })
  } catch (error) {
    console.error('Error deleting appointment:', error)
    return NextResponse.json(
      { error: '删除预约失败' },
      { status: 500 }
    )
  }
}
