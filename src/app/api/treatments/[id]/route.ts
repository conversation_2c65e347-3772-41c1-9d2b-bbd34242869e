import { NextRequest, NextResponse } from 'next/server'
import { treatmentQueries } from '@/lib/supabase/queries'

// GET /api/treatments/[id] - Get treatment by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const treatment = await treatmentQueries.getById(id)
    return NextResponse.json({ treatment })
  } catch (error) {
    console.error('Error fetching treatment:', error)
    return NextResponse.json(
      { error: '治疗项目不存在' },
      { status: 404 }
    )
  }
}

// PUT /api/treatments/[id] - Update treatment
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const body = await request.json()

    const treatment = await treatmentQueries.update(id, {
      name: body.name,
      name_chinese: body.name_chinese,
      description: body.description || null,
      description_chinese: body.description_chinese || null,
      default_price: body.default_price ? parseFloat(body.default_price) : undefined,
      fixed_deposit_amount: body.fixed_deposit_amount !== undefined ? parseFloat(body.fixed_deposit_amount) : undefined,
      consultation_fee: body.consultation_fee !== undefined ? parseFloat(body.consultation_fee) : undefined,
      duration_minutes: body.duration_minutes ? parseInt(body.duration_minutes) : undefined,
      category: body.category,
      is_active: body.is_active,
      requires_consultation: body.requires_consultation
    })

    return NextResponse.json({ treatment })
  } catch (error) {
    console.error('Error updating treatment:', error)
    return NextResponse.json(
      { error: '更新治疗项目失败' },
      { status: 500 }
    )
  }
}

// DELETE /api/treatments/[id] - Delete treatment
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    await treatmentQueries.delete(id)
    return NextResponse.json({ message: '治疗项目删除成功' })
  } catch (error) {
    console.error('Error deleting treatment:', error)
    return NextResponse.json(
      { error: '删除治疗项目失败' },
      { status: 500 }
    )
  }
}
