/* 响应式日历样式 */

/* 移动端优化 */
@media (max-width: 768px) {
  .rbc-calendar {
    font-size: 12px;
  }

  .rbc-toolbar {
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
  }

  .rbc-toolbar-label {
    font-size: 16px;
    font-weight: 600;
    order: -1;
  }

  .rbc-btn-group {
    display: flex;
    gap: 4px;
  }

  .rbc-btn-group button {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
  }

  /* 月视图优化 */
  .rbc-month-view {
    border: none;
  }

  .rbc-month-header {
    border-bottom: 1px solid hsl(var(--border));
  }

  .rbc-header {
    padding: 8px 4px;
    font-size: 11px;
    font-weight: 600;
    text-align: center;
    border-bottom: 1px solid hsl(var(--border));
    background: hsl(var(--muted));
  }

  .rbc-date-cell {
    padding: 4px;
    text-align: right;
  }

  .rbc-date-cell>a {
    font-size: 11px;
    font-weight: 500;
  }

  .rbc-month-row {
    border-bottom: 1px solid hsl(var(--border));
    min-height: 60px;
  }

  .rbc-day-bg {
    border-right: 1px solid hsl(var(--border));
  }

  .rbc-event {
    padding: 2px 4px;
    font-size: 10px;
    border-radius: 3px;
    margin: 1px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 周视图和日视图优化 */
  .rbc-time-view {
    border: 1px solid hsl(var(--border));
    border-radius: 6px;
  }

  .rbc-time-header {
    border-bottom: 1px solid hsl(var(--border));
  }

  .rbc-time-content {
    border-top: none;
  }

  .rbc-timeslot-group {
    border-bottom: 1px solid hsl(var(--border));
  }

  .rbc-time-slot {
    border-top: 1px solid hsl(var(--border));
  }

  .rbc-day-slot {
    border-right: 1px solid hsl(var(--border));
  }

  .rbc-current-time-indicator {
    background-color: hsl(var(--primary));
    height: 2px;
  }
}

/* 平板端优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .rbc-calendar {
    font-size: 13px;
  }

  .rbc-event {
    padding: 3px 6px;
    font-size: 11px;
  }

  .rbc-month-row {
    min-height: 80px;
  }
}

/* 桌面端优化 */
@media (min-width: 1025px) {
  .rbc-calendar {
    font-size: 14px;
  }

  .rbc-month-row {
    min-height: 100px;
  }

  .rbc-event {
    padding: 4px 8px;
    font-size: 12px;
  }
}

/* 通用样式优化 */
.rbc-calendar {
  background: hsl(var(--background));
  border: none;
  border-radius: 0;
  overflow: hidden;
  font-family: inherit;
}

/* 日历容器样式 */
.calendar-container {
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
  overflow: hidden;
  background: hsl(var(--background));
}

.rbc-toolbar {
  padding: 16px;
  background: hsl(var(--muted) / 0.5);
  border-bottom: 1px solid hsl(var(--border));
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rbc-toolbar button {
  background: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

.rbc-toolbar button:hover {
  background: hsl(var(--muted));
  border-color: hsl(var(--primary));
  transform: translateY(-1px);
}

.rbc-toolbar button:focus,
.rbc-toolbar button.rbc-active {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border-color: hsl(var(--primary));
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rbc-toolbar-label {
  font-size: 16px;
  font-weight: 600;
  color: hsl(var(--foreground));
  margin: 0 16px;
}

.rbc-btn-group {
  display: flex;
  gap: 4px;
}

.rbc-event {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.rbc-event:hover {
  background: hsl(var(--primary) / 0.9);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rbc-event.rbc-selected {
  background: hsl(var(--primary) / 0.8);
}

.rbc-show-more {
  background: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
  border: 1px solid hsl(var(--border));
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 11px;
  cursor: pointer;
}

.rbc-show-more:hover {
  background: hsl(var(--muted) / 0.8);
}

/* 今天的高亮 */
.rbc-today {
  background: hsl(var(--primary) / 0.1);
}

.rbc-off-range-bg {
  background: hsl(var(--muted) / 0.3);
}

/* 选中的时间槽 */
.rbc-slot-selection {
  background: hsl(var(--primary) / 0.2);
  border: 2px solid hsl(var(--primary));
}

/* 加载状态优化 */
.rbc-calendar.loading {
  opacity: 0.6;
  pointer-events: none;
}