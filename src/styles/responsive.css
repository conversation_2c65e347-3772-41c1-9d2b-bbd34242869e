/* 响应式设计改进 */

/* 移动端优化 */
@media (max-width: 768px) {
  /* 页面容器 */
  .page-container {
    padding: 1rem;
  }
  
  /* 卡片间距 */
  .card-grid {
    gap: 1rem;
  }
  
  /* 按钮组 */
  .button-group {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .button-group > * {
    width: 100%;
  }
  
  /* 表格响应式 */
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .table-responsive table {
    min-width: 600px;
  }
  
  /* 模态框 */
  .modal-content {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
    overflow-y: auto;
  }
  
  /* 侧边栏 */
  .sidebar-content {
    padding: 1rem;
  }
  
  /* 表单 */
  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  /* 统计卡片 */
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* 平板端优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .card-grid {
    gap: 1.5rem;
  }
  
  .form-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  .button-group {
    flex-direction: row;
    gap: 0.75rem;
  }
}

/* 桌面端优化 */
@media (min-width: 1025px) {
  .card-grid {
    gap: 2rem;
  }
  
  .form-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }
  
  .button-group {
    flex-direction: row;
    gap: 1rem;
  }
}

/* 通用响应式工具类 */
.responsive-text {
  font-size: 0.875rem;
}

@media (min-width: 640px) {
  .responsive-text {
    font-size: 1rem;
  }
}

@media (min-width: 1024px) {
  .responsive-text {
    font-size: 1.125rem;
  }
}

/* 响应式间距 */
.responsive-padding {
  padding: 1rem;
}

@media (min-width: 640px) {
  .responsive-padding {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-padding {
    padding: 2rem;
  }
}

/* 响应式网格 */
.responsive-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1280px) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 专业化改进 */
.professional-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border) / 0.5);
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
}

.professional-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.professional-button {
  transition: all 0.2s ease-in-out;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.professional-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
}

.professional-input {
  transition: all 0.2s ease-in-out;
  border: 1px solid hsl(var(--border));
}

.professional-input:focus {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
}

/* 加载状态改进 */
.loading-skeleton {
  background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--muted) / 0.5) 50%, hsl(var(--muted)) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-indicator::before {
  content: '';
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: currentColor;
}

/* 专业化动画 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 焦点管理 */
.focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* 无障碍改进 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
  
  .print-avoid-break {
    page-break-inside: avoid;
  }
}
