/**
 * Mobile-First Responsive Design System
 * Professional responsive CSS with mobile-first approach for medical aesthetics CRM
 */

/* ==========================================================================
   RESPONSIVE BREAKPOINTS
   ========================================================================== */

:root {
  /* Breakpoint Variables */
  --breakpoint-xs: 320px;   /* Small phones */
  --breakpoint-sm: 640px;   /* Large phones */
  --breakpoint-md: 768px;   /* Tablets */
  --breakpoint-lg: 1024px;  /* Small laptops */
  --breakpoint-xl: 1280px;  /* Large laptops */
  --breakpoint-2xl: 1536px; /* Desktop monitors */

  /* Touch Target Sizes */
  --touch-target-min: 44px;
  --touch-target-comfortable: 48px;
  --touch-target-large: 56px;

  /* Mobile-Optimized Spacing */
  --mobile-padding: 1rem;
  --mobile-margin: 0.75rem;
  --mobile-gap: 0.5rem;

  /* Tablet-Optimized Spacing */
  --tablet-padding: 1.5rem;
  --tablet-margin: 1rem;
  --tablet-gap: 0.75rem;

  /* Desktop-Optimized Spacing */
  --desktop-padding: 2rem;
  --desktop-margin: 1.5rem;
  --desktop-gap: 1rem;

  /* Mobile Typography Scale */
  --mobile-text-xs: 0.75rem;
  --mobile-text-sm: 0.875rem;
  --mobile-text-base: 1rem;
  --mobile-text-lg: 1.125rem;
  --mobile-text-xl: 1.25rem;
  --mobile-text-2xl: 1.5rem;
  --mobile-text-3xl: 1.875rem;

  /* Desktop Typography Scale */
  --desktop-text-xs: 0.75rem;
  --desktop-text-sm: 0.875rem;
  --desktop-text-base: 1rem;
  --desktop-text-lg: 1.125rem;
  --desktop-text-xl: 1.25rem;
  --desktop-text-2xl: 1.5rem;
  --desktop-text-3xl: 1.875rem;
  --desktop-text-4xl: 2.25rem;
  --desktop-text-5xl: 3rem;
}

/* ==========================================================================
   MOBILE-FIRST BASE STYLES
   ========================================================================== */

/* Mobile-first container */
.container-responsive {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--mobile-padding);
  padding-right: var(--mobile-padding);
}

/* Legacy support for existing classes */
.page-container {
  padding: var(--mobile-padding);
}

.card-grid {
  gap: var(--mobile-gap);
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: var(--mobile-gap);
}

.button-group > * {
  width: 100%;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--mobile-gap);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--mobile-gap);
}

/* Responsive container sizes */
@media (min-width: 640px) {
  .container-responsive {
    max-width: 640px;
    padding-left: var(--tablet-padding);
    padding-right: var(--tablet-padding);
  }
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: 768px;
  }

  .card-grid {
    gap: var(--tablet-gap);
  }

  .form-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--tablet-gap);
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--tablet-gap);
  }

  .button-group {
    flex-direction: row;
    gap: var(--tablet-gap);
  }

  .button-group > * {
    width: auto;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    max-width: 1024px;
    padding-left: var(--desktop-padding);
    padding-right: var(--desktop-padding);
  }

  .card-grid {
    gap: var(--desktop-gap);
  }

  .form-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--desktop-gap);
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--desktop-gap);
  }

  .button-group {
    gap: var(--desktop-gap);
  }
}

@media (min-width: 1280px) {
  .container-responsive {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container-responsive {
    max-width: 1536px;
  }
}

/* ==========================================================================
   TOUCH-OPTIMIZED COMPONENTS
   ========================================================================== */

/* Touch-friendly buttons */
.btn-touch {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-size: var(--mobile-text-base);
  font-weight: 500;
  line-height: 1.25;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  /* Ensure text is readable */
  text-align: center;
  vertical-align: middle;

  /* Prevent text selection on touch */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* Improve touch response */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

/* Larger touch targets for primary actions */
.btn-touch-large {
  min-height: var(--touch-target-large);
  padding: 1rem 1.5rem;
  font-size: var(--mobile-text-lg);
}

/* Touch-friendly form inputs */
.input-touch {
  min-height: var(--touch-target-min);
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-size: var(--mobile-text-base);
  line-height: 1.25;

  /* Improve mobile input experience */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* ==========================================================================
   MOBILE NAVIGATION PATTERNS
   ========================================================================== */

/* Mobile-first navigation */
.nav-mobile {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 0.5rem;
  z-index: 50;

  /* Safe area for devices with home indicator */
  padding-bottom: calc(0.5rem + env(safe-area-inset-bottom));
}

.nav-mobile-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: var(--touch-target-min);
  padding: 0.25rem;
  border-radius: 0.5rem;
  font-size: var(--mobile-text-xs);
  font-weight: 500;
  color: #6b7280;
  text-decoration: none;
  transition: all 0.2s ease-in-out;

  /* Improve touch response */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.nav-mobile-item:hover,
.nav-mobile-item.active {
  color: #3b82f6;
  background-color: #eff6ff;
}

/* Desktop navigation */
@media (min-width: 1024px) {
  .nav-mobile {
    position: static;
    background: transparent;
    border: none;
    padding: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    gap: 1rem;
  }

  .nav-mobile-item {
    flex-direction: row;
    font-size: var(--desktop-text-sm);
    padding: 0.5rem 1rem;
  }
}

/* 通用响应式工具类 */
.responsive-text {
  font-size: 0.875rem;
}

@media (min-width: 640px) {
  .responsive-text {
    font-size: 1rem;
  }
}

@media (min-width: 1024px) {
  .responsive-text {
    font-size: 1.125rem;
  }
}

/* 响应式间距 */
.responsive-padding {
  padding: 1rem;
}

@media (min-width: 640px) {
  .responsive-padding {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-padding {
    padding: 2rem;
  }
}

/* 响应式网格 */
.responsive-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1280px) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 专业化改进 */
.professional-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border) / 0.5);
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
}

.professional-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.professional-button {
  transition: all 0.2s ease-in-out;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.professional-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
}

.professional-input {
  transition: all 0.2s ease-in-out;
  border: 1px solid hsl(var(--border));
}

.professional-input:focus {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
}

/* 加载状态改进 */
.loading-skeleton {
  background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--muted) / 0.5) 50%, hsl(var(--muted)) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-indicator::before {
  content: '';
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: currentColor;
}

/* 专业化动画 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 焦点管理 */
.focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* 无障碍改进 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
  
  .print-avoid-break {
    page-break-inside: avoid;
  }
}
