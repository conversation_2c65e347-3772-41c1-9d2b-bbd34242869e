import { NavItem } from '@/types';

export type Product = {
  photo_url: string;
  name: string;
  description: string;
  created_at: string;
  price: number;
  id: number;
  category: string;
  updated_at: string;
};

//Info: The following data is used for the sidebar navigation and Cmd K bar.
export const navItems: NavItem[] = [
  {
    title: '概览',
    url: '/dashboard/overview',
    icon: 'dashboard',
    isActive: false,
    shortcut: ['d', 'd'],
    items: [] // Empty array as there are no child items for Dashboard
  },
  {
    title: '预约日历',
    url: '/dashboard/calendar',
    icon: 'calendar',
    shortcut: ['c', 'c'],
    isActive: false,
    items: [] // No child items
  },
  {
    title: '客户管理',
    url: '/dashboard/clients',
    icon: 'users',
    shortcut: ['u', 'u'],
    isActive: false,
    items: [] // No child items
  },
  {
    title: '治疗项目',
    url: '/dashboard/treatments',
    icon: 'product',
    shortcut: ['t', 't'],
    isActive: false,
    items: [] // No child items
  },
  {
    title: '账单管理',
    url: '#', // Placeholder as there is no direct link for the parent
    icon: 'billing',
    isActive: true,
    items: [
      {
        title: '账单列表',
        url: '/dashboard/invoices',
        icon: 'receipt',
        shortcut: ['i', 'i']
      },
      {
        title: '付款记录',
        url: '/dashboard/payments',
        icon: 'creditCard',
        shortcut: ['p', 'p']
      }
    ]
  },
  {
    title: '系统设置',
    url: '#', // Placeholder as there is no direct link for the parent
    icon: 'settings',
    isActive: false,
    items: [
      {
        title: '工作时间',
        url: '/dashboard/settings/working-hours',
        icon: 'clock',
        shortcut: ['w', 'h']
      },
      {
        title: '预约类型',
        url: '/dashboard/settings/appointment-types',
        icon: 'calendar',
        shortcut: ['a', 't']
      },
      {
        title: '治疗分类',
        url: '/dashboard/settings/treatment-categories',
        icon: 'product',
        shortcut: ['t', 'c']
      },
      {
        title: '个人资料',
        url: '/dashboard/profile',
        icon: 'userPen',
        shortcut: ['m', 'm']
      },
      {
        title: '退出登录',
        shortcut: ['l', 'l'],
        url: '/',
        icon: 'login'
      }
    ]
  }
];

export interface SaleUser {
  id: number;
  name: string;
  email: string;
  amount: string;
  image: string;
  initials: string;
}

export const recentSalesData: SaleUser[] = [
  {
    id: 1,
    name: 'Olivia Martin',
    email: '<EMAIL>',
    amount: '+$1,999.00',
    image: 'https://api.slingacademy.com/public/sample-users/1.png',
    initials: 'OM'
  },
  {
    id: 2,
    name: 'Jackson Lee',
    email: '<EMAIL>',
    amount: '+$39.00',
    image: 'https://api.slingacademy.com/public/sample-users/2.png',
    initials: 'JL'
  },
  {
    id: 3,
    name: 'Isabella Nguyen',
    email: '<EMAIL>',
    amount: '+$299.00',
    image: 'https://api.slingacademy.com/public/sample-users/3.png',
    initials: 'IN'
  },
  {
    id: 4,
    name: 'William Kim',
    email: '<EMAIL>',
    amount: '+$99.00',
    image: 'https://api.slingacademy.com/public/sample-users/4.png',
    initials: 'WK'
  },
  {
    id: 5,
    name: 'Sofia Davis',
    email: '<EMAIL>',
    amount: '+$39.00',
    image: 'https://api.slingacademy.com/public/sample-users/5.png',
    initials: 'SD'
  }
];
